package com.emc.service.impl;

import com.emc.constants.EmcConstants;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class CochannelServiceImplTest {

    @Test
    void testCochaninf() {
        // Setup
        CochannelServiceImpl cochannelService = new CochannelServiceImpl();
        
        // Execute
        int result = cochannelService.cochaninf();
        
        // Verify
        assertEquals(EmcConstants.OK, result);
    }
}
