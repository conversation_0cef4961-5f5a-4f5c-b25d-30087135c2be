/**********************************************************************/
/*                                                                    */
/*    Module Name   :  main (esemctmr.pc)                             */
/*    Date Written  :  July, 1993                                     */
/*    Author        :  C. <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  main (esemep0f.c)                              */  
/*                                                                    */
/*    Parameters    :  error message                                  */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Prints traffic loading readings for all MRS    */
/*                     channels in the order of measurement date or   */
/*                     frequency depending on whether user supplies   */
/*                     '-d' or '-f' flag tp the program.              */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Jul-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>

//20180627 Cyrus add
#include <time.h>
#include <unistd.h>
#include <curses.h>
#include "../include/define.h"
#include "../include/emc.h"







#ifndef ERROR
#define ERROR    1
#endif

#ifndef OK
#define OK       0
#endif

#ifndef LOGIN_OK
#define LOGIN_OK    0
#endif

#ifndef NOT_FOUND
#define NOT_FOUND   1403
#endif

#define  MAX_RPT_LINES       57
#define  HEADLINES           4
#define  COLUMN_HEADLINES    6

/* report sorting order */
#define  BY_FREQ        0
#define  BY_DATE        1

/*   strip blank mode   */
#define  TRAIL          0
#define  LEADING        1


int get_sys_date_time(char *,char *,char *,char *);	                /*20180627 Cyrus Add */
int user_login    (char *,char *,char *);                           /*20180627 Cyrus Add */
int strip_blank   (char *,char *);									/*20180627 Cyrus Add */
void print_rpt_head (FILE *, int, int);                               /*20180627 Cyrus Add */
void print_by_date_rpt_col (FILE *);                                 /*20180627 Cyrus Add */


char    user_id[20];        /* user login name       */
char	sys_date[9];        /* system date           */
char	sys_time[9];        /* system time           */
char	yymmdd[7];          /* system date in yymmdd */
char	hhmmss[7];          /* system time in hhmmss */
char	printer_id[15];     /* destinated printer id */
char    err_msg[133];

int     items_per_line;

char       *getenv();
struct tm  *get_date_time();


EXEC SQL BEGIN DECLARE SECTION;

    double   o_freq;
    float    o_traffic;
    VARCHAR  o_date[9];
    char     select1[256];

EXEC SQL END DECLARE SECTION;

EXEC SQL INCLUDE SQLCA;


int main(argc, argv)
int    argc;
char   **argv;
{
    char   *rpt_dir;
    char   rpt_line[133];
    char   sub_rpt_line[45];
    char   fname[120];
    char   order_by_str[35];
    char   cmdline[100];
    char   prev_date[3];
    FILE   *rfp;
    int    sort_order;
    int    line_cnt = 0;
    int    page_cnt = 1;
    int    item_cnt = 0;


    EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn;

    if (argc != 6)
    {
        printf("Fatal error: invalid no. of arguments passed to %s\n", argv[0]);
        printf("Usage: esemctmr username password [-f or -d] -P printer_id\n");
        exit(1);
    }

    if (strcmp(argv[3], "-f") && strcmp(argv[3], "-d"))
    {
        printf("Fatal error: invalid no. of arguments passed to %s\n", argv[0]);
        printf("Usage: esemctmr username password [-f or -d] -P printer_id\n");
        exit(1);
    }

    if (strcmp(argv[4], "-P"))
    {
        printf("Fatal error: invalid no. of arguments passed to %s\n", argv[0]);
        printf("Usage: esemctmr username password [-f or -d] -P printer_id\n");
        exit(1);
    }

    strcpy(user_id, argv[1]);
    rpt_dir = getenv("REPORT_DIR");
    sprintf(fname, "%s/%s.%s", rpt_dir, argv[0], user_id);
    if((rfp = fopen(fname, "w")) == (FILE *) NULL)
    {
        printf("Fail to open %s, press any key to exit", fname);
        getchar();
        goto force_exit;
    }

    if (user_login(user_id, argv[2], err_msg) != LOGIN_OK)
    {
        strip_blank(err_msg, TRAIL);
        strcat(err_msg, "  ");
        strcat(err_msg, argv[1]);
        strcat(err_msg, argv[2]);
        fprintf(rfp, "%s\n", err_msg);
        fclose(rfp);
        goto force_exit;
    }

    if (argv[3][1] == 'f')
    {
        strcpy(order_by_str, "TX_FREQ");
        sort_order = BY_FREQ;
        items_per_line = 3;
    }
    else
    {
        strcpy(order_by_str, "LAST_UPDATE, TX_FREQ");
        sort_order = BY_DATE;
        items_per_line = 5;
    }

    strcpy(printer_id, argv[5]);

    sprintf(select1, 
    "SELECT TX_FREQ, LOAD_PER_MOBILE, TO_CHAR(LAST_UPDATE, 'DD/MM/YY') FROM CHANNEL_TRAFFIC ORDER BY %s", order_by_str);

    EXEC SQL PREPARE S1 FROM :select1;
    EXEC SQL DECLARE C1 CURSOR FOR S1;

    EXEC SQL OPEN C1;

    EXEC SQL FETCH C1 INTO :o_freq, :o_traffic, :o_date;
    if (sqlca.sqlcode == NOT_FOUND)
    {
        fprintf(rfp, "Fatal error: no record found in CHANNEL_TRAFFIC");
        EXEC SQL CLOSE C1;
        fclose(rfp);
        goto force_exit;
    }

    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);

    print_rpt_head(rfp, sort_order, page_cnt); 
    page_cnt++;
    line_cnt = HEADLINES + COLUMN_HEADLINES;
    rpt_line[0] = prev_date[0] = '\0';

    if (sort_order == BY_FREQ)
        for (; ;)
        {
            o_date.arr[o_date.len] = '\0';

            sprintf(sub_rpt_line, "%11.5lf   %6.4f    %s       ",
                    o_freq, o_traffic, o_date.arr);
            strcat(rpt_line, sub_rpt_line);

            item_cnt = (item_cnt + 1) % items_per_line;
            if (item_cnt == 0)
            {
                if (line_cnt == MAX_RPT_LINES)
                {
                    print_rpt_head(rfp, sort_order, page_cnt); 
                    page_cnt++;
                    line_cnt = HEADLINES + COLUMN_HEADLINES;
                }
                fprintf(rfp, "%10s%s\n", "", rpt_line);
                rpt_line[0] = '\0';
                line_cnt++;
            }

            EXEC SQL FETCH C1 INTO :o_freq, :o_traffic, :o_date;

            if (sqlca.sqlcode == NOT_FOUND)
            {
                if (item_cnt != 0)
                    fprintf(rfp, "%10s%s\n", "", rpt_line);

                break;
            }
        }
    else
        for (; ;)
        {
            o_date.arr[o_date.len] = '\0';

/*            if (strcmp(o_date.arr, prev_date)) */
            if (strcmp((char *)o_date.arr, prev_date))
            {
                if (prev_date[0] != '\0')
                {
                    fprintf(rfp, "%s\n", rpt_line);
                    fprintf(rfp, "%10s", "");
                    line_cnt++;
                    rpt_line[0] = '\0';
                    item_cnt = 0;
                    if ((line_cnt + COLUMN_HEADLINES) >= MAX_RPT_LINES)
                    {
                        print_rpt_head(rfp, sort_order, page_cnt); 
                        page_cnt++;
                        line_cnt = HEADLINES + COLUMN_HEADLINES;
                    }
                    else
                    {
                        print_by_date_rpt_col(rfp); 
                        line_cnt += COLUMN_HEADLINES;
                    }
                }
/*                strcpy(prev_date, o_date.arr); */
                strcpy(prev_date, (char *)o_date.arr);                
            }

            sprintf(sub_rpt_line, "%11.5lf  %6.4f      ", o_freq, o_traffic);
            strcat(rpt_line, sub_rpt_line);

            item_cnt = (item_cnt + 1) % items_per_line;
            if (item_cnt == 0)
            {
                if (line_cnt == MAX_RPT_LINES)
                {
                    print_rpt_head(rfp, sort_order, page_cnt); 
                    page_cnt++;
                    line_cnt = HEADLINES + COLUMN_HEADLINES;
                }
                fprintf(rfp, "%s\n", rpt_line);
                rpt_line[0] = '\0';
                line_cnt++;
            }

            EXEC SQL FETCH C1 INTO :o_freq, :o_traffic, :o_date;

            if (sqlca.sqlcode == NOT_FOUND)
            {
                if (item_cnt != 0)
                    fprintf(rfp, "%s\n", rpt_line);

                break;
            }
        }
    
    EXEC SQL CLOSE C1;

    fprintf(rfp, "\n\n%57s** END OF REPORT **", "");
    fclose(rfp);

    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", fname);
    system(cmdline);
/*
    unlink(fname);
*/

    exit(0);

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    fprintf(rfp, "%s\n", err_msg);
    fclose(rfp);

force_exit:
    exit(1);

}


/**********************************************************************/
/*  print channel traffic report column heading                       */
/**********************************************************************/

void print_by_date_rpt_col(rfp)
FILE   *rfp;
{
    int    i;

    fprintf(rfp, "\n\nMEASUREMENT DATE : %s\n\n", o_date.arr);
    for (i = 0; i < items_per_line - 1; i++)
        fprintf(rfp, "FREQUENCY    TRAFFIC     ");
    fprintf(rfp, "FREQUENCY    TRAFFIC\n");
    for (i = 0; i < items_per_line - 1; i++)
        fprintf(rfp, "===========  =======     ");
    fprintf(rfp, "===========  =======\n");
}


/**********************************************************************/
/*  print channel traffic report heading                              */
/**********************************************************************/

void print_rpt_head(rfp, sort_order, page_cnt)
FILE    *rfp;
int     sort_order;
int     page_cnt;
{
   if (page_cnt > 1)
       fprintf(rfp, "\f");
   fprintf(rfp, "USER ID  : %-15s%26s", user_id, "");
   fprintf(rfp, "ENHANCED SPECTRUM MANAGEMENT SYSTEM");
   fprintf(rfp, "%27sPAGE    : %-d\n", "", page_cnt);
   fprintf(rfp, "RUN DATE : %s", sys_date);
   fprintf(rfp, "%35sMRS CHANNEL MAINTENANCE REPORT", "");
   fprintf(rfp, "%30sPROGRAM : esemctmr\n", "");
   if (sort_order == BY_FREQ)
   {
       fprintf(rfp, "%58s(SORTED BY FREQUENCY)\n\n\n","");     //20180627 Cyrus Add ,"" at the end , cause  ‘%s’ expects a matching ‘char *’ argument
       fprintf(rfp, "                                  MEASURED");
       fprintf(rfp, "                               MEASURED                               MEASURED\n"); 
       fprintf(rfp, "%10sFREQUENCY     TRAFFIC     DATE         ", "");
       fprintf(rfp, "FREQUENCY     TRAFFIC     DATE         FREQUENCY     TRAFFIC     DATE\n");
       fprintf(rfp, "%10s===========   =======   ========       ", "");
       fprintf(rfp, "===========   =======   ========       ===========   =======   ========\n");
   }
   else
   {
	   
       fprintf(rfp, "%55s(SORTED BY MEASUREMENT DATE)\n\n",""); //20180627 Cyrus Add ,"" at the end , cause  ‘%s’ expects a matching ‘char *’ argument
       print_by_date_rpt_col(rfp);
   }
}
