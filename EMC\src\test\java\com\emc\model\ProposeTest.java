package com.emc.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class ProposeTest {

    @Test
    void testSetStnNode() {
        // Setup
        Propose propose = new Propose();
        propose.setStnNode(5);
        
        // Execute
        int prevNode = propose.setStnNode(10);
        
        // Verify
        assertEquals(5, prevNode);
        assertEquals(10, propose.getStnNode());
    }

    @Test
    void testProposeProperties() {
        // Setup
        Propose propose = new Propose();
        
        // Set properties
        propose.setEmcUid("TEST_EMC_UID");
        propose.setSysCategory('X');
        propose.setSysType("XX");
        propose.setSysNo("0001");
        propose.setSysSuffix("000");
        propose.setEastGrid(12345);
        propose.setNorthGrid(67890);
        propose.setSubDistrict("ABC");
        propose.setStationType('X');
        propose.setDesenAttDb(12.34);
        propose.setIntmodAttDb(56.78);
        propose.setAntenna("YZ");
        propose.setAntHeight(45);
        propose.setPwDbw(12.34);
        propose.setAzMaxRad(123);
        propose.setAzMaxRadR(2.1467);
        propose.setFeedLoss(123);
        propose.setSfxFilter("FILTER123");
        propose.setHeightAsl(100.0);
        propose.setDistType('U');
        propose.setNoiseCode(1);
        propose.setDistIndex(2);
        propose.setTxChannel(1234);
        propose.setRxChannel(5678);
        propose.setMode('S');
        propose.setBand(1);
        propose.setStnNode(3);
        
        // Verify
        assertEquals("TEST_EMC_UID", propose.getEmcUid());
        assertEquals('X', propose.getSysCategory());
        assertEquals("XX", propose.getSysType());
        assertEquals("0001", propose.getSysNo());
        assertEquals("000", propose.getSysSuffix());
        assertEquals(12345, propose.getEastGrid());
        assertEquals(67890, propose.getNorthGrid());
        assertEquals("ABC", propose.getSubDistrict());
        assertEquals('X', propose.getStationType());
        assertEquals(12.34, propose.getDesenAttDb());
        assertEquals(56.78, propose.getIntmodAttDb());
        assertEquals("YZ", propose.getAntenna());
        assertEquals(45, propose.getAntHeight());
        assertEquals(12.34, propose.getPwDbw());
        assertEquals(123, propose.getAzMaxRad());
        assertEquals(2.1467, propose.getAzMaxRadR());
        assertEquals(123, propose.getFeedLoss());
        assertEquals("FILTER123", propose.getSfxFilter());
        assertEquals(100.0, propose.getHeightAsl());
        assertEquals('U', propose.getDistType());
        assertEquals(1, propose.getNoiseCode());
        assertEquals(2, propose.getDistIndex());
        assertEquals(1234, propose.getTxChannel());
        assertEquals(5678, propose.getRxChannel());
        assertEquals('S', propose.getMode());
        assertEquals(1, propose.getBand());
        assertEquals(3, propose.getStnNode());
    }
}
