extern  char    user_id[];
extern  char	sys_date[];
extern  char	sys_time[];
extern  char	yymmdd[];
extern  char	hhmmss[];
extern  char	printer_id[];
extern  char	print_file[];
extern  char	*emc_dir;
extern  char	emc_uid[];

extern  int     cull_stn_cnt;
extern  int     prop_fq_cnt;
extern  int     prop_stn_cnt;

extern  int     interactive;

extern  FILE    *afp;

/*****************************************************/
/*           Variables for summary totals            */
/*****************************************************/
extern int     cochan_tot;
extern int     desen_tot;
extern int     intmod2_vict_tot;
extern int     intmod2_tx1_tot;
extern int     intmod2_tx2_tot;
extern int     intmod3_vict_tot;
extern int     intmod3_tx1_tot;
extern int     intmod3_tx3_tot;
