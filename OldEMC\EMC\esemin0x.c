
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,0,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[12];
};
static const struct sqlcxp sqlfpn =
{
    11,
    "esemin0x.pc"
};


static unsigned int sqlctx = 149723;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[4];
   unsigned long  sqhstl[4];
            int   sqhsts[4];
            short *sqindv[4];
            int   sqinds[4];
   unsigned long  sqharm[4];
   unsigned long  *sqharc[4];
   unsigned short  sqadto[4];
   unsigned short  sqtdso[4];
} sqlstm = {13,4};

/* SQLLIB Prototypes */
extern void sqlcxt (void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlcx2t(void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlbuft(void **, char *);
extern void sqlgs2t(void **, char *);
extern void sqlorat(void **, unsigned int *, void *);

/* Forms Interface */
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern void sqliem(unsigned char *, signed int *);

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4242,1,0,0,
5,0,0,1,117,0,4,152,0,0,4,3,0,1,0,2,4,0,0,1,9,0,0,1,4,0,0,1,4,0,0,
36,0,0,2,68,0,4,323,0,0,2,1,0,1,0,2,3,0,0,1,9,0,0,
59,0,0,3,83,0,4,421,0,0,3,2,0,1,0,2,4,0,0,1,9,0,0,1,3,0,0,
};


#line 1 "esemin0x.pc"
/**********************************************************************/
/*                                                                    */
/*    Module Name   :  base_to_base (esemin0x.pc)                     */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  desensit_interference (esemdc0r.c)             */  
/*                     intermod_2 (esemim0r.c)                        */  
/*                     intermod_3 (esemim0r.c)                        */  
/*                                                                    */
/*    Parameters    :  intrfr_flag (interference flag)                */
/*                     caller (caller id.)                            */
/*                                                                    */
/*    Called Modules:  cal_dist (utility.c)                           */
/*                     get_diffract_loss (esemdl0x.c)                 */
/*                     get_vict_antenna_gain (esemin0x.pc)            */
/*                                                                    */
/*    Purpose       :  Calculate base-to-base interference and then   */
/*                     determine the interference is significant      */
/*                     (according to nature of caller). If yes, then  */
/*                     set intrfr_flag to TRUE.                       */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <math.h>
/* #include "../include/math.h" */
#include "../include/macros.h"
#include "../include/define.h"
#include "../include/emcext.h"
#include "../include/refext.h"
#include "../include/victext.h"
#include "../include/intrext.h"
#include "../include/presuext.h"
#include "../include/propext.h"

#ifndef M_PI
#define M_PI            3.14159265358979323846
#endif

#ifndef M_PI_2
#define M_PI_2          1.57079632679489661923
#endif


float    cal_dist();

float get_diffract_loss(float*, float, float*, float);
int	 get_vict_antenna_gain();





extern char msg[];

/* EXEC SQL BEGIN DECLARE SECTION; */ 
#line 63 "esemin0x.pc"


    /* VARCHAR o_sfx_filter[11]; */ 
struct { unsigned short len; unsigned char arr[11]; } o_sfx_filter;
#line 65 "esemin0x.pc"

    double  o_chan_sep;
    float   o_filter_att_db;
    /* VARCHAR o_antenna[3]; */ 
struct { unsigned short len; unsigned char arr[3]; } o_antenna;
#line 68 "esemin0x.pc"

    int     o_degree;
    float   o_gain_in_db;
    int     o_cnt;
    float   epsilon;

/* EXEC SQL END DECLARE SECTION; */ 
#line 74 "esemin0x.pc"


/* EXEC SQL INCLUDE SQLCA;
 */ 
#line 1 "/u01/app/oracle/product/12.2.0/dbhome_1/precomp/public/SQLCA.H"
/*
 * $Header: sqlca.h 24-apr-2003.12:50:58 mkandarp Exp $ sqlca.h 
 */

/* Copyright (c) 1985, 2003, Oracle Corporation.  All rights reserved.  */
 
/*
NAME
  SQLCA : SQL Communications Area.
FUNCTION
  Contains no code. Oracle fills in the SQLCA with status info
  during the execution of a SQL stmt.
NOTES
  **************************************************************
  ***                                                        ***
  *** This file is SOSD.  Porters must change the data types ***
  *** appropriately on their platform.  See notes/pcport.doc ***
  *** for more information.                                  ***
  ***                                                        ***
  **************************************************************

  If the symbol SQLCA_STORAGE_CLASS is defined, then the SQLCA
  will be defined to have this storage class. For example:
 
    #define SQLCA_STORAGE_CLASS extern
 
  will define the SQLCA as an extern.
 
  If the symbol SQLCA_INIT is defined, then the SQLCA will be
  statically initialized. Although this is not necessary in order
  to use the SQLCA, it is a good pgming practice not to have
  unitialized variables. However, some C compilers/OS's don't
  allow automatic variables to be init'd in this manner. Therefore,
  if you are INCLUDE'ing the SQLCA in a place where it would be
  an automatic AND your C compiler/OS doesn't allow this style
  of initialization, then SQLCA_INIT should be left undefined --
  all others can define SQLCA_INIT if they wish.

  If the symbol SQLCA_NONE is defined, then the SQLCA variable will
  not be defined at all.  The symbol SQLCA_NONE should not be defined
  in source modules that have embedded SQL.  However, source modules
  that have no embedded SQL, but need to manipulate a sqlca struct
  passed in as a parameter, can set the SQLCA_NONE symbol to avoid
  creation of an extraneous sqlca variable.
 
MODIFIED
    lvbcheng   07/31/98 -  long to int
    jbasu      12/12/94 -  Bug 217878: note this is an SOSD file
    losborne   08/11/92 -  No sqlca var if SQLCA_NONE macro set 
  Clare      12/06/84 - Ch SQLCA to not be an extern.
  Clare      10/21/85 - Add initialization.
  Bradbury   01/05/86 - Only initialize when SQLCA_INIT set
  Clare      06/12/86 - Add SQLCA_STORAGE_CLASS option.
*/
 
#ifndef SQLCA
#define SQLCA 1
 
struct   sqlca
         {
         /* ub1 */ char    sqlcaid[8];
         /* b4  */ int     sqlabc;
         /* b4  */ int     sqlcode;
         struct
           {
           /* ub2 */ unsigned short sqlerrml;
           /* ub1 */ char           sqlerrmc[70];
           } sqlerrm;
         /* ub1 */ char    sqlerrp[8];
         /* b4  */ int     sqlerrd[6];
         /* ub1 */ char    sqlwarn[8];
         /* ub1 */ char    sqlext[8];
         };

#ifndef SQLCA_NONE 
#ifdef   SQLCA_STORAGE_CLASS
SQLCA_STORAGE_CLASS struct sqlca sqlca
#else
         struct sqlca sqlca
#endif
 
#ifdef  SQLCA_INIT
         = {
         {'S', 'Q', 'L', 'C', 'A', ' ', ' ', ' '},
         sizeof(struct sqlca),
         0,
         { 0, {0}},
         {'N', 'O', 'T', ' ', 'S', 'E', 'T', ' '},
         {0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0}
         }
#endif
         ;
#endif
 
#endif
 
/* end SQLCA */

#line 77 "esemin0x.pc"

int base_to_base(intrfr_flag, caller)
int    *intrfr_flag;
int    caller;
{
    float   log_delta_dist;
    float   diffract_loss;
    float   off_chan_rej;
    float   prop_loss;
    float   min_usable_power;
    double  curve_adj;
    float   tmp_vict_grid[2], tmp_intr_grid[2];
    int     i;


    /* EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn; */ 
#line 92 "esemin0x.pc"

    /* EXEC SQL WHENEVER NOT FOUND CONTINUE; */ 
#line 93 "esemin0x.pc"



/*
#ifdef DEBUG
    printf("base_to_base\n");
#endif
*/

    epsilon = FREQ_EPSILON;
    tmp_vict_grid[0] = vict.grid[0] / 10;
    tmp_vict_grid[1] = vict.grid[1] / 10;
    tmp_intr_grid[0] = intr.grid[0] / 10;
    tmp_intr_grid[1] = intr.grid[1] / 10;
    delta_dist       = cal_dist(tmp_vict_grid, tmp_intr_grid) * 1000;
	
	
/*
printf("vict0 vict1 intr0 intr1 delta_dist: %f %f %f %f %f\n",
tmp_vict_grid[0], tmp_vict_grid[1], tmp_intr_grid[0], tmp_intr_grid[1],
delta_dist);
*/
/*
system("echo \"esemin0x data\" >> /tmp/debug");
sprintf(msg, "echo \"%f %f %f %f %f\" >> /tmp/debug",
tmp_vict_grid[0], tmp_vict_grid[1], tmp_intr_grid[0], tmp_intr_grid[1],
delta_dist);
system(msg);
*/

    log_delta_dist = (float)log10((double)delta_dist/1000);


	
    diffract_loss = get_diffract_loss(intr.grid, intr.ant_height,
                                      vict.grid, vict.ant_height);

    prop_loss =   28.1 + 20.0*log10(intr.tx_freq) + 20.0*log_delta_dist
                + diffract_loss + CLUTTER_LOSS;

    /*********************************************************************/
    /* If antenna gain (horizontal) of victim station is to be neglected */
    /* or antenna is non-directional (currently with respect to vertical */
    /* plane), then vict.az_max_rad is set to 999 in EMC Batch Entry     */
    /*********************************************************************/
    vict.ant_gain = 0.0;
    if (vict.az_max_rad != 999)
        get_vict_antenna_gain();

    if ((vict.sfx_filter[0] != '\0') && (vict.sfx_filter[0] != ' '))
    {
/*        strcpy(o_sfx_filter.arr, vict.sfx_filter); */
	    strcpy((char *)o_sfx_filter.arr, vict.sfx_filter);
        o_sfx_filter.len = strlen((char *)o_sfx_filter.arr);
        o_chan_sep = delta_freq;
/*
system("echo \"esemin0x 1\" >> /tmp/debug");
sprintf(msg, "echo \"%s %f %f\" >> /tmp/debug",
o_sfx_filter.arr, o_chan_sep, epsilon );
system(msg);
*/

        /* EXEC SQL
             SELECT ATTENUATION_IN_DB
             INTO   :o_filter_att_db
             FROM   SFX_FILTER_ATT
             WHERE  FILTER_TYPE = :o_sfx_filter
             AND    ABS(CHANNEL_SEPARATION - :o_chan_sep) <= :epsilon; */ 
#line 157 "esemin0x.pc"

{
#line 152 "esemin0x.pc"
        struct sqlexd sqlstm;
#line 152 "esemin0x.pc"
        sqlstm.sqlvsn = 13;
#line 152 "esemin0x.pc"
        sqlstm.arrsiz = 4;
#line 152 "esemin0x.pc"
        sqlstm.sqladtp = &sqladt;
#line 152 "esemin0x.pc"
        sqlstm.sqltdsp = &sqltds;
#line 152 "esemin0x.pc"
        sqlstm.stmt = "select ATTENUATION_IN_DB into :b0  from SFX_FILTER_AT\
T where (FILTER_TYPE=:b1 and ABS((CHANNEL_SEPARATION-:b2))<=:b3)";
#line 152 "esemin0x.pc"
        sqlstm.iters = (unsigned int  )1;
#line 152 "esemin0x.pc"
        sqlstm.offset = (unsigned int  )5;
#line 152 "esemin0x.pc"
        sqlstm.selerr = (unsigned short)1;
#line 152 "esemin0x.pc"
        sqlstm.sqlpfmem = (unsigned int  )0;
#line 152 "esemin0x.pc"
        sqlstm.cud = sqlcud0;
#line 152 "esemin0x.pc"
        sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 152 "esemin0x.pc"
        sqlstm.sqlety = (unsigned short)4352;
#line 152 "esemin0x.pc"
        sqlstm.occurs = (unsigned int  )0;
#line 152 "esemin0x.pc"
        sqlstm.sqhstv[0] = (unsigned char  *)&o_filter_att_db;
#line 152 "esemin0x.pc"
        sqlstm.sqhstl[0] = (unsigned long )sizeof(float);
#line 152 "esemin0x.pc"
        sqlstm.sqhsts[0] = (         int  )0;
#line 152 "esemin0x.pc"
        sqlstm.sqindv[0] = (         short *)0;
#line 152 "esemin0x.pc"
        sqlstm.sqinds[0] = (         int  )0;
#line 152 "esemin0x.pc"
        sqlstm.sqharm[0] = (unsigned long )0;
#line 152 "esemin0x.pc"
        sqlstm.sqadto[0] = (unsigned short )0;
#line 152 "esemin0x.pc"
        sqlstm.sqtdso[0] = (unsigned short )0;
#line 152 "esemin0x.pc"
        sqlstm.sqhstv[1] = (unsigned char  *)&o_sfx_filter;
#line 152 "esemin0x.pc"
        sqlstm.sqhstl[1] = (unsigned long )13;
#line 152 "esemin0x.pc"
        sqlstm.sqhsts[1] = (         int  )0;
#line 152 "esemin0x.pc"
        sqlstm.sqindv[1] = (         short *)0;
#line 152 "esemin0x.pc"
        sqlstm.sqinds[1] = (         int  )0;
#line 152 "esemin0x.pc"
        sqlstm.sqharm[1] = (unsigned long )0;
#line 152 "esemin0x.pc"
        sqlstm.sqadto[1] = (unsigned short )0;
#line 152 "esemin0x.pc"
        sqlstm.sqtdso[1] = (unsigned short )0;
#line 152 "esemin0x.pc"
        sqlstm.sqhstv[2] = (unsigned char  *)&o_chan_sep;
#line 152 "esemin0x.pc"
        sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
#line 152 "esemin0x.pc"
        sqlstm.sqhsts[2] = (         int  )0;
#line 152 "esemin0x.pc"
        sqlstm.sqindv[2] = (         short *)0;
#line 152 "esemin0x.pc"
        sqlstm.sqinds[2] = (         int  )0;
#line 152 "esemin0x.pc"
        sqlstm.sqharm[2] = (unsigned long )0;
#line 152 "esemin0x.pc"
        sqlstm.sqadto[2] = (unsigned short )0;
#line 152 "esemin0x.pc"
        sqlstm.sqtdso[2] = (unsigned short )0;
#line 152 "esemin0x.pc"
        sqlstm.sqhstv[3] = (unsigned char  *)&epsilon;
#line 152 "esemin0x.pc"
        sqlstm.sqhstl[3] = (unsigned long )sizeof(float);
#line 152 "esemin0x.pc"
        sqlstm.sqhsts[3] = (         int  )0;
#line 152 "esemin0x.pc"
        sqlstm.sqindv[3] = (         short *)0;
#line 152 "esemin0x.pc"
        sqlstm.sqinds[3] = (         int  )0;
#line 152 "esemin0x.pc"
        sqlstm.sqharm[3] = (unsigned long )0;
#line 152 "esemin0x.pc"
        sqlstm.sqadto[3] = (unsigned short )0;
#line 152 "esemin0x.pc"
        sqlstm.sqtdso[3] = (unsigned short )0;
#line 152 "esemin0x.pc"
        sqlstm.sqphsv = sqlstm.sqhstv;
#line 152 "esemin0x.pc"
        sqlstm.sqphsl = sqlstm.sqhstl;
#line 152 "esemin0x.pc"
        sqlstm.sqphss = sqlstm.sqhsts;
#line 152 "esemin0x.pc"
        sqlstm.sqpind = sqlstm.sqindv;
#line 152 "esemin0x.pc"
        sqlstm.sqpins = sqlstm.sqinds;
#line 152 "esemin0x.pc"
        sqlstm.sqparm = sqlstm.sqharm;
#line 152 "esemin0x.pc"
        sqlstm.sqparc = sqlstm.sqharc;
#line 152 "esemin0x.pc"
        sqlstm.sqpadto = sqlstm.sqadto;
#line 152 "esemin0x.pc"
        sqlstm.sqptdso = sqlstm.sqtdso;
#line 152 "esemin0x.pc"
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 152 "esemin0x.pc"
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 152 "esemin0x.pc"
}

#line 157 "esemin0x.pc"


        if (sqlca.sqlcode == NOT_FOUND)
        {
            fprintf(afp, "Warning: No attenuation for filter %s ",
                    o_sfx_filter.arr);
            fprintf(afp, "at channel separation %lf\n", o_chan_sep);
            o_filter_att_db = 0.0;
        }
/*
printf("sfx_filter att_db: %s %f\n", o_sfx_filter.arr, o_filter_att_db);
*/
    }
    else
        o_filter_att_db = 0.0;

min_usable_power = min_signal[subdist[prop->dist_index].noise_code][prop->band];
/*
printf("idx min noise band i delta intr: %d %f %d %d %d %lf %f\n", 
prop->dist_index, min_usable_power, subdist[prop->dist_index].noise_code,prop->band, i, delta_freq, power_intr);
printf("prop freq diffr vfeed gain off att_db al: %f %lf %f %d %f %f %f %f\n",
prop_loss,intr.tx_freq,diffract_loss,vict.feed_loss,vict.ant_gain,off_chan_rej,o_filter_att_db, prop->att_db);
*/


switch (caller)
{
    case DESENSIT:
    {
        if (delta_freq < FREQ_EPSILON)
            i = 0;
        else
	    for (i = 1; i < off_chan_cnt; i++)
            {
                if (delta_freq < off_channel[i].ch_sep)
                    break;
                if (abs(delta_freq - off_channel[i].ch_sep) < FREQ_EPSILON)
                    break;    /* delta_freq = off_channel[i].ch_sep */
            }
/*
                if (delta_freq < off_channel[i].ch_sep)
                    if (abs(delta_freq - off_channel[i].ch_sep) > FREQ_EPSILON)
                        break;
*/

        off_chan_rej = (i == off_chan_cnt)?
                   off_channel[i-1].rej_db[prop->band]:
                   off_channel[i].rej_db[prop->band];

        power_intr =   intr.pw_dbw - prop_loss -  vict.feed_loss + vict.ant_gain
                     - off_chan_rej - o_filter_att_db;
        attenuation = power_intr - min_usable_power + 6.0;
		

		
        if (attenuation >= prop->desen_att_db)
            *intrfr_flag = TRUE;
/*
printf("att flag: %f %d\n",attenuation,*intrfr_flag);
*/
        break;
    }
    case INTERMOD:
    {
        double  x = log10(delta_freq);
        double  x2 = x * x;
        double  x3 = x2 * x;
        double  x4 = x2 * x2;
        double  c0 = 56.1666142692494105;
        double  c1 = 22.143965108789265;
        double  c2 = 17.365681294850533;
        double  c3 = 11.876775570883986;
        double  c4 = 3.8234601723933208;

        curve_adj  = (-1) * (c0 - c1*x - c2*x2 - c3*x3 - c4*x4);
        power_intr =   intr.pw_dbw - prop_loss -  vict.feed_loss + vict.ant_gain
                     - o_filter_att_db;
        
        if (power_intr > curve_adj + prop->intmod_att_db)
            *intrfr_flag = TRUE;
/*
printf("intr min intr_flag: %f %f %d\n", power_intr,min_usable_power,*intrfr_flag);
        break;
*/
    }
}


#ifdef DEBUG
    if (interactive == FALSE)
    {
        float a=20.0*log10(intr.tx_freq);
        float b=20.0*log_delta_dist;
        if (caller == DESENSIT)
        {
           printf("%10.4lf  %10.4lf  %6.2f   %6.1f  %6.1f  %6.2f  %3d   %5.2f",
                  intr.tx_freq, vict.rx_freq, attenuation, min_usable_power,
                  power_intr, intr.pw_dbw, vict.feed_loss, vict.ant_gain);
           printf("  %6.2f  %6.2f  %6.2f  %6.2f  %6.2f    %6.2f    %d\n", 
                  off_chan_rej, o_filter_att_db, prop_loss, a, b, diffract_loss,
                  *intrfr_flag);
        }
        if (caller == INTERMOD)
           printf("%10.4lf %10.4lf %6.1f  %6.1f %6.2f %3d  %5.2f  %7.2f  %6.2f %6.2f %6.2f %6.2f    %6.2f  %d\n", 
           intr.tx_freq, vict.rx_freq, min_usable_power, power_intr,
           intr.pw_dbw, vict.feed_loss, vict.ant_gain, curve_adj, 
           o_filter_att_db, prop_loss, a, b, diffract_loss, *intrfr_flag);
    }
#endif

/* commented out by Chen Yung
return;
*/
/* changed by Chen Yung */
/* original: */
/*    return; */
    return(0);


sqlerr_rtn:
/* changed by Chen Yung */
/* original: */
/*    printf("%s\c", sqlca.sqlerrm.sqlerrmc); */
    printf("%s\n", sqlca.sqlerrm.sqlerrmc);
    fflush(afp);
    fclose(afp);
    exit(1);

}


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  get_vict_antenna_gain (esemin0x.pc)            */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  base_to_base(esemin0x.pc)                      */  
/*                                                                    */
/*    Parameters    :                                                 */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Calculate antenna gain of victim station       */
/*                     with respect to interfering station.           */
/*                     Currently, only horizontal antenna gain is     */
/*                     considered.                                    */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

int get_vict_antenna_gain()
{

/*
#ifdef DEBUG
    printf("get_vict_antenna_gain\n");
#endif
*/


/*    strcpy(o_antenna.arr, vict.antenna); */

    strcpy((char *)o_antenna.arr, vict.antenna);
    o_antenna.len = strlen((char *)o_antenna.arr);

    
    /* EXEC SQL
         SELECT COUNT(ROWID)
         INTO   :o_cnt
         FROM   HZ_ANTENNA_GAIN
         WHERE  ANTENNA = :o_antenna; */ 
#line 327 "esemin0x.pc"

{
#line 323 "esemin0x.pc"
    struct sqlexd sqlstm;
#line 323 "esemin0x.pc"
    sqlstm.sqlvsn = 13;
#line 323 "esemin0x.pc"
    sqlstm.arrsiz = 4;
#line 323 "esemin0x.pc"
    sqlstm.sqladtp = &sqladt;
#line 323 "esemin0x.pc"
    sqlstm.sqltdsp = &sqltds;
#line 323 "esemin0x.pc"
    sqlstm.stmt = "select count(ROWID) into :b0  from HZ_ANTENNA_GAIN where \
ANTENNA=:b1";
#line 323 "esemin0x.pc"
    sqlstm.iters = (unsigned int  )1;
#line 323 "esemin0x.pc"
    sqlstm.offset = (unsigned int  )36;
#line 323 "esemin0x.pc"
    sqlstm.selerr = (unsigned short)1;
#line 323 "esemin0x.pc"
    sqlstm.sqlpfmem = (unsigned int  )0;
#line 323 "esemin0x.pc"
    sqlstm.cud = sqlcud0;
#line 323 "esemin0x.pc"
    sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 323 "esemin0x.pc"
    sqlstm.sqlety = (unsigned short)4352;
#line 323 "esemin0x.pc"
    sqlstm.occurs = (unsigned int  )0;
#line 323 "esemin0x.pc"
    sqlstm.sqhstv[0] = (unsigned char  *)&o_cnt;
#line 323 "esemin0x.pc"
    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
#line 323 "esemin0x.pc"
    sqlstm.sqhsts[0] = (         int  )0;
#line 323 "esemin0x.pc"
    sqlstm.sqindv[0] = (         short *)0;
#line 323 "esemin0x.pc"
    sqlstm.sqinds[0] = (         int  )0;
#line 323 "esemin0x.pc"
    sqlstm.sqharm[0] = (unsigned long )0;
#line 323 "esemin0x.pc"
    sqlstm.sqadto[0] = (unsigned short )0;
#line 323 "esemin0x.pc"
    sqlstm.sqtdso[0] = (unsigned short )0;
#line 323 "esemin0x.pc"
    sqlstm.sqhstv[1] = (unsigned char  *)&o_antenna;
#line 323 "esemin0x.pc"
    sqlstm.sqhstl[1] = (unsigned long )5;
#line 323 "esemin0x.pc"
    sqlstm.sqhsts[1] = (         int  )0;
#line 323 "esemin0x.pc"
    sqlstm.sqindv[1] = (         short *)0;
#line 323 "esemin0x.pc"
    sqlstm.sqinds[1] = (         int  )0;
#line 323 "esemin0x.pc"
    sqlstm.sqharm[1] = (unsigned long )0;
#line 323 "esemin0x.pc"
    sqlstm.sqadto[1] = (unsigned short )0;
#line 323 "esemin0x.pc"
    sqlstm.sqtdso[1] = (unsigned short )0;
#line 323 "esemin0x.pc"
    sqlstm.sqphsv = sqlstm.sqhstv;
#line 323 "esemin0x.pc"
    sqlstm.sqphsl = sqlstm.sqhstl;
#line 323 "esemin0x.pc"
    sqlstm.sqphss = sqlstm.sqhsts;
#line 323 "esemin0x.pc"
    sqlstm.sqpind = sqlstm.sqindv;
#line 323 "esemin0x.pc"
    sqlstm.sqpins = sqlstm.sqinds;
#line 323 "esemin0x.pc"
    sqlstm.sqparm = sqlstm.sqharm;
#line 323 "esemin0x.pc"
    sqlstm.sqparc = sqlstm.sqharc;
#line 323 "esemin0x.pc"
    sqlstm.sqpadto = sqlstm.sqadto;
#line 323 "esemin0x.pc"
    sqlstm.sqptdso = sqlstm.sqtdso;
#line 323 "esemin0x.pc"
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 323 "esemin0x.pc"
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 323 "esemin0x.pc"
}

#line 327 "esemin0x.pc"


    if (o_cnt == 0)
        fprintf(afp, "Warning: no horizontal antenna gain record for '%s' (%s, base no. %d)\n", 
                vict.antenna, vict.system_id, vict.base_no);
    else
    {
        double   ar2, ad;

/*
        double   ar1, ar2, ad;
        double   val_in_tan;

        ar1 = 5 * M_PI / 2 - vict.az_max_rad_r;
        if (ar1 - 2 * M_PI > (double)0)
            ar1 -= (2 * M_PI);
        else
            if (abs(ar1 - 2 * M_PI) <  EPSILON)
                ar1 = (double)0;

        if (((intr.grid[0] > vict.grid[0]) && (intr.grid[1] > vict.grid[1]))
        ||  ((intr.grid[0] > vict.grid[0]) && (intr.grid[1] < vict.grid[1])))
        {
            val_in_tan = (double)((intr.grid[1]-vict.grid[1])/((intr.grid[0]-vict.grid[0])));
            ar2 = atan(val_in_tan);
            ar2 = atan2((double)((intr.grid[1]-vict.grid[1])/10),
                        (double)((intr.grid[0]-vict.grid[0])/10));
        }
        
        if (((intr.grid[0] < vict.grid[0]) && (intr.grid[1] > vict.grid[1]))
        ||  ((intr.grid[0] < vict.grid[0]) && (intr.grid[1] < vict.grid[1])))
        {
            val_in_tan = (double)((intr.grid[1]-vict.grid[1])/((intr.grid[0]-vict.grid[0])));
            ar2 = atan(val_in_tan) + M_PI_2;
            ar2 =   atan2((double)((intr.grid[1]-vict.grid[1])/10),
                          (double)((intr.grid[0]-vict.grid[0])/10))
                  + M_PI_2;
        }
*/

        ar2 = atan2((double)(intr.grid[0]-vict.grid[0]),
                    (double)(intr.grid[1]-vict.grid[1]));

        if (intr.grid[0] == vict.grid[0])
		{   /* 20170703 Cyrusma -Add "{}" into if statement */
            if (intr.grid[1] == vict.grid[1])
                ar2 = 0; /* co-site antennae */
            else
                ar2 = (intr.grid[1] > vict.grid[1]) ? 0 : M_PI;
		}	

        if (intr.grid[1] == vict.grid[1])
		{   /* 20170703 Cyrusma -Add "{}" into if statement */
	
            if (intr.grid[0] == vict.grid[0])
                ar2 = 0; /* co-site antennae */
            else
                ar2 = (intr.grid[0] > vict.grid[0]) ? M_PI_2 : (3 * M_PI_2);
		}
/*
        ad = (ar1 - ar2) * (180 / M_PI);
*/

        if ((ar2 - 0) > EPSILON)
            ad = abs((vict.az_max_rad_r - ar2) * (180 / M_PI));
        else
            if (abs(ar2 - 0) < EPSILON)    /* ie., ar2 = 0 */
                ad = vict.az_max_rad_r * (180 / M_PI);
            else
                ad = abs((vict.az_max_rad_r - ar2) * (180 / M_PI) - 360.0);

        if (ad >= 0.0)
            if (ad > 360.0)
                o_degree = (int)(ad - 360.0 + 0.5);
            else
                o_degree = (int)(ad + 0.5);
        else
            o_degree = (int)(360.0 + ad + 0.5);

        if (o_degree == 360)
            o_degree = 0;

/*
printf("intr vict: %f %f %f %f\n",
            intr.grid[0],intr.grid[1],vict.grid[0],vict.grid[1]);
printf("az azr ar2 ad degree: %d %f %f %f %d\n", vict.az_max_rad, vict.az_max_rad_r, ar2, ad, o_degree);
printf("az azr ar1 ar2 ad degree: %d %f %f %f %f %d\n", vict.az_max_rad, vict.az_max_rad_r, ar1, ar2, ad, o_degree);
*/
/*
system("echo \"esemin0x 2\" >> /tmp/debug");
sprintf(msg, "echo \"%s %d\" >> /tmp/debug", o_antenna.arr , o_degree );
system(msg);
*/

        /* EXEC SQL
             SELECT GAIN_IN_DB
             INTO   :o_gain_in_db
             FROM   HZ_ANTENNA_GAIN
             WHERE  ANTENNA = :o_antenna
             AND    DEGREE = :o_degree; */ 
#line 426 "esemin0x.pc"

{
#line 421 "esemin0x.pc"
        struct sqlexd sqlstm;
#line 421 "esemin0x.pc"
        sqlstm.sqlvsn = 13;
#line 421 "esemin0x.pc"
        sqlstm.arrsiz = 4;
#line 421 "esemin0x.pc"
        sqlstm.sqladtp = &sqladt;
#line 421 "esemin0x.pc"
        sqlstm.sqltdsp = &sqltds;
#line 421 "esemin0x.pc"
        sqlstm.stmt = "select GAIN_IN_DB into :b0  from HZ_ANTENNA_GAIN wher\
e (ANTENNA=:b1 and DEGREE=:b2)";
#line 421 "esemin0x.pc"
        sqlstm.iters = (unsigned int  )1;
#line 421 "esemin0x.pc"
        sqlstm.offset = (unsigned int  )59;
#line 421 "esemin0x.pc"
        sqlstm.selerr = (unsigned short)1;
#line 421 "esemin0x.pc"
        sqlstm.sqlpfmem = (unsigned int  )0;
#line 421 "esemin0x.pc"
        sqlstm.cud = sqlcud0;
#line 421 "esemin0x.pc"
        sqlstm.sqlest = (unsigned char  *)&sqlca;
#line 421 "esemin0x.pc"
        sqlstm.sqlety = (unsigned short)4352;
#line 421 "esemin0x.pc"
        sqlstm.occurs = (unsigned int  )0;
#line 421 "esemin0x.pc"
        sqlstm.sqhstv[0] = (unsigned char  *)&o_gain_in_db;
#line 421 "esemin0x.pc"
        sqlstm.sqhstl[0] = (unsigned long )sizeof(float);
#line 421 "esemin0x.pc"
        sqlstm.sqhsts[0] = (         int  )0;
#line 421 "esemin0x.pc"
        sqlstm.sqindv[0] = (         short *)0;
#line 421 "esemin0x.pc"
        sqlstm.sqinds[0] = (         int  )0;
#line 421 "esemin0x.pc"
        sqlstm.sqharm[0] = (unsigned long )0;
#line 421 "esemin0x.pc"
        sqlstm.sqadto[0] = (unsigned short )0;
#line 421 "esemin0x.pc"
        sqlstm.sqtdso[0] = (unsigned short )0;
#line 421 "esemin0x.pc"
        sqlstm.sqhstv[1] = (unsigned char  *)&o_antenna;
#line 421 "esemin0x.pc"
        sqlstm.sqhstl[1] = (unsigned long )5;
#line 421 "esemin0x.pc"
        sqlstm.sqhsts[1] = (         int  )0;
#line 421 "esemin0x.pc"
        sqlstm.sqindv[1] = (         short *)0;
#line 421 "esemin0x.pc"
        sqlstm.sqinds[1] = (         int  )0;
#line 421 "esemin0x.pc"
        sqlstm.sqharm[1] = (unsigned long )0;
#line 421 "esemin0x.pc"
        sqlstm.sqadto[1] = (unsigned short )0;
#line 421 "esemin0x.pc"
        sqlstm.sqtdso[1] = (unsigned short )0;
#line 421 "esemin0x.pc"
        sqlstm.sqhstv[2] = (unsigned char  *)&o_degree;
#line 421 "esemin0x.pc"
        sqlstm.sqhstl[2] = (unsigned long )sizeof(int);
#line 421 "esemin0x.pc"
        sqlstm.sqhsts[2] = (         int  )0;
#line 421 "esemin0x.pc"
        sqlstm.sqindv[2] = (         short *)0;
#line 421 "esemin0x.pc"
        sqlstm.sqinds[2] = (         int  )0;
#line 421 "esemin0x.pc"
        sqlstm.sqharm[2] = (unsigned long )0;
#line 421 "esemin0x.pc"
        sqlstm.sqadto[2] = (unsigned short )0;
#line 421 "esemin0x.pc"
        sqlstm.sqtdso[2] = (unsigned short )0;
#line 421 "esemin0x.pc"
        sqlstm.sqphsv = sqlstm.sqhstv;
#line 421 "esemin0x.pc"
        sqlstm.sqphsl = sqlstm.sqhstl;
#line 421 "esemin0x.pc"
        sqlstm.sqphss = sqlstm.sqhsts;
#line 421 "esemin0x.pc"
        sqlstm.sqpind = sqlstm.sqindv;
#line 421 "esemin0x.pc"
        sqlstm.sqpins = sqlstm.sqinds;
#line 421 "esemin0x.pc"
        sqlstm.sqparm = sqlstm.sqharm;
#line 421 "esemin0x.pc"
        sqlstm.sqparc = sqlstm.sqharc;
#line 421 "esemin0x.pc"
        sqlstm.sqpadto = sqlstm.sqadto;
#line 421 "esemin0x.pc"
        sqlstm.sqptdso = sqlstm.sqtdso;
#line 421 "esemin0x.pc"
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
#line 421 "esemin0x.pc"
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
#line 421 "esemin0x.pc"
}

#line 426 "esemin0x.pc"

        if (sqlca.sqlcode == NOT_FOUND)
        {
            fprintf(afp, "Warning: No horizontal gain for antenna %s ",
                    vict.antenna);
            fprintf(afp, "at degree %d (%s, base no. %d)\n", 
                    o_degree, vict.system_id, vict.base_no);
        }
        else
            vict.ant_gain = o_gain_in_db;
    }

/* commented out by Chen Yung
    return;
*/
/* changed by Chen Yung */
/* original: */
/*    return; */
    return(0);

sqlerr_rtn:
/* changed by Chen Yung */
/* original: */
/*    printf("%s\c", sqlca.sqlerrm.sqlerrmc); */
    printf("%s\n", sqlca.sqlerrm.sqlerrmc);
    fflush(afp);
    fclose(afp);
    exit(1);

}
