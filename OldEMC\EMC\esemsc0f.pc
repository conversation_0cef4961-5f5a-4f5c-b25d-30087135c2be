/**********************************************************************/
/*                                                                    */
/*    Module Name   :  main (esemsc0f.pc)                             */
/*    Date Written  :  May, 1993                                      */
/*    Author        :  <PERSON><PERSON> <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  esemfp0f.c through 'system' statement          */  
/*                                                                    */
/*    Parameters    :  user id.                                       */
/*                     user password                                  */
/*                     EMC user id.                                   */
/*                     printer id.                                    */
/*                                                                    */
/*    Called Modules:  screen functions from 'screen.c'               */
/*                     (centre_msg, disp_err, disp_space)             */
/*                     screen function from 'cursesX'                 */
/*                     user_login and user_logout (login.pc)          */
/*                                                                    */
/*    Purpose       :  Accept user input frequency band, weighting    */
/*                     factor and business code, then print out user- */
/*                     specified no. of channels within the selected  */
/*                     frequency band in ascending order of figure    */
/*                     sharing (figure of sharing is calculated based */
/*                     on user-supplied formula).                     */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      May-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <time.h>
#include <curses.h>
#include "../include/macros.h"
#include "../include/emc.h"


#define  SLEEP_SEC      2

#define  MAX_FLD_LEN    10     /* max. field length of input field */

#define  LOGIN_OK       0

/* ORACLE status */
#define  FOUND          0
#define  NOT_FOUND      1403

#define  NEXT_START_PT  0     /* field index at which user starts his   */
                              /* input for next frequency pre-selection */

#define  MAX_BAND_NO           100   /* max. no. of bands                 */
#define  MAX_WEIGHT_NO         61    /* max. no. of weighting factor sets */
#define  MAX_BAND_LEN          77
#define  MAX_WEIGHT_LEN        61
#define  MAX_CHANNEL_NO        600   /* max. no. of channels in a band     */
#define  MAX_SHARER_NO         20    /* max. no. of sharers in a channel   */

#define  MAX_SUBDIST_NO        8     /* max. no. of sub-districts per line */
#define  MAX_SUBDIST_NO_2      (MAX_SUBDIST_NO * 2)
                               /* total no. of sub-districts for a system  */

#define  LINES_PER_PAGE        60

#define  FREQ_EPSILON          0.0005

#define  TRIGGER_WIN_MSG       "Press F2 for window select"

#include "../include/winscrn.h"


char    *band[MAX_BAND_NO];
char    *wset[MAX_WEIGHT_NO];

char    *band_head[] = 
{
" BAND CODE     BASE TX LOW     BASE TX HIGH     BASE RX LOW     BASE RX HIGH",
"                  (MHz)           (MHz)            (MHz)           (MHz)    ",
" =========     ===========     ============     ===========     ============",
(char *)NULL
};
            
char    *wset_head[] = 
{
" SET NO.      CATEGORY      WEIGHTING      REMARK           ",
" =======      ========      =========      ===============  ",
(char *)NULL
};
            
/* Report line details */
struct tag
{
    char   system_id[MAX_SHARER_NO][14];
    char   subdistrict_line1[MAX_SHARER_NO][MAX_SUBDIST_NO * 5];
    char   subdistrict_line2[MAX_SHARER_NO][MAX_SUBDIST_NO * 5];
    char   buss_code[MAX_SHARER_NO][5];
    char   assign_date[MAX_SHARER_NO][9];
    char   measure_date[9];
    int    no_of_mobiles;
    double tx_freq;
    double rx_freq;
    float  traffic;
    float  share_figure;
    int    sharer_cnt;
    int    same_buss_cnt;
};

typedef struct tag  FREQ_PRESEL;
FREQ_PRESEL  *t_rec[MAX_CHANNEL_NO];

/* field validation function declarations */
int    chk_band_code();
int    chk_buss_code();
int    chk_wgt_set();

/* For field definitions, see ../include/winscrn.h */
FIELD item[] = 
{
18,12,0,    band,band_head,TRUE,STRING, TRUE, 9, 0, NEW,"         ",   "",0,FALSE,chk_band_code,
18,43,DUMMY,NULL_AR,NULL_AR,TRUE,STRING, TRUE, 4, 0, NEW,"    ",   "",0,FALSE,chk_buss_code,
18,73,1,    wset,wset_head,TRUE,INTEGER,TRUE, 2, 0, NEW,"00", "",0,FALSE,chk_wgt_set,
19,33,DUMMY,NULL_AR,NULL_AR,TRUE,INTEGER,TRUE, 3, 0, NEW,"000","",0,FALSE,NULL_FUNC,
-1,-1,DUMMY,NULL_AR,NULL_AR,TRUE,DUMMY,  TRUE, 0, 0, NEW,"",   "",0,FALSE,NULL_FUNC
};

FIELD_WIN fld_win[] = 
{
NULL_WIN, 4, 1,  12, MAX_BAND_LEN,
NULL_WIN, 4, 8, 12, MAX_WEIGHT_LEN,
NULL_WIN, DUMMY,DUMMY,DUMMY,DUMMY
};

char   *prog_id = "emsc0f_01";
char   *screen_head = "SHARED CHANNEL ALLOCATION REPORT GENERATION";

char   *getenv();

char   passwd[20];
char   emc_uid[20];
char   buss_code[5];
char   band_code[10];
int    no_of_channels;
int    max_sharer_cnt = DUMMY;
int    max_buss_cnt = DUMMY;

extern char msg[];

EXEC SQL BEGIN DECLARE SECTION;

    char     o_sys_category;
    VARCHAR  o_sys_type[3];
    VARCHAR  o_sys_no[8];
    VARCHAR  o_sys_suffix[4];
    double   o_tx_freq_hi;
    double   o_tx_freq_lo;
    double   o_rx_freq_hi;
    double   o_rx_freq_lo;
    double   o1_tx_freq_hi;
    double   o1_tx_freq_lo;
    double   o1_rx_freq_hi;
    double   o1_rx_freq_lo;
    double   o_tx_freq;
    double   o_rx_freq;
    float    o_channel_spacing;
    VARCHAR  o_band_code[10];
    VARCHAR  o_buss_code[5];
    VARCHAR  o_subdistrict[4];
    VARCHAR  o_assign_date[9];
    VARCHAR  o_ac_cancel_date[10];
    VARCHAR  o_sys_date[9];
    VARCHAR  o_date[10];
    VARCHAR  o_measure_date[9];
    VARCHAR  o_remark[16];
    int      o_no_of_channels;
    int      o_base_no;
    int      o_weight_set;
    int      o_category;
    float    o_weight;
    float    o_weight_traffic;
    float    o_weight_sharer;
    float    o_weight_buss;
    float    o_load_per_mobile;
    int      o_mobile_cnt;
    char     o_dummy;

EXEC SQL END DECLARE SECTION;

EXEC SQL INCLUDE SQLCA;


main(argc, argv)
int    argc;
char   **argv;
{
    char    ns_fname[150];
    char    *confirm_msg = "** CONFIRM [Y/N]? ";
    char    err_msg[80];
    char    answer;
    FILE    *sfp;
    int     term_code;
    int     item_cnt;                   /* total no. of items               */
    int     last_item;                  /* index of last filled-up item     */

    int     x_pos, y_pos;               /* current cursor position          */
    int     curr_pos;                   /* current position of input string */
    int     loop;                       /* TRUE if entry continue on        */
                                        /* current item                     */
    int     modified = FALSE;           /* TRUE when current item has been  */
                                        /* modified                         */
    int     token;                      /* user-input character             */
    int     status = !(QUIT);
    int     direction;                  /* field shuttle direction          */
    int     select_by_window = FALSE;
    int     err_flag = FALSE;
    int     y1, y2, x1, x2;             /* co-ordinates of upper-left       */
                                        /* corner and lower-right corner of */
                                        /* selection window                 */
    int     win_cnt;                    /* no. of window lines              */
    int     entry;                      /* entry selected from selection    */
                                        /* window                           */
    int     page_cnt = 1;
    int     i;

    register int     j;
    struct tm  *tt;
char s[80];


    EXEC SQL WHENEVER NOT FOUND CONTINUE;

    initscr();
    raw();
    noecho();
    clear();
    keypad(stdscr, TRUE); 

    if (argc != 6)
    {
       sprintf(err_msg, "Usage: esemsc0f user_id passwd emc_uid -P printer_id");
       goto force_exit;
    }

    if (strcmp(argv[4], "-P"))
    {
       sprintf(err_msg, "Usage: esemsc0f user_id passwd emc_uid -P printer_id");
       goto force_exit;
    }
    
    strcpy(user_id, argv[1]);
    strcpy(passwd, argv[2]);

    if (user_login(user_id, passwd, err_msg) != LOGIN_OK)
    {
        strip_blank(err_msg, TRAIL);
        strcat(err_msg, "  ");
        strcat(err_msg, user_id);
        strcat(err_msg, passwd);
        goto force_exit;
    }

    EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn;

    strcpy(emc_uid, argv[3]);
    strcpy(printer_id, argv[5]);
    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);

    for (i = 0; item[i].xpos != -1; i++)
        ;

    item_cnt = i;

    emc_dir = getenv("EMC");
    disp_heading(prog_id, screen_head, sys_date);
    disp_entry_scn(item_cnt, item_cnt, NEW_SCREEN);
 
    for (i = 0; status != QUIT; )
    {
        x_pos = item[i].xpos; 
        y_pos = item[i].ypos;

        if (with_window(&item[i]))
            centre_msg(16, TRIGGER_WIN_MSG, (A_BOLD|A_BLINK), A_REVERSE);
        else
            clear_msg(16);

         if (select_by_window)
             select_by_window = FALSE;

        /**********************************************************/
        /* if data type of current field is sign field, then skip */
        /* the 1st position of current string and increment       */
        /* screen x-coordinate by 1, else keep 1st position       */
        /**********************************************************/
        if (is_sign(&item[i]))
        {
            curr_pos = 1;
            x_pos++;
        }
        else
            curr_pos = 0;

        move(y_pos, x_pos);
        loop = TRUE;

        for (; loop == TRUE;)
        {
            refresh();
            token = getch();

            attroff(A_REVERSE);
            getyx(stdscr, y_pos, x_pos);
            if (err_flag == TRUE)
            {
                clear_err();
                err_flag = FALSE;
            }
            move(y_pos, x_pos);
            attron(A_REVERSE);

            /**************************************/
            /* Function key definitions:          */
            /*    KEY_F(1)  - quit session        */
            /*    KEY_F(2)  - trigger sub-window  */
            /*    KEY_F(3)  - refresh screen      */
            /*    KEY_F(4)  - clear field         */
            /*    TAB,                            */
            /*    NEWLINE,                        */
            /*    KEY_DOWN  - next field          */
            /*    CTRL_B,                         */
            /*    KEY_UP    - next field          */
            /*    KEY_LEFT  - next char position  */
            /*    KEY_RIGHT - next char position  */
            /*    DELETE,                         */
            /*    BACKSPACE - delete current char */
            /*                and back 1 position */
            /**************************************/

            switch (token)
            {
                case KEY_F(1):
                    status = QUIT;
                    loop = FALSE;
                    break;

                case KEY_F(2):
                    if (!(with_window(&item[i])))
                    {
                        beep();
                        break;
                    }
                    hide_cursor();
                    clear_msg(16);
                    if (item[i].first_select == TRUE)
                    {
                        int    status;

                        status = prepare_win_lines(&fld_win[item[i].win_idx],
                                                   item[i].disp_arr,
                                                   &win_cnt,
                                                   err_msg);
                        if (status == SELECT_ERROR)
                            goto force_exit;

                    }

                    y1 = fld_win[item[i].win_idx].win_ypos;
                    x1 = fld_win[item[i].win_idx].win_xpos;
                    y2 = y1 + fld_win[item[i].win_idx].win_lines - 1;
                    x2 = x1 + fld_win[item[i].win_idx].win_len - 1;
                    if (item[i].first_select == TRUE)
                    {
                        item[i].first_select = FALSE;
                        entry = select_entry(fld_win[item[i].win_idx].wp, TRUE,
                                             y1,x1,y2,x2, item[i].disp_arr,
                                             item[i].heading,
                                             fld_win[item[i].win_idx].win_len, 
                                             FALSE, win_cnt, err_msg);
                    }
                    else
                        entry = select_entry(fld_win[item[i].win_idx].wp, TRUE,
                                             y1,x1,y2,x2, item[i].disp_arr,
                                             item[i].heading,
                                             fld_win[item[i].win_idx].win_len, 
                                             TRUE, win_cnt, err_msg);

                    if (entry == SELECT_ERROR)
                        goto force_exit;

                    werase(fld_win[item[i].win_idx].wp);
                    clear_border(y1, x1, y2, x2);
                    attrset(A_REVERSE);
                    show_cursor();

                    if (entry != EXIT)
                    {
                        sscanf(item[i].disp_arr[entry], "%s", item[i].curr_str);
                        disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
                        mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
                        item[i].curr_len = strlen(item[i].curr_str);
                        item[i].state = MODIFIED;
                    }

                    select_by_window = TRUE;
                    loop = FALSE;
                    direction = FORWARD;
                    break;

                case KEY_F(3):
                    attroff(A_REVERSE);
                    refresh_screen(item_cnt, i);
                    if (with_window(&item[i]))
                        centre_msg(16, TRIGGER_WIN_MSG, 
                                   (A_BOLD|A_BLINK), A_REVERSE);
                    move(y_pos, x_pos);
                    break; 

                case KEY_F(4):
                    init_field(&item[i], &curr_pos);
                    loop = FALSE; 
                    direction = RESTART; 
                    break; 

                case KEY_UP:
                case CTRL_B:
                    if (i == 0)
                        beep();
                    else
                    {
                        loop = FALSE;
                        direction = BACKWARD;
                    }
                    break;

                case KEY_DOWN:
                case NEWLINE:
                case TAB:
/*
sprintf(s," i len req: %d %d %d", i, item[i].curr_len, item[i].required);
mvaddstr(23,0,s);
                    if ((item[i].state == NEW) && (item[i].required))
                    if ((is_sign(&item[i]) && (item[i].curr_len == 1))
                     || (!is_sign(&item[i]) && empty(&item[i]))
                    && (item[i].required))
*/
                    /*********************************************************/
                    /* beep user if he wants to skip an input-required field */
                    /*********************************************************/
                    if (is_sign(&item[i]))
                    {
                        if ((item[i].curr_len == 1) && (item[i].required))
                        {
                            if (with_window(&item[i]))
                                beep();
                            else
                            {
                                err_flag = TRUE;
                                disp_err(INPUT_REQ_MSG);
                                attron(A_REVERSE);
                            }
                            break;
                        }
                    }
                    else
                        if (empty(&item[i]) && (item[i].required))
                        {
                            if (with_window(&item[i]))
                                beep();
                            else
                            {
                                err_flag = TRUE;
                                disp_err(INPUT_REQ_MSG);
                                attron(A_REVERSE);
                            }
                            break;
                        }

                    if (empty(&item[i]) 
                    ||  (is_sign(&item[i]) && (item[i].curr_len == 1)))
                        strcpy(item[i].curr_str, item[i].init_str);

                    loop = FALSE;
                    direction = FORWARD;
                    break;

                case KEY_LEFT:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                    }
                    break;

                case KEY_RIGHT:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* right boundary of current field  */
                    /************************************/
                    if (x_pos - item[i].xpos == item[i].curr_len)
                        beep();
                    else
                    {
                        x_pos++; curr_pos++;
                    }
                    break;

                case BACKSPACE:
                case DELETE:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                        if (is_float(&item[i]) 
                        && (item[i].curr_str[curr_pos] == DOT))
                            item[i].has_dot = FALSE;
                        move(y_pos, x_pos);
                        addch(BLANK);
                        modified = TRUE;
/*
                        if (x_pos == item[i].xpos)
                            item[i].state = NEW;
                        else
                            item[i].state = MODIFIED;
*/
                    }
                    break;

                case DOT:

                    /******************************************************/
                    /* beep user if data type of current field is integer */
                    /******************************************************/
                    if (is_int(&item[i]))
                    {
                        beep();
                        break;
                    }


                    /********************************************************/
                    /* beep user if data type of current field is float and */
                    /* user has already input one dot                       */
                    /********************************************************/
                    if (is_float(&item[i]))
                        if (item[i].has_dot)
                        {
                            beep();
                            break;
                        }
                        else
                            item[i].has_dot = TRUE;

                    if (item[i].state == NEW)
                        if (is_sign(&item[i]))
                        {
                            if (curr_pos == 1)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len - 1);
                                move(y_pos, x_pos);
                            }
                        }
                        else
                            if (curr_pos == 0)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len);
                                move(y_pos, x_pos);
                            }

                    addch(token);
                    item[i].curr_str[curr_pos] = token;
                    x_pos++; curr_pos++;
                    modified = TRUE;
                    item[i].state = MODIFIED;

                    if (x_pos - item[i].xpos == item[i].fld_len)
                    {
                        item[i].curr_str[item[i].fld_len] = '\0';
                        item[i].curr_len = item[i].fld_len;
                        loop = FALSE;
                        direction = FORWARD;
                    }

                    break;

                default:
                    if ((!isalnum(token)) && (token != MINUS) 
                    &&  (token != PLUS))
                    {
                        beep();
                        break;
                    }

                    if (!isdigit(token))
                    {
/*
mvaddstr(23, 0, "not digit");
*/
                        if (is_sign(&item[i]))
                        {
                            if ((token != MINUS) && (token != PLUS))
                            {
                                beep();
                                break;
                            }
                            else
                                if (curr_pos > 1)
                                {
                                    beep();
                                    break;
                                }
                        }
                        else
/*
sprintf(s, "should not be integer: %d %d %s", i, item[i].type, item[i].init_str);
mvaddstr(22, 0, s);
*/
                            if ((item[i].type != CHAR) 
                            &&  (item[i].type != STRING))
                            {
                                beep();
                                break;
                            }
                            else
                                if (isalpha(token))
                                    token = toupper(token); 
                    }
                    else
                        if (item[i].state == NEW)
                            if (is_sign(&item[i]))
                            {
                                if (curr_pos == 1)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len-1);
                                    move(y_pos, x_pos);
                                }
                            }
                            else
                                if (curr_pos == 0)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len);
                                    move(y_pos, x_pos);
                                }

                    if (((token == MINUS) || (token == PLUS)) 
                    &&  is_sign(&item[i]))
                    {
                        x_pos--; curr_pos--;  /* because we don't want to  */
                                              /* move cursor to 1 position */
                                              /* this statement is used    */
                                              /* to complement the         */
                                              /* 'x_pos++; curr_pos++'     */
                                              /* a few lines below         */
                        if (token == MINUS)
                        {
                            item[i].curr_str[0] = MINUS;
                            move(y_pos, x_pos);
                            addch(MINUS);
                        }
                            
                    }
                    else
                    {
                        item[i].curr_str[curr_pos] = token;
                        addch(token);
                    }

/*
sprintf(s, "modified");
mvaddstr(22, 0, s);
*/
                    if ((token != MINUS) && (token != PLUS))
                        item[i].state = MODIFIED;

                    modified = TRUE;
                    x_pos++; curr_pos++;

                    if (x_pos - item[i].xpos == item[i].fld_len)
                    {
                        item[i].curr_str[item[i].fld_len] = '\0';
                        item[i].curr_len = item[i].fld_len;
                        loop = FALSE;
                        direction = FORWARD;
                    }
                
                    break;
            }

            refresh();
            if (loop == FALSE)
            {
                if (!select_by_window)
                {
                    int    (*check_item)();

                    check_item = item[i].validate;
                    if (check_item != (int(*)())NULL)
                    {
                        if ((direction != BACKWARD) && (!empty(&item[i])))
                            if ((*check_item)(&i, err_msg) == ERROR)
                            {
                                err_flag = TRUE;
                                disp_err(err_msg);
                                attron(A_REVERSE);
                                loop = FALSE;
                                direction = RESTART;
                            }
                    }

/*
                    for (j = 0; j < item_cnt; j++)
                        if (is_sign(&item[j]))
                        {
                            if ((item[j].curr_len == 1) 
                            &&  (item[j].required == TRUE))
                            {
                                direction = RESTART;
                                err_flag = TRUE;
                                disp_err(INPUT_REQ_MSG);
                                attron(A_REVERSE);
                                i = j;
                                break;
                            }
                        }
*/
                } 

                switch (direction)
                {
                    case FORWARD:
                        i++;
                        break;
                    case BACKWARD:
                        i--;
                        break;
                    case RESTART:
                        break;
                }

                continue;
            }
            
            if (modified && (!select_by_window) && (!err_flag))
            {
                int    len = x_pos - item[i].xpos;
   
                if ((item[i].curr_len < len) || is_delete(token))
                {
                    item[i].curr_len = len;
                    item[i].curr_str[item[i].curr_len] = '\0';
                }
/*
disp_space(22,0,80);
sprintf(s,"i mode: %d %d", i, tx_mode);
mvaddstr(22,0,s);
*/

                modified = FALSE;
            }
  
            move(y_pos, x_pos);
        }

        if ((status != QUIT) && (i == item_cnt) && (!err_flag))
        {
            attrset(A_BOLD);
            mvaddstr(19, 47, confirm_msg);
            refresh();
            attrset(A_REVERSE);
            getyx(stdscr, y_pos, x_pos);
            x_pos += 2;
            disp_space(y_pos, x_pos, 1);
            attroff(A_REVERSE);
            move(y_pos, x_pos);
            refresh();
            read_str(&answer, "", 1, 0, 10, &term_code, A_REVERSE);
            answer = toupper(answer);
            disp_space(19, 47, 23);
            attron(A_REVERSE);
            if (answer == 'Y')
            {
                strcpy(band_code, item[0].curr_str);
                no_of_channels = atoi(item[3].curr_str);
                if (chk_no_of_channels(err_msg) == ERROR)
                {
                    disp_err(err_msg);
                    err_flag = TRUE;
                    i = 3;
                }
                else
                {
                    i = NEXT_START_PT;
    
                    hide_cursor();
                    centre_msg(23, "Processing ...", A_BLINK, A_REVERSE);
                    strcpy(buss_code, item[1].curr_str);
                    o_weight_set = atoi(item[2].curr_str);
                    if (print_schannel_rpt(err_msg) == NOT_FOUND)
                    {
                        disp_err(err_msg);
                        err_flag = TRUE;
                    }

                    show_cursor();
                    if (!err_flag)    /* clear "Processing ..." */
                    {
                        attrset(A_NORMAL);
                        disp_space(23, 0, 80);
                        refresh();
                        attrset(A_REVERSE);
                    }
                }
                
                if (!err_flag)
                    for (j = NEXT_START_PT; j < item_cnt; j++)
                        init_field(&item[j], &curr_pos);
                beep();
            }
            else
                i = 0;
        }
    }

    attroff(A_BOLD);
    clear();
    endwin();

    user_logout();
    exit(0);


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);

force_exit:
    disp_err(err_msg); getch();
    clear(); show_cursor(); refresh();
    endwin();
    exit(1);
}


/**************************************************************/
/* Display new entry screen or refresh current screen to user */
/**************************************************************/

disp_entry_scn(item_cnt, curr_cnt, mode)
int    item_cnt;
int    curr_cnt;
int    mode;
{
    char    tmp_str[80];
    register int    i;

    attroff(A_REVERSE);
    mvaddstr(18, 0, "BAND CODE :");
    mvaddstr(18, 27, "BUSINESS CODE :");
    mvaddstr(18, 50, "WEIGHTING FACTOR SET :");
    mvaddstr(19, 0,  "NO. OF CHANNELS TO BE REPORTED :");
 
    sprintf(tmp_str, "%s",
"F1-Quit   F3-Refresh   F4-Clear field   UP-Prev   DOWN/<Tab>-next");
    centre_msg(21, tmp_str, A_BOLD, A_REVERSE);

    if (mode == REFRESH)
    {
        for (i = 0; i < curr_cnt; i++)
        {
            disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
            mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
        }
        for (i = curr_cnt; i < item_cnt; i++)
            if (item[i].state == NEW)
                mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);
            else
            {
                disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
                mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
            }
    }
    else
        for (i = 0; i < item_cnt; i++)
            mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);

    refresh();

}


/**************************************************************/
/* call 'disp_entry_scn' to refresh current screen to user    */
/**************************************************************/

refresh_screen(item_cnt, curr_cnt)
int item_cnt;
int curr_cnt;
{
    clear();
    disp_heading(prog_id, screen_head, sys_date);
    disp_entry_scn(item_cnt, curr_cnt, REFRESH);
}


/*************************************************/
/* initialise current field to its initial value */
/*************************************************/

init_field(p_item, curr_pos)
FIELD *p_item;
int   *curr_pos;
{
    mvaddstr(p_item->ypos, p_item->xpos, p_item->init_str);
    if (is_sign(p_item))
    {
        p_item->curr_len = *curr_pos = 1;
        p_item->curr_str[1] = '\0';
    }
    else
    {
        p_item->curr_len = *curr_pos = 0;
        p_item->curr_str[0] = '\0';
    }

    if (!disp_only(p_item))
    {
        p_item->state = NEW;
        if (is_float(p_item))
            p_item->has_dot = FALSE;
    }

    if (with_window(p_item) && (p_item->first_select == FALSE))
        p_item->first_select = TRUE;
}


/**********************************************************************/
/*  prepare window lines for display                                  */
/**********************************************************************/

prepare_win_lines(fw, disp_arr, win_cnt, err_msg)
FIELD_WIN  *fw;
char       **disp_arr;
int        *win_cnt;
char       *err_msg;
{
    int    i;
char s[80];

    if (disp_arr == band)
    {
        EXEC SQL DECLARE C03 CURSOR FOR
                 SELECT  BAND_CODE, BASE_TX_LOW, BASE_TX_HIGH,
                         BASE_RX_LOW, BASE_RX_HIGH
                 FROM    FREQUENCY_BAND
                 ORDER BY BASE_TX_LOW;

        EXEC SQL OPEN C03;

        EXEC SQL
             FETCH C03
             INTO  :o_band_code, :o_tx_freq_lo, :o_tx_freq_hi,
                   :o_rx_freq_lo, :o_rx_freq_hi;

        if (sqlca.sqlcode == NOT_FOUND)
        {
            sprintf(err_msg, "No band codes found in FREQUENCY_BAND");
            EXEC SQL CLOSE C03;
            return ERROR;
        }
    
        for (i = 0; i < MAX_BAND_NO; i++)
        {
            o_band_code.arr[o_band_code.len] = '\0';
    
            if ((disp_arr[i] = (char *) malloc(MAX_BAND_LEN)) == (char *) NULL)
            {
                sprintf(err_msg, "Fail to allocate memory for disp_arr");
                return ERROR;
            }
            sprintf(disp_arr[i]," %9s%5s%11.5lf%5s%11.5lf%6s%11.5lf%5s%11.5lf  ",
                    o_band_code.arr, "", o_tx_freq_lo, "", o_tx_freq_hi, "",
                    o_rx_freq_lo, "", o_rx_freq_hi);

            EXEC SQL
                 FETCH C03
                 INTO  :o_band_code, :o_tx_freq_lo, :o_tx_freq_hi,
                       :o_rx_freq_lo, :o_rx_freq_hi;
    
            if (sqlca.sqlcode == NOT_FOUND)
            {
                EXEC SQL CLOSE C03;
                break;
            }
        }
    }

    if (i == MAX_BAND_NO)
    {
        sprintf(err_msg, "No. of band codes > MAX_BAND_NO");
        disp_err(err_msg);
    }

    if (disp_arr == wset)
    {
        EXEC SQL DECLARE C04 CURSOR FOR
                 SELECT  WEIGHTING_SET, CATEGORY, WEIGHTING, REMARK
                 FROM    WEIGHTING_FACTOR
                 ORDER BY WEIGHTING_SET, CATEGORY;

        EXEC SQL OPEN C04;

        EXEC SQL
             FETCH C04
             INTO  :o_weight_set, :o_category, :o_weight, :o_remark;

        if (sqlca.sqlcode == NOT_FOUND)
        {
            sprintf(err_msg, "No weighting factor sets in WEIGHTING_FACTOR");
            EXEC SQL CLOSE C04;
            return ERROR;
        }
    
        for (i = 0; i < MAX_WEIGHT_NO; i++)
        {
            if ((disp_arr[i] = (char *)malloc(MAX_WEIGHT_LEN)) == (char *) NULL)
            {
                sprintf(err_msg, "Fail to allocate memory for disp_arr");
                return ERROR;
            }
            o_remark.arr[o_remark.len] = '\0';
            sprintf(disp_arr[i], "   %2d%12s%2d%11s%5.2f%8s%-15s  ",
                    o_weight_set, "", o_category, "", o_weight, 
                    "", o_remark.arr);
    
            EXEC SQL
                 FETCH C04
                 INTO  :o_weight_set, :o_category, :o_weight, :o_remark;
    
            if (sqlca.sqlcode == NOT_FOUND)
            {
                EXEC SQL CLOSE C04;
                break;
            }
        }
    }

    if (i == MAX_WEIGHT_NO)
    {
        sprintf(err_msg, "No. of weighting factor sets > MAX_WEIGHT_NO");
        disp_err(err_msg);
    }

    fw->wp = newwin(fw->win_lines, fw->win_len, fw->win_ypos, fw->win_xpos);
    keypad(fw->wp, TRUE);

    *win_cnt = i + 1;
    return OK;


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    beep(); sleep(SLEEP_SEC);
    clear(); refresh();
    endwin();
    exit(1);
}


/************************************************************************/
/* check that input frequency band code exists in FREQUENCY_BAND        */
/************************************************************************/

int    chk_band_code(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
/*    strcpy(o_band_code.arr, item[*curr_cnt].curr_str); 
    o_band_code.len = strlen(o_band_code.arr); */
	strcpy((char *)o_band_code.arr, item[*curr_cnt].curr_str);
    o_band_code.len = strlen((char *)o_band_code.arr);

/*
system("echo \"esemsc0f 1\" >> /tmp/debug");
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_band_code.arr );
system(msg);
*/

    EXEC SQL
         SELECT  'X'
         INTO    :o_dummy
         FROM    FREQUENCY_BAND
         WHERE   BAND_CODE = :o_band_code;

    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "Invalid band code");
        return ERROR;
    }

    return OK;


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    beep(); sleep(SLEEP_SEC);
    clear(); refresh();
    endwin();
    exit(1);
}


/************************************************************************/
/* check that input business code exists in BUSINESS                    */
/************************************************************************/

int    chk_buss_code(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
/*    strcpy(o_buss_code.arr, item[*curr_cnt].curr_str);
    o_buss_code.len = strlen(o_buss_code.arr); */
    strcpy((char *)o_buss_code.arr, item[*curr_cnt].curr_str);
    o_buss_code.len = strlen((char *)o_buss_code.arr);

/*
system("echo \"esemsc0f 2\" >> /tmp/debug");
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_buss_code.arr );
system(msg);
*/

    EXEC SQL
         SELECT  'X'
         INTO    :o_dummy
         FROM    BUSINESS
         WHERE   BUSS_CODE = :o_buss_code;

    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "Invalid business code");
        return ERROR;
    }

    return OK;


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    beep(); sleep(SLEEP_SEC);
    clear(); refresh();
    endwin();
    exit(1);
}


/************************************************************************/
/* check that input weighting factor set exists in WEIGHTING_FACTOR     */
/************************************************************************/

int    chk_wgt_set(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{

    o_weight_set = atoi(item[*curr_cnt].curr_str);

/*
system("echo \"esemsc0f 3\" >> /tmp/debug");
sprintf(msg, "echo \"%d\" >> /tmp/debug", o_weight_set );
system(msg);
*/

    EXEC SQL
         SELECT  'X'
         INTO    :o_dummy
         FROM    WEIGHTING_FACTOR
         WHERE   WEIGHTING_SET = :o_weight_set;

    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "Invalid weighting factor set");
        return ERROR;
    }

    return OK;


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    beep(); sleep(SLEEP_SEC);
    clear(); refresh();
    endwin();
    exit(1);
}


/************************************************************************/
/* check that input no. of channels to be printed is within the max.    */
/* no. of channels of the specified frequency band.                     */
/************************************************************************/

int    chk_no_of_channels(err_msg)
char    *err_msg;
{
    float  channel_width;
    int    band_channel_no;

/*    strcpy(o_band_code.arr, band_code);
    o_band_code.len = strlen(o_band_code.arr); */
    strcpy((char *)o_band_code.arr, band_code);
    o_band_code.len = strlen((char *)o_band_code.arr);

/*
system("echo \"esemsc0f 4\" >> /tmp/debug");
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_band_code.arr );
system(msg);
*/

    EXEC SQL
         SELECT  BASE_TX_LOW, BASE_TX_HIGH, BASE_RX_LOW, BASE_RX_HIGH,
                 CHANNEL_SPACING
         INTO    :o_tx_freq_lo, :o_tx_freq_hi, :o_rx_freq_lo, :o_rx_freq_hi,
                 :o_channel_spacing
         FROM    FREQUENCY_BAND
         WHERE   BAND_CODE = :o_band_code;

    
    if (sqlca.sqlcode == NOT_FOUND) 
    {
        sprintf(err_msg, "Fatal: band code (%s) not in FREQUENCY BAND",
                band_code);
        goto sqlerr_rtn;
    }

    /********************************************************************/
    /* get assigned date boundary (currently 3 months from system date) */
    /* for channel selection                                            */
    /********************************************************************/
/*    strcpy(o_sys_date.arr, sys_date);
    o_sys_date.len = strlen(o_sys_date.arr); */
    strcpy((char *)o_sys_date.arr, sys_date);
    o_sys_date.len = strlen((char *)o_sys_date.arr);
    EXEC SQL 
         SELECT TO_CHAR(ADD_MONTHS(TO_DATE(:o_sys_date, 'DD/MM/YY'), -3))
         INTO   :o_date
         FROM DUMMY;

    o_date.arr[o_date.len] = '\0';

    EXEC SQL
         SELECT COUNT(UNIQUE TX_FREQ)
         INTO   :o_no_of_channels
         FROM   ASSIGN_CH
         WHERE  TX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
         AND    CANCEL_DATE IS NULL
         AND    ASSIGN_DATE <= TO_DATE(:o_date);

    if (no_of_channels > o_no_of_channels)
    {
        sprintf(err_msg, "There are only %d assigned channels, so select a no. which is <= %d",
                o_no_of_channels, o_no_of_channels);
        return ERROR;
    }

    if (o_no_of_channels > MAX_CHANNEL_NO)
    {
        sprintf(err_msg, "No. of shared channels in band > %s, press any key to continue",
                MAX_CHANNEL_NO); 
        return ERROR;
    }
/*
    channel_width = o_tx_freq_hi - o_tx_freq_lo;
    band_channel_no = (int)(channel_width / o_channel_spacing) + 1;
    if (band_channel_no > no_of_channels)
    {
        sprintf(err_msg, "No. of channel to be printed must be < %d",
                band_channel_no);
        return ERROR;
    }
*/

    return OK;

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    beep(); sleep(SLEEP_SEC);
    clear(); refresh();
    endwin();
    exit(1);
}


/**********************************************************************/
/*  print shared channels report                                      */
/**********************************************************************/

print_schannel_rpt(err_msg)
char   *err_msg;
{
    char   sc_fname[120], err_fname[100];
    char   prev_sys_id[14];
    char   cmdline[150];
    double prev_tx_freq = (double)0;
    double prev_rx_freq = (double)0;
    FILE   *sfp, *efp;
    int    line_cnt = 0;
    int    page_cnt = 1;
/*
    int    sys_cnt = 0;
*/
    int    err_flag = FALSE;
    int    i;

    register int j = 0, k = 0, gap;
    FREQ_PRESEL  *tmp_rec;
char s[80];


    sprintf(sc_fname, "%s/schannel/%s.%s.%s", 
            emc_dir, emc_uid, o_buss_code.arr, band_code);
    if((sfp = fopen(sc_fname, "w")) == (FILE *) NULL)
    {
        sprintf(err_msg, "Fail to open %s", sc_fname);
        goto force_exit;
    }

    sprintf(err_fname, "%s/schannel/%s.err", emc_dir, emc_uid);
    if((efp = fopen(err_fname, "w")) == (FILE *) NULL)
    {
        sprintf(err_msg, "Fail to open %s", err_fname);
        goto force_exit;
    }

    /****************************/
    /* get weighting factor set */
    /****************************/
    EXEC SQL DECLARE C01 CURSOR FOR
         SELECT WEIGHTING
         FROM   WEIGHTING_FACTOR
         WHERE  WEIGHTING_SET = :o_weight_set
         ORDER BY CATEGORY;

    EXEC SQL OPEN C01;

    EXEC SQL
         FETCH C01
         INTO  :o_weight_traffic;
    EXEC SQL
         FETCH C01
         INTO  :o_weight_sharer;
    EXEC SQL
         FETCH C01
         INTO  :o_weight_buss;
         
    EXEC SQL CLOSE C01;


    /**************************************************/
    /* get frequency boundaries for channel selection */
    /**************************************************/
/*    strcpy(o_band_code.arr, band_code);
    o_band_code.len = strlen(o_band_code.arr); */
    strcpy((char *)o_band_code.arr, band_code);
    o_band_code.len = strlen((char *)o_band_code.arr);

/*
system("echo \"esemsc0f 5\" >> /tmp/debug");
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_band_code.arr );
system(msg);
*/

    EXEC SQL
         SELECT BASE_TX_LOW, BASE_TX_HIGH
         INTO   :o_tx_freq_lo, :o_tx_freq_hi
         FROM   FREQUENCY_BAND
         WHERE  BAND_CODE = :o_band_code;

    /********************************************************************/
    /* get assigned date boundary (currently 3 months from system date) */
    /* for channel selection                                            */
    /********************************************************************/
/*    strcpy(o_sys_date.arr, sys_date);
    o_sys_date.len = strlen(o_sys_date.arr); */
    strcpy((char *)o_sys_date.arr, sys_date);
    o_sys_date.len = strlen((char *)o_sys_date.arr);
    EXEC SQL 
         SELECT TO_CHAR(ADD_MONTHS(TO_DATE(:o_sys_date, 'DD/MM/YY'), -3))
         INTO   :o_date
         FROM DUMMY;

    o_date.arr[o_date.len] = '\0';

    EXEC SQL DECLARE C05 CURSOR FOR
         SELECT SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, TX_FREQ,
                NVL(RX_FREQ, 0.0), TO_CHAR(ASSIGN_DATE, 'DD/MM/YY'),
                NVL(TO_CHAR(CANCEL_DATE), '-')
         FROM   ASSIGN_CH
         WHERE  TX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
         AND    ASSIGN_DATE <= TO_DATE(:o_date)
         ORDER BY TX_FREQ, SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX;

    EXEC SQL OPEN C05;

    EXEC SQL
         FETCH C05
         INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
               :o_tx_freq, :o_rx_freq, :o_assign_date, :o_ac_cancel_date;

    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "No assigned channels found");
        EXEC SQL CLOSE C05;
        fclose(efp); fclose(sfp);
        return NOT_FOUND;
    }

    for (i = 0; i < MAX_CHANNEL_NO; i++)
        t_rec[i] = NULL;

    i = 0;
    prev_sys_id[0] = '\0';

    for (; ;)
    {
        o_sys_type.arr[o_sys_type.len] = '\0';
        o_sys_no.arr[o_sys_no.len] = '\0';
        o_sys_suffix.arr[o_sys_suffix.len] = '\0';

        /*****************************************************/
        /* if this is a cancelled channel, skip it           */
        /*****************************************************/
        if (o_ac_cancel_date.arr[0] != '-')
        {
            EXEC SQL
                 FETCH C05
                 INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
                       :o_tx_freq, :o_rx_freq, :o_assign_date, :o_ac_cancel_date;

            if (sqlca.sqlcode == NOT_FOUND)
            {
                if (t_rec[i] != (FREQ_PRESEL *)NULL)
                {
                    t_rec[i]->sharer_cnt = j;
                    t_rec[i]->same_buss_cnt = k;
                    max_sharer_cnt = max(max_sharer_cnt, t_rec[i]->sharer_cnt);
                    max_buss_cnt = max(max_buss_cnt, t_rec[i]->same_buss_cnt);
                    i++;
                }

                EXEC SQL CLOSE C05;
                break;
            }

            continue;
        }

        /*****************************************************/
        /* This is done only if a new channel is encountered */
        /*****************************************************/
        if ((abs(o_tx_freq - prev_tx_freq) > FREQ_EPSILON) 
        ||  (abs(o_rx_freq - prev_rx_freq) > FREQ_EPSILON))
        {
            o1_tx_freq_lo = o_tx_freq - FREQ_EPSILON;
            o1_tx_freq_hi = o_tx_freq + FREQ_EPSILON;
            o1_rx_freq_lo = o_rx_freq - FREQ_EPSILON;
            o1_rx_freq_hi = o_rx_freq + FREQ_EPSILON;

            if (abs(o_rx_freq) < FREQ_EPSILON)
            {
                EXEC SQL
                     SELECT COUNT(UNIQUE MOBILE_NO)
                     INTO   :o_mobile_cnt
                     FROM   MOBILE_CH
                     WHERE  RX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi
                     AND    TX_FREQ IS NULL
                     AND    CANCEL_DATE IS NULL;
            }
            else
            {
                EXEC SQL
                     SELECT COUNT(UNIQUE MOBILE_NO)
                     INTO   :o_mobile_cnt
                     FROM   MOBILE_CH
                     WHERE  RX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi
                     AND    TX_FREQ between :o1_rx_freq_lo and :o1_rx_freq_hi
                     AND    CANCEL_DATE IS NULL;
            }
/*
                 WHERE  TX_FREQ = :o_rx_freq
                 AND    RX_FREQ = :o_tx_freq
                 AND    CANCEL_DATE IS NULL;
sprintf(s,"mobile cnt: %d", o_mobile_cnt);
mvaddstr(17,0,s);refresh();getch();
*/

/*
            if ((o_mobile_cnt > 0) && (t_rec[i] == (FREQ_PRESEL *)NULL))
*/
            if (o_mobile_cnt > 0)
            {
                t_rec[i] = (FREQ_PRESEL *) malloc(sizeof(FREQ_PRESEL));
                if (t_rec[i] == (FREQ_PRESEL *) NULL)
                {
                    sprintf(err_msg, "Cannot allocate space for FREQ_PRESEL");
                    fclose(efp); fclose(sfp);
                    goto force_exit;
                }

/*
system("echo \"esemsc0f 6\" >> /tmp/debug");
sprintf(msg, "echo \"%f %f\" >> /tmp/debug", o1_tx_freq_lo, o1_tx_freq_hi );
system(msg);
*/

                EXEC SQL
                     SELECT LOAD_PER_MOBILE, TO_CHAR(LAST_UPDATE, 'DD/MM/YY')
                     INTO  :o_load_per_mobile, :o_measure_date
                     FROM   CHANNEL_TRAFFIC
                     WHERE  TX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi;
/*
disp_space(17, 0, 80);
sprintf(s,"traffic: %d", o_load_per_mobile);
mvaddstr(17,0,s);refresh();getch();
disp_space(17, 0, 80);
*/
                if (sqlca.sqlcode == NOT_FOUND)
                {
                    o_load_per_mobile = .005;
/*                    strcpy(o_measure_date.arr, "--------"); */
                    strcpy((char *)o_measure_date.arr, "--------");
                }
                else
                    o_measure_date.arr[o_measure_date.len] = '\0';

/*                strcpy(t_rec[i]->measure_date, o_measure_date.arr); */
                strcpy(t_rec[i]->measure_date, (char *)o_measure_date.arr);
                t_rec[i]->tx_freq = o_tx_freq;
                t_rec[i]->rx_freq = o_rx_freq;
                t_rec[i]->no_of_mobiles = o_mobile_cnt;
                t_rec[i]->traffic = o_load_per_mobile * o_mobile_cnt;
                prev_tx_freq = o_tx_freq;
                prev_rx_freq = o_rx_freq;
            }
            else
            {
                err_flag = TRUE;
                fprintf(efp, "No mobile channel for %lf/%lf\n", 
                        o_tx_freq, o_rx_freq);
            }
        }

        if (t_rec[i] != (FREQ_PRESEL *)NULL)
        {
            register int    l;
/*
sprintf(s,"%d %lf %lf %d %f     ", i, o_tx_freq, o_rx_freq, o_mobile_cnt, o_load_per_mobile);
mvaddstr(23,0,s);refresh();getch();
sprintf(s,"system id: %s", t_rec[i]->system_id[j]);
mvaddstr(17,0,s);refresh();getch();
disp_space(17, 0, 80);
sprintf(s,"buss code: %s", t_rec[i]->buss_code[j]);
mvaddstr(17,0,s);refresh();getch();
disp_space(17, 0, 80);
*/
            o1_tx_freq_lo = o_tx_freq - FREQ_EPSILON;
            o1_tx_freq_hi = o_tx_freq + FREQ_EPSILON;

            if (abs(o_rx_freq) < FREQ_EPSILON)
            {
                EXEC SQL DECLARE C060 CURSOR FOR
                     SELECT BASE_NO
                     FROM   BASE_CH
                     WHERE  TX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi
                     AND    RX_FREQ IS NULL
                     AND    SYS_CATEGORY = :o_sys_category
                     AND    SYS_TYPE = :o_sys_type
                     AND    SYS_NO = :o_sys_no
                     AND    SYS_SUFFIX = :o_sys_suffix;

                EXEC SQL OPEN C060;
                EXEC SQL FETCH C060 
                     INTO   :o_base_no;
            }
            else
            {
                o1_rx_freq_lo = o_rx_freq - FREQ_EPSILON;
                o1_rx_freq_hi = o_rx_freq + FREQ_EPSILON;
/*
                EXEC SQL DECLARE C061 CURSOR FOR
                     SELECT BASE_NO
                     FROM   BASE_CH
                     WHERE  TX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi
                     AND    RX_FREQ between :o1_rx_freq_lo and :o1_rx_freq_hi
                     AND    SYS_CATEGORY = :o_sys_category
                     AND    SYS_TYPE = :o_sys_type
                     AND    SYS_NO = :o_sys_no
                     AND    SYS_SUFFIX = :o_sys_suffix;
*/
                EXEC SQL DECLARE C061 CURSOR FOR
                     SELECT X.BASE_NO
                     FROM   BASE_CH X
                     WHERE  X.SYS_CATEGORY = :o_sys_category
                     AND    X.SYS_TYPE = :o_sys_type
                     AND    X.SYS_NO = :o_sys_no
                     AND    X.SYS_SUFFIX = :o_sys_suffix
                     AND    
                     (   (    X.TX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi
                          AND X.RX_FREQ between :o1_rx_freq_lo and :o1_rx_freq_hi)
                      OR (    X.TX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi
                          AND X.RX_FREQ IS NULL
                          AND EXISTS
                              (SELECT * 
                               FROM   BASE_CH Y
                               WHERE  Y.SYS_CATEGORY = :o_sys_category
                               AND    Y.SYS_TYPE = :o_sys_type
                               AND    Y.SYS_NO = :o_sys_no
                               AND    Y.SYS_SUFFIX = :o_sys_suffix
                               AND    Y.RX_FREQ between :o1_rx_freq_lo and :o1_rx_freq_hi
                               AND    Y.TX_FREQ IS NULL)));

                EXEC SQL OPEN C061;
                EXEC SQL FETCH C061 
                     INTO   :o_base_no;
            }

            if (sqlca.sqlcode == NOT_FOUND)
            {
                EXEC SQL
                     FETCH C05
                     INTO  :o_sys_category, :o_sys_type, :o_sys_no,
                           :o_sys_suffix, :o_tx_freq, :o_rx_freq,
                           :o_assign_date, :o_ac_cancel_date;

                if (sqlca.sqlcode == NOT_FOUND)
                {
                    if (t_rec[i] != (FREQ_PRESEL *)NULL)
                    {
                        t_rec[i]->sharer_cnt = j;
                        t_rec[i]->same_buss_cnt = k;
                        max_sharer_cnt = max(max_sharer_cnt, t_rec[i]->sharer_cnt);
                        max_buss_cnt = max(max_buss_cnt, t_rec[i]->same_buss_cnt);
                        i++;
                    }

                    EXEC SQL CLOSE C05;
                    break;
                }

                continue;
            }

            t_rec[i]->subdistrict_line2[j][0] = '\0'; 

            for (l = 0; l < MAX_SUBDIST_NO_2; )
            {
/*
sprintf(s,"base no: %d", o_base_no);
mvaddstr(23,0,s);refresh();getch();
*/

/*
system("echo \"esemsc0f 7\" >> /tmp/debug");
sprintf(msg, "echo \"%c %s %s %s %d\" >> /tmp/debug",
o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr, o_base_no );
system(msg);
*/

                EXEC SQL
                     SELECT SUBDISTRICT
                     INTO   :o_subdistrict
                     FROM   STATION
                     WHERE  SYS_CATEGORY = :o_sys_category
                     AND    SYS_TYPE = :o_sys_type
                     AND    SYS_NO = :o_sys_no
                     AND    SYS_SUFFIX = :o_sys_suffix
                     AND    BASE_NO = :o_base_no;

                if (sqlca.sqlcode == NOT_FOUND)
                { 
                    sprintf(err_msg, "Station not found for channel (%lf/%-lf) of system: %c-%s-%s-%s", 
                            o_tx_freq, o_rx_freq, o_sys_category,
                            o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr);
                    fclose(efp); fclose(sfp);
                    goto force_exit;
                }

                o_subdistrict.arr[o_subdistrict.len] = '\0';
                if (l < MAX_SUBDIST_NO)
                {
/*                    strcat(t_rec[i]->subdistrict_line1[j], 
                           o_subdistrict.arr); */
                    strcat(t_rec[i]->subdistrict_line1[j], (char *)o_subdistrict.arr);
                    pad_n_space(t_rec[i]->subdistrict_line1[j], 1);
                }
                else
                {
/*                    strcat(t_rec[i]->subdistrict_line2[j], 
                           o_subdistrict.arr); */
                    strcat(t_rec[i]->subdistrict_line2[j], (char *)o_subdistrict.arr);
                    pad_n_space(t_rec[i]->subdistrict_line2[j], 1);
                }

                l++;

                if (abs(o_rx_freq) < FREQ_EPSILON)
                {
                    EXEC SQL FETCH C060 
                         INTO   :o_base_no;
                }
                else
                {
                    EXEC SQL FETCH C061
                         INTO   :o_base_no;
                }

                if (sqlca.sqlcode == NOT_FOUND)
                    break;
            }

            if (abs(o_rx_freq) < FREQ_EPSILON)
            {
                EXEC SQL CLOSE C060;
            }
            else
            {
                EXEC SQL CLOSE C061;
            }

            if (l == MAX_SUBDIST_NO_2)
            {
                strcat(t_rec[i]->subdistrict_line1[j], "#");
/*
                sprintf(err_msg, "Too many stations for system %c-%s-%s-%s with channel %lf/%lf", 
                        o_sys_category, o_sys_type.arr, o_sys_no.arr, 
                        o_sys_suffix.arr, o_tx_freq, o_rx_freq);
                fclose(efp); fclose(sfp);
                goto force_exit;
*/
            }

/*
system("echo \"esemsc0f 8\" >> /tmp/debug");
sprintf(msg, "echo \"%c %s %s %s\" >> /tmp/debug",
o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr );
system(msg);
*/

            EXEC SQL 
                 SELECT BUSS_CODE
                 INTO   :o_buss_code
                 FROM   SYSTEM
                 WHERE  SYS_CATEGORY = :o_sys_category
                 AND    SYS_TYPE = :o_sys_type
                 AND    SYS_NO = :o_sys_no
                 AND    SYS_SUFFIX = :o_sys_suffix;

            if (sqlca.sqlcode == NOT_FOUND)
            { 
               sprintf(err_msg, "System not found: %c-%s-%s-%s", 
                       o_sys_category, o_sys_type.arr, o_sys_no.arr, 
                       o_sys_suffix.arr);
               fclose(efp); fclose(sfp);
               goto force_exit;
            }

            o_buss_code.arr[o_buss_code.len] = '\0';
 
            sprintf(t_rec[i]->system_id[j], "%c%s%s-%s", o_sys_category, 
                    o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr);
/*            strcpy(t_rec[i]->buss_code[j], o_buss_code.arr); */
            strcpy(t_rec[i]->buss_code[j], (char *)o_buss_code.arr);
            
            o_assign_date.arr[o_assign_date.len] = '\0';
/*            strcpy(t_rec[i]->assign_date[j], o_assign_date.arr); */
            strcpy(t_rec[i]->assign_date[j], (char *)o_assign_date.arr);
            
/*
sprintf(s,"i j system id: %d %d %s", i, j, t_rec[i]->system_id[j]);
mvaddstr(17,0,s);refresh();getch();
*/

/*            if (!strcmp(buss_code, o_buss_code.arr)) */
            if (!strcmp(buss_code, (char *)o_buss_code.arr))
                k++; 

            j++;
        }

        if (j == MAX_SHARER_NO)
        {
            for (prev_tx_freq = o_tx_freq; prev_tx_freq == o_tx_freq; )
            {
                prev_sys_id[0] = o_sys_category; prev_sys_id[1] = '\0';
/*                strcat(prev_sys_id, o_sys_type.arr);
                strcat(prev_sys_id, o_sys_no.arr);
                strcat(prev_sys_id, o_sys_suffix.arr); */
                strcat(prev_sys_id, (char *)o_sys_type.arr);
                strcat(prev_sys_id, (char *)o_sys_no.arr);
                strcat(prev_sys_id, (char *)o_sys_suffix.arr);

                EXEC SQL
                     FETCH C05
                     INTO  :o_sys_category, :o_sys_type, :o_sys_no,
                           :o_sys_suffix, :o_tx_freq, :o_rx_freq,
                           :o_assign_date, :o_ac_cancel_date;

                if (sqlca.sqlcode == NOT_FOUND)
                {
                    if (t_rec[i] != (FREQ_PRESEL *)NULL)
                    {
                        t_rec[i]->sharer_cnt = j;
                        t_rec[i]->same_buss_cnt = k;
                        max_sharer_cnt = max(max_sharer_cnt, t_rec[i]->sharer_cnt);
                        max_buss_cnt = max(max_buss_cnt, t_rec[i]->same_buss_cnt);
                        i++;
                    }

                    EXEC SQL CLOSE C05;
                    break;
                }
            }
        }
        else
        {
            prev_sys_id[0] = o_sys_category; prev_sys_id[1] = '\0';
/*            strcat(prev_sys_id, o_sys_type.arr);
            strcat(prev_sys_id, o_sys_no.arr);
            strcat(prev_sys_id, o_sys_suffix.arr); */
            strcat(prev_sys_id, (char *)o_sys_type.arr);
            strcat(prev_sys_id, (char *)o_sys_no.arr);
            strcat(prev_sys_id, (char *)o_sys_suffix.arr);

            EXEC SQL
                 FETCH C05
                 INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
                       :o_tx_freq, :o_rx_freq, :o_assign_date, :o_ac_cancel_date;

            if (sqlca.sqlcode == NOT_FOUND)
            {
                if (t_rec[i] != (FREQ_PRESEL *)NULL)
                {
                    t_rec[i]->sharer_cnt = j;
                    t_rec[i]->same_buss_cnt = k;
                    max_sharer_cnt = max(max_sharer_cnt, t_rec[i]->sharer_cnt);
                    max_buss_cnt = max(max_buss_cnt, t_rec[i]->same_buss_cnt);
                    i++;
                }

                EXEC SQL CLOSE C05;
                break;
            }
        }

        if (   ((abs(o_tx_freq - prev_tx_freq) >= FREQ_EPSILON) 
        ||  (abs(o_rx_freq - prev_rx_freq) >= FREQ_EPSILON))
        &&  (t_rec[i] != (FREQ_PRESEL *)NULL))
        {
            t_rec[i]->sharer_cnt = j;
            t_rec[i]->same_buss_cnt = k;
            max_sharer_cnt = max(max_sharer_cnt, t_rec[i]->sharer_cnt);
            max_buss_cnt = max(max_buss_cnt, t_rec[i]->same_buss_cnt);
/*
sprintf(s,"%lf %lf %f %d %d press ...", t_rec[i]->tx_freq, t_rec[i]->rx_freq,
        t_rec[i]->traffic, t_rec[i]->sharer_cnt, t_rec[i]->same_buss_cnt);
mvaddstr(22,0,s);refresh();getch();
*/
            j = k = 0; i++;

            if (i == MAX_CHANNEL_NO)
                break;
        }
        else
        {
            if ((abs(o_tx_freq - prev_tx_freq) < FREQ_EPSILON) 
            &&  (abs(o_rx_freq - prev_rx_freq) < FREQ_EPSILON))
            {
                char   curr_sys_id[14];
                int    break_this_loop = 0;
                int    break_whole_loop = 0;

               
                for (; (!break_this_loop) && (!break_whole_loop);)
                {
                    o_sys_type.arr[o_sys_type.len] = '\0';
                    o_sys_no.arr[o_sys_no.len] = '\0';
                    o_sys_suffix.arr[o_sys_suffix.len] = '\0';

                    curr_sys_id[0] = o_sys_category; curr_sys_id[1] = '\0';
/*                    strcat(curr_sys_id, o_sys_type.arr);
                    strcat(curr_sys_id, o_sys_no.arr);
                    strcat(curr_sys_id, o_sys_suffix.arr); */
                    strcat(curr_sys_id, (char *)o_sys_type.arr);
                    strcat(curr_sys_id, (char *)o_sys_no.arr);
                    strcat(curr_sys_id, (char *)o_sys_suffix.arr);

/*
sprintf(s, " %d BF c: %s %lf/%-lf  p: %s %lf/%-lf   ", i, curr_sys_id, o_tx_freq, o_rx_freq,
prev_sys_id, prev_tx_freq, prev_rx_freq);
mvaddstr(22, 0, s); refresh(); getch();
*/

                    /*****************************************************/
                    /* skip the assigned channels that are of the same   */
                    /* tx/rx freq. and system id. as the previous one    */
                    /*                                                   */
                    /* a system is allowed to have 2 channels with the   */
                    /* same tx/rx freq. as long as they have different   */
                    /* tone freq.                                        */
                    /*****************************************************/

                    if ((strcmp(prev_sys_id, curr_sys_id) == 0)
                    && ((abs(o_tx_freq - prev_tx_freq) < FREQ_EPSILON) 
                    &&  (abs(o_rx_freq - prev_rx_freq) < FREQ_EPSILON)))
                    {
                        EXEC SQL
                             FETCH C05
                             INTO  :o_sys_category, :o_sys_type, :o_sys_no,
                                   :o_sys_suffix, :o_tx_freq, :o_rx_freq, 
                                   :o_assign_date, :o_ac_cancel_date;

                        if (sqlca.sqlcode == NOT_FOUND)
                        {
                            if (t_rec[i] != (FREQ_PRESEL *)NULL)
                            {
                                t_rec[i]->sharer_cnt = j;
                                t_rec[i]->same_buss_cnt = k;
                                max_sharer_cnt = max(max_sharer_cnt, t_rec[i]->sharer_cnt);
                                max_buss_cnt = max(max_buss_cnt, t_rec[i]->same_buss_cnt);
                                i++;
                            }

                            EXEC SQL CLOSE C05;
                            break_whole_loop = 1;
                        }
                    }
                    else
                        break_this_loop = 1;
                }

                if (    ((abs(o_tx_freq - prev_tx_freq) >= FREQ_EPSILON) 
                    ||   (abs(o_rx_freq - prev_rx_freq) >= FREQ_EPSILON))
                && (t_rec[i] != (FREQ_PRESEL *)NULL))
                {
                    t_rec[i]->sharer_cnt = j;
                    t_rec[i]->same_buss_cnt = k;
                    max_sharer_cnt = max(max_sharer_cnt, t_rec[i]->sharer_cnt);
                    max_buss_cnt = max(max_buss_cnt, t_rec[i]->same_buss_cnt);
                    j = k = 0; i++;
                }

/*
sprintf(s, "%d AF c: %s %lf/%-lf  p: %s %lf/%-lf   ", i, curr_sys_id, o_tx_freq, o_rx_freq,
prev_sys_id, prev_tx_freq, prev_rx_freq);
mvaddstr(23, 0, s); refresh(); getch();
*/
                if (break_whole_loop)
                    break;
            }

        }

    }

clear_msg(17);attrset(A_REVERSE);

/*
sprintf(s,"cal traffic %d %d ...press", max_sharer_cnt, max_buss_cnt);
mvaddstr(17,0, s);refresh();getch();
sprintf(s,"sharer weight %f %f ...press", o_weight_sharer, o_weight_buss);
mvaddstr(17,0, s);refresh();getch();
*/

    /***********************************************************/
    /* Calculates figures of sharings according to information */
    /* stored in t_rec and the 'max_' values                   */
    /***********************************************************/
    for (j = 0; j < i; j++)
    {
        t_rec[j]->share_figure = t_rec[j]->traffic * o_weight_traffic;
        if (max_sharer_cnt != 0)
            t_rec[j]->share_figure = t_rec[j]->share_figure
                + t_rec[j]->sharer_cnt * o_weight_sharer / max_sharer_cnt;
        if (max_buss_cnt != 0)
            t_rec[j]->share_figure = t_rec[j]->share_figure
                + t_rec[j]->same_buss_cnt * o_weight_buss / max_buss_cnt;
/*
sprintf(s," %lf %f %f %f ...press", t_rec[j]->tx_freq, f1, f2, f3);
mvaddstr(22,0,s);refresh();getch();
*/
    }

    /**********************************************************************/
    /*  sort all channels in ascending order using shell sort             */
    /**********************************************************************/

/*
mvaddstr(17,0,"sort array ...press ");refresh();getch();
*/
    for (gap = i / 2; gap > 0; gap /= 2)
        for (j = gap; j < i; j++)
            for (k = j - gap; 
                    (k >= 0)
                 && (t_rec[k]->share_figure > t_rec[k+gap]->share_figure);
                 k -= gap)
            {
                tmp_rec      = t_rec[k];
                t_rec[k]     = t_rec[k+gap];
                t_rec[k+gap] = tmp_rec;
            }

/*
mvaddstr(17,0,"print result ...press");refresh();getch();
*/
    if (i > 0)
    {
        if (i > no_of_channels)
            i = no_of_channels;

        for (j = 0; j < i; j++)
            print_schannel_lines(sfp, t_rec[j], &line_cnt, &page_cnt);
/*
            print_schannel_lines(sfp, t_rec[j], &sys_cnt, &line_cnt, &page_cnt);
*/

        print_schannel_rpt_head(sfp, &page_cnt);
        fprintf(sfp, "\n\n\n\n\n\n");
        fprintf(sfp, "%38sNO. OF SHARED CHANNELS PRINTED                : %4d\n", "", i);
        fprintf(sfp, "%38sMAX. NO. OF SHARERS IN BAND               (M2): %4d\n", "", max_sharer_cnt);
        fprintf(sfp, "%38sMAX. NO. OF SAME BUSINESS SHARERS IN BAND (M3): %4d",
                "", max_buss_cnt);
        fprintf(sfp, "\n\n\n%5sNOTE\n", "");
        fprintf(sfp, "%5s====\n\n", "");
        fprintf(sfp, "%5sFIGURE OF SHARING = (A/1)*W1 + (B/M2)*W2 + (C/M3)*W3\n\n", "");
        fprintf(sfp, "%5sWHERE\n\n", "");
        fprintf(sfp, "%5sA = TRAFFIC LOADING OF A CHANNEL ", "");
        fprintf(sfp, "(MEASURED LOADING * NO. OF MOBILES, DEFAULT MEASURED LOADING = .005)\n");
        fprintf(sfp, "%5sB = NO. OF SHARERS IN A CHANNEL,\n", "");
        fprintf(sfp, "%5sC = NO. OF SHARERS IN A CHANNEL WITH ", "");
        fprintf(sfp, "SAME BUSINESS AS THE PROPOSED STATION,\n\n");
        fprintf(sfp, "%5sW1, W2 AND W3 ARE WEIGHTING FACTORS FOR ", "");
        fprintf(sfp, "A, B AND C RESPECTIVELY\n");
        fprintf(sfp, "%5sWHERE W1 = %5.2f, W2 = %5.2f, W3 = %5.2f",
                "", o_weight_traffic, o_weight_sharer, o_weight_buss);
    }

    fclose(efp); fclose(sfp);

    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", sc_fname);
    system(cmdline);

    if (err_flag)
    {
        sprintf(err_msg,
                "Assigned channels with no mobiles found, see error log");
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", err_fname);
        system(cmdline);
        return NOT_FOUND;
    }

    for (j = 0; j < i; j++)
        free(t_rec[j]);

    return OK;

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);

force_exit:
    disp_err(err_msg); getch();
    clear(); show_cursor(); refresh();
    endwin();
    EXEC SQL ROLLBACK RELEASE;
    exit(1);
}
             

/**********************************************************************/
/*  print shared channels report lines                                */
/**********************************************************************/

/*
print_schannel_lines(sfp, ptr, sys_cnt, line_cnt, page_cnt)
*/
print_schannel_lines(sfp, ptr, line_cnt, page_cnt)
FILE   *sfp;
FREQ_PRESEL *ptr;
int    *line_cnt, *page_cnt;
/*
int    *sys_cnt, *line_cnt, *page_cnt;
*/
{
    int    i;

    
/*
    if (*sys_cnt == 0)
*/
    if ((*line_cnt == 0) || (*line_cnt >= LINES_PER_PAGE))
    {
        print_schannel_rpt_head(sfp, page_cnt);
        print_schannel_col_head(sfp);
        (*page_cnt)++;
        *line_cnt = 17;
    }

    fprintf(sfp, "%11.5lf%2s%11.5lf%4s", ptr->tx_freq, "", ptr->rx_freq, "");

    (ptr->sharer_cnt == MAX_SHARER_NO) ?
        fprintf(sfp, "%5.2f%6s%3d*%3s%6.3f/%-3d%2s%s%2s%-14s %s %4s%2s%s\n", 
                ptr->share_figure, "", ptr->sharer_cnt, "", ptr->traffic, 
                ptr->no_of_mobiles, "", ptr->measure_date, "", 
                ptr->system_id[0], ptr->assign_date[0], ptr->buss_code[0], "",
                ptr->subdistrict_line1[0]):
        fprintf(sfp, "%5.2f%6s%3d%4s%6.3f/%-3d%2s%s%2s%-14s %s %4s%2s%s\n", 
                ptr->share_figure, "", ptr->sharer_cnt, "", ptr->traffic, 
                ptr->no_of_mobiles, "", ptr->measure_date, "", 
                ptr->system_id[0], ptr->assign_date[0], ptr->buss_code[0], "",
                ptr->subdistrict_line1[0]);

    if (ptr->subdistrict_line2[0][0] != '\0')
    {
        fprintf(sfp, "%98s%s\n", "", ptr->subdistrict_line2[0]);
        *line_cnt += 2;
    }
    else
        (*line_cnt)++;

/*
    *sys_cnt = (*sys_cnt + 1) % SYSTEM_PER_PAGE;
*/

    for (i = 1; i < ptr->sharer_cnt; i++)
    {
/*
        if (*sys_cnt == 0)
*/
        if (*line_cnt >= LINES_PER_PAGE)
        {
            print_schannel_rpt_head(sfp, page_cnt);
            print_schannel_col_head(sfp);
            (*page_cnt)++;

            fprintf(sfp, "%11.5lf%2s%11.5lf%4s", 
                    ptr->tx_freq, "", ptr->rx_freq, "");

            (ptr->sharer_cnt == MAX_SHARER_NO) ?
                fprintf(sfp, "%5.2f%6s%3d*%3s%6.3f/%-3d%2s%s%2s%-14s %s %4s%2s%s\n", 
                        ptr->share_figure, "", ptr->sharer_cnt, "",
                        ptr->traffic, ptr->no_of_mobiles, "",
                        ptr->measure_date, "", ptr->system_id[0], 
                        ptr->assign_date[0], ptr->buss_code[0], "",
                        ptr->subdistrict_line1[0]):
                fprintf(sfp, "%5.2f%6s%3d%4s%6.3f/%-3d%2s%s%2s%-14s %s %4s%2s%s\n", 
                        ptr->share_figure, "", ptr->sharer_cnt, "",
                        ptr->traffic, ptr->no_of_mobiles, "",
                        ptr->measure_date, "", ptr->system_id[0],
                        ptr->assign_date[0], ptr->buss_code[0], "",
                        ptr->subdistrict_line1[0]);

            if (ptr->subdistrict_line2[i][0] != '\0')
            {
                fprintf(sfp, "%98s%s\n", "", ptr->subdistrict_line2[i]);
                *line_cnt = 19;
            }
            else
                *line_cnt = 18;
        }
        else
        {
            fprintf(sfp, "%68s%-14s %s %4s%2s%s\n", "",
                    ptr->system_id[i], ptr->assign_date[i], 
                    ptr->buss_code[i], "", ptr->subdistrict_line1[i]);
            if (ptr->subdistrict_line2[i][0] != '\0')
            {
                fprintf(sfp, "%98s%s\n", "", ptr->subdistrict_line2[i]);
                *line_cnt += 2;
            }
            else
                (*line_cnt)++;
        }

/*
        *sys_cnt = (*sys_cnt + 1) % SYSTEM_PER_PAGE;
*/
    }

    fprintf(sfp, "\n");
    (*line_cnt)++;
    fflush(sfp);
}


/**********************************************************************/
/*  print shared channels report heading                              */
/**********************************************************************/

print_schannel_rpt_head(sfp, page_cnt)
FILE    *sfp;
int     *page_cnt;
{

#ifdef DEBUG
    printf("print_schannel_head\n");
#endif

   if (*page_cnt > 1)
       fprintf(sfp, "\f");

   fprintf(sfp, "RUN DATE: %s%16s", sys_date, "");
   fprintf(sfp, "*****************************************************************");
   fprintf(sfp, "%16sPAGE   : %-d\n", "", *page_cnt);
   fprintf(sfp, "RUN TIME: %s%16s", sys_time, "");
   fprintf(sfp, "*%63s*", "");
   fprintf(sfp, "%16sPROGRAM: esemsc0f\n", "");
   fprintf(sfp, "USER ID : %-19s%5s", emc_uid, "");
   fprintf(sfp, "*%20sFREQUENCY PRE-SELECTION%20s*\n", "", "");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*%17sSHARED CHANNEL TRAFFIC REPORT%17s*\n", "", "");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*%63s*\n", "");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*   BUSS CODE : %-4s%18sFREQ BAND  : %-9s%4s*\n", buss_code,
           "", band_code, "");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*   BASE TX LOW (MHz) : %11.5lf   HIGH (MHz) : %11.5lf  *\n",
           o_tx_freq_lo, o_tx_freq_hi);
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*   BASE RX LOW (MHz) : %11.5lf   HIGH (MHz) : %11.5lf  *\n",
           o_rx_freq_lo, o_rx_freq_hi);
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*   NO. OF CHANNELS TO BE PRINTED : %3d%25s*\n", 
	   no_of_channels, "");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*%63s*\n", "");
   fprintf(sfp, "%34s", "");
   fprintf(sfp, "*****************************************************************");
   fprintf(sfp, "\n\n\n");
}


/**********************************************************************/
/*  print shared channels report column heading                       */
/**********************************************************************/

print_schannel_col_head(sfp)
FILE    *sfp;
{
   fprintf(sfp, " TX   FREQ%4sRX   FREQ%3sFIGURE OF%2sNO. OF%3sTRAF.LOAD/%2s",
		"", "", "", "", "");
   fprintf(sfp, "MEASURED%17sASSIGN%3sBUSS\n", "", "");
   fprintf(sfp, "   (MHz)%8s(MHz)%5sSHARING%4sSHARERS%2s", "", "", "", "");
   fprintf(sfp, "NO. OF MOB%4sDATE%4sSYSTEM ID.%5sDATE%5s", "", "", "", "");
   fprintf(sfp, "CODE%2sBASE STATION SUB-DISTRICT CODES\n", "");
   fprintf(sfp, "===========  ===========  =========  =======  ");
   fprintf(sfp, "==========  ========  ============== ======== ====  ");
   fprintf(sfp, "===============================\n\n"); 
   fflush(sfp);
}
