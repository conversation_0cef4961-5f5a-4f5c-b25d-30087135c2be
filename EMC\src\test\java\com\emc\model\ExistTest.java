package com.emc.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class ExistTest {

    @Test
    void testExistProperties() {
        // Setup
        Exist exist = new Exist();
        
        // Set properties
        exist.setEmcUid("TEST_EMC_UID");
        exist.setSysCategory('X');
        exist.setSysType("XX");
        exist.setSysNo("0001");
        exist.setSysSuffix("000");
        exist.setEastGrid(12345);
        exist.setNorthGrid(67890);
        exist.setSubDistrict("ABC");
        exist.setStationType('X');
        exist.setDesenAttDb(12.34);
        exist.setIntmodAttDb(56.78);
        exist.setAntenna("YZ");
        exist.setAntHeight(45);
        exist.setPwDbw(12.34);
        exist.setAzMaxRad(123);
        exist.setAzMaxRadR(2.1467);
        exist.setFeedLoss(123);
        exist.setSfxFilter("FILTER123");
        exist.setHeightAsl(100.0);
        exist.setDistType('U');
        exist.setNoiseCode(1);
        exist.setDistIndex(2);
        
        // Verify
        assertEquals("TEST_EMC_UID", exist.getEmcUid());
        assertEquals('X', exist.getSysCategory());
        assertEquals("XX", exist.getSysType());
        assertEquals("0001", exist.getSysNo());
        assertEquals("000", exist.getSysSuffix());
        assertEquals(12345, exist.getEastGrid());
        assertEquals(67890, exist.getNorthGrid());
        assertEquals("ABC", exist.getSubDistrict());
        assertEquals('X', exist.getStationType());
        assertEquals(12.34, exist.getDesenAttDb());
        assertEquals(56.78, exist.getIntmodAttDb());
        assertEquals("YZ", exist.getAntenna());
        assertEquals(45, exist.getAntHeight());
        assertEquals(12.34, exist.getPwDbw());
        assertEquals(123, exist.getAzMaxRad());
        assertEquals(2.1467, exist.getAzMaxRadR());
        assertEquals(123, exist.getFeedLoss());
        assertEquals("FILTER123", exist.getSfxFilter());
        assertEquals(100.0, exist.getHeightAsl());
        assertEquals('U', exist.getDistType());
        assertEquals(1, exist.getNoiseCode());
        assertEquals(2, exist.getDistIndex());
    }
}
