#ifndef global_const
#define global_const

#include <cursesX.h>
#include <stdio.h>

int screen, printer,            /* output modes */
    land_type;

FILE *terrain_lun, *fine_lun,   /* these two are needed in terrain.for */
     *output_file_unit,
     *profile_unit,       /* unit no. in plotting graph */
     *control_unit;       /* unit no. of control file */

/*
  character*(*) plot_file, plot_file_sea
*/

/******************************************************************************/

#define OUTPUT_FILE_UNIT    60
#define PROFILE_UNIT        61
#define CONTROL_UNIT        62

#define TERRAIN_LUN     32
#define FINE_LUN        39

#define SCREEN          6
#define PRINTER         2

/* terrain types */
#define SEA_TYPE        0
#define UNKNOWN_TYPE    9       /* LAND_TYPE 1,2,3,4,5,6,7,8, */

#define PLOT_FILE       "PLOT_FILE.DAT"
#define PLOT_FILE_SEA   "[LEARN.STUDENT.DTR]PLOT_FILE_SEA.DAT"

/* sizes of text outputs with horizonal and vertical values */
#define TINY            0
#define WIDTH_TINY      9
#define HEIGHT_TINY     8
#define SMALL           1
#define WIDTH_SMALL     9
#define HEIGHT_SMALL    18
#define NORMAL          2
#define WIDTH_NORMAL    18
#define HEIGHT_NORMAL   28
#define LARGE           4
#define WIDTH_LARGE     36
#define HEIGHT_LARGE    58
#define HUGEX           10
#define WIDTH_HUGE      90
#define HEIGHT_HUGE     148

/* styles of text outputs */
#define ITALIC          12
#define INVERTED        14
#define DOUBLE_HEIGHT   16

/* a total of 14 colors can be displayed can be displayed simultaneously */
#define BLACK           "S(M0(L0)(AH0L0S0))W(I0)"
#define GRAY            "S(M7(AH0L53S0))W(I7)"
#define WHITE           "S(M15(AH0L95S100))W(I15)"
#define LIGHT_BLUE      "S(M9(AH0L75S100))W(I9)"
#define BLUE            "S(M1(AH0L50S100))W(I1)"
#define DARK_BLUE       "S(M1(H0L20S67))W(I1)"
#define LIGHT_RED       "S(M10(AH120L65S67))W(I10)"
#define RED             "S(M2(AH120L40S100))W(I2)"
#define DARK_RED        "S(M2(AH120L25S67))W(I2)"
#define LIGHT_GREEN     "S(M11(AH240L65S95))W(I11)"
#define GREEN           "S(M3(AH240L35S100))W(I3)"
#define DARK_GREEN      "S(M3(AH240L25S67))W(I3)"
#define LIGHT_MAGENTA   "S(M12(AH60L65S67))W(I12)"
#define MAGENTA         "S(M4(AH60L45S67))W(I4)"
#define DARK_MAGENTA    "S(M4(AH60L25S67))W(I4)"
#define LIGHT_CYAN      "S(M13(AH300L65S67))W(I13)"
#define CYAN            "S(M5(AH300L45S67))W(I5)"
#define DARK_CYAN       "S(M5(AH300L45S67))W(I5)"
#define LIGHT_YELLOW    "S(M14(AH180L80S67))W(I14)"
#define YELLOW          "S(M6(AH180L45S90))W(I6)"
#define DARK_YELLOW     "S(M6(AH180L30S67))W(I6)"

/* this color palette is reserved for user */
#define USER_COLOR      "S(M8)W(I8)"

/* colors used in plotting different terrains */
/* suppressed at 1Mar93
#define SEA_COLOR       LIGHT_BLUE
#define UNKNOWN_COLOR   LIGHT_MAGENTA
*/
#define SEA_COLOR       YELLOW 
#define UNKNOWN_COLOR   YELLOW
#define LAND_COLOR      YELLOW

/* available line styles */
#define SOLID_LINE          1
#define DASH_LINE           2
#define DASH_DOT_LINE       3
#define DOTTED_LINE         4
#define SPARSE_DOT_LINE     6

/* line styles used in plotting profile */
#define LAND_LINE_STYLE     SOLID_LINE
/* old settings (before 22Mar93)
#define SEA_LINE_STYLE      DASH_DOT_LINE
#define UNKNOWN_LINE_STYLE  SPARSE_DOT_LINE
*/
#define SEA_LINE_STYLE      SOLID_LINE
#define UNKNOWN_LINE_STYLE  SOLID_LINE

/* graph is bounded in this area */
/* old settings (before 1Mar93)
#define LEFT_MARGIN     60
#define RIGHT_MARGIN    770
*/
#define LEFT_MARGIN     50
#define RIGHT_MARGIN    760
#define TOP_MARGIN      45
#define BOTTOM_MARGIN   400

/* screen size */
#define MAX_SCR_X       799
#define MAX_SCR_Y       479
 
/* position and size of legend */
#define LEGEND_X        645
#define LEGEND_Y        90
#define LEGEND_WIDTH    120
#define LEGEND_HEIGHT   70

/* no. of partitions in x and y axises */
#define N_X_PARTITION   12
#define N_Y_PARTITION   8

# define ESCCHAR 033    /* byte */

/* Various video modifiers */
#define	TRM_NOECHO	0x00000001
#define TRM_CVTLOW	0x00000002
#define TRM_LOCK  	0x00000004

#endif
