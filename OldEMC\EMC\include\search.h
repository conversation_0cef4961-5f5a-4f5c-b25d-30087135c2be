/*	@(#)search.h	6.1	(ULTRIX)	11/19/91	*/

/************************************************************************
 *									*
 *			Copyright (c) 1989 by				*
 *		Digital Equipment Corporation, Maynard, MA		*
 *			All rights reserved.				*
 *									*
 *   This software is furnished under a license and may be used and	*
 *   copied  only  in accordance with the terms of such license and	*
 *   with the  inclusion  of  the  above  copyright  notice.   This	*
 *   software  or  any  other copies thereof may not be provided or	*
 *   otherwise made available to any other person.  No title to and	*
 *   ownership of the software is hereby transferred.			*
 *									*
 *   This software is  derived  from  software  received  from  the	*
 *   University    of   California,   Berkeley,   and   from   Bell	*
 *   Laboratories.  Use, duplication, or disclosure is  subject  to	*
 *   restrictions  under  license  agreements  with  University  of	*
 *   California and with AT&T.						*
 *									*
 *   The information in this software is subject to change  without	*
 *   notice  and should not be construed as a commitment by Digital	*
 *   Equipment Corporation.						*
 *									*
 *   Digital assumes no responsibility for the use  or  reliability	*
 *   of its software on equipment which is not supplied by Digital.	*
 *									*
 ************************************************************************/
/*
 *
 *   Modification history:
 *
 *	Jon <PERSON>, 1989-Jul-14
 *	Added headers, X/Open-mandated function declarations.
 *
 */
/*	Derived from:	*/
/*	@(#)search.h	1.1	*/

/* HSEARCH(3C) */
typedef struct entry { char *key, *data; } ENTRY;
typedef enum { FIND, ENTER } ACTION;

extern ENTRY *hsearch();
extern int hcreate();
extern void hdestroy();

/* TSEARCH(3C) */
typedef enum { preorder, postorder, endorder, leaf } VISIT;

extern void *tsearch(), *tfind(), *tdelete();
extern void twalk();

/* LSEARCH(3) */
extern void *lsearch(), *lfind();
