package com.emc.model;

import lombok.Data;
import java.util.List;

/**
 * Represents a request for EMC analysis.
 * This is used as input for the REST API.
 */
@Data
public class EmcAnalysisRequest {
    /**
     * The EMC UID to use for the analysis.
     * For interactive mode, if not provided, defaults to "ELSO_WEB.ANALYSIS".
     */
    private String emcUid;

    private boolean interactive;
    private String interactiveMode; // "-l" or "-s"

    /**
     * List of existing stations for EMC analysis.
     * This replaces the previous batchContent string approach.
     */
    private List<Exist> batchContent;

    /**
     * Default constructor that initializes default values.
     */
    public EmcAnalysisRequest() {
        // Default values
        this.interactive = false;
        this.interactiveMode = "-l";
    }
}
