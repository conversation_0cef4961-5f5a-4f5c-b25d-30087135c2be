/************************************/
/*  Logical and Choice Definitions  */
/************************************/

#ifdef TRUE
#undef TRUE
#endif
#define   TRUE     1

#ifdef FALSE
#undef FALSE
#endif
#define   FALSE    0

#define DESENSIT       0
#define INTERMOD       1

#define RECEIVER       0
#define TRANSMITTER_1  1
#define TRANSMITTER_2  2
#define TRANSMITTER_3  3

#define IS_PROPOSED    0
#define IS_EXIST       1


/************************************/
/*    EMC constants Definitions     */
/************************************/

#define EPSILON                0.00005

#define MAX_EXIST              5000

#define MAX_POINTS             200

#define MIN_ANT_HEIGHT         3.0

#define INTERMOD_DB            -14.0

#define CLUTTER_LOSS           10.0
#define CLEARANCE_LOSS         10.0

#define MIN_CHANNEL_SEP        0.0125

/*
#define DESENSIT_CULL_FREQ     3.0
#define INTERMOD2_CULL_FREQ    3.0

#ifdef  COSITE
#define INTERMOD3_CULL_FREQ    3.0
#else
#define INTERMOD3_CULL_FREQ    0.5
#endif
*/

#define FREQ_EPSILON           0.0005

#define FILE_OPEN_ERROR        -1

#define PROPSTN_ID             "P-PPP-PPPPPPP-PPP"


/************************************/
/*    SQL constants Definitions     */
/************************************/
#define NOT_FOUND          1403
