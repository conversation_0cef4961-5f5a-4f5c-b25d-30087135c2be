/*	@(#)ulimit.h	6.1	ULTRIX	11/19/91	*/
/************************************************************************
 *									*
 *			Copyright (c) 1989 by				*
 *		Digital Equipment Corporation, Maynard, MA		*
 *			All rights reserved.				*
 *									*
 *   This software is furnished under a license and may be used and	*
 *   copied  only  in accordance with the terms of such license and	*
 *   with the  inclusion  of  the  above  copyright  notice.   This	*
 *   software  or  any  other copies thereof may not be provided or	*
 *   otherwise made available to any other person.  No title to and	*
 *   ownership of the software is hereby transferred.			*
 *									*
 *   The information in this software is subject to change  without	*
 *   notice  and should not be construed as a commitment by Digital	*
 *   Equipment Corporation.						*
 *									*
 *   Digital assumes no responsibility for the use  or  reliability	*
 *   of its software on equipment which is not supplied by Digital.	*
 *									*
 ************************************************************************/
/************************************************************************
 *			Modification History
 *
 *	Jon Reeves, 1989 July 17
 * 001	Created to satisfy X/Open standard
 *
 ************************************************************************/

/*
 *	Definitions for the ulimit() system call.
 */

/*
 *	Operations available:
 */
/*	Get maximum file size	*/
#define	UL_GETFSIZE	1
/*	Set maximum file size	*/
#define	UL_SETFSIZE	2

/*	Declare the function	*/
extern	long	ulimit();
