
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,0,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[12];
};
static const struct sqlcxp sqlfpn =
{
    11,
    "esemvc0f.pc"
};


static unsigned int sqlctx = 150059;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[6];
   unsigned long  sqhstl[6];
            int   sqhsts[6];
            short *sqindv[6];
            int   sqinds[6];
   unsigned long  sqharm[6];
   unsigned long  *sqharc[6];
   unsigned short  sqadto[6];
   unsigned short  sqtdso[6];
} sqlstm = {13,6};

/* SQLLIB Prototypes */
extern void sqlcxt (void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlcx2t(void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlbuft(void **, char *);
extern void sqlgs2t(void **, char *);
extern void sqlorat(void **, unsigned int *, void *);

/* Forms Interface */
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern void sqliem(unsigned char *, signed int *);

 static const char *sq0001 = 
"select BAND_CODE ,BASE_TX_LOW ,BASE_TX_HIGH ,BASE_RX_LOW ,BASE_RX_HIGH ,CHAN\
NEL_SPACING  from FREQUENCY_BAND  order by BASE_TX_LOW            ";

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4242,1,0,0,
5,0,0,1,142,0,9,893,0,0,0,0,0,1,0,
20,0,0,1,0,0,13,895,0,0,6,0,0,1,0,2,9,0,0,2,4,0,0,2,4,0,0,2,4,0,0,2,4,0,0,2,4,
0,0,
59,0,0,1,0,0,15,903,0,0,0,0,0,1,0,
74,0,0,1,0,0,13,920,0,0,6,0,0,1,0,2,9,0,0,2,4,0,0,2,4,0,0,2,4,0,0,2,4,0,0,2,4,
0,0,
113,0,0,1,0,0,15,927,0,0,0,0,0,1,0,
128,0,0,2,60,0,4,976,0,0,2,1,0,1,0,2,1,0,0,1,9,0,0,
151,0,0,3,142,0,4,1040,0,0,6,1,0,1,0,2,4,0,0,2,4,0,0,2,4,0,0,2,4,0,0,2,4,0,0,1,
9,0,0,
190,0,0,4,102,0,4,1074,0,0,5,4,0,1,0,2,1,0,0,1,4,0,0,1,4,0,0,1,0,0,0,1,0,0,0,
225,0,0,5,102,0,4,1094,0,0,5,4,0,1,0,2,1,0,0,1,4,0,0,1,4,0,0,1,0,0,0,1,0,0,0,
};


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  main (esemvc0f.pc)                             */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  esemfp0f.c through 'system' statement          */  
/*                                                                    */
/*    Parameters    :  user id.                                       */
/*                     user password                                  */
/*                     EMC user id.                                   */
/*                     printer id.                                    */
/*                                                                    */
/*    Called Modules:  screen functions from 'screen.c'               */
/*                     (centre_msg, disp_err, disp_space)             */
/*                     screen function from 'cursesX'                 */
/*                     user_login and user_logout (login.pc)          */
/*                                                                    */
/*    Purpose       :  Accept user input frequency band, then         */
/*                     print out all vacant channels within this      */
/*                     band.                                          */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/


#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <time.h>
#include <curses.h>
#include "../include/emc.h"
#include "../include/global.h"


#define  SLEEP_SEC      2

#define  MAX_FLD_LEN    10     /* max. field length of input field */

#define  LOGIN_OK       0

/* ORACLE status */
#define  FOUND          0
#define  NOT_FOUND      1403

#define  NEXT_START_PT  0     /* field index at which user starts his   */
                              /* input for next frequency pre-selection */

#define  MAX_BAND_NO           100
#define  MAX_BAND_LEN          77

#define  MAX_VCHANNEL_LINES    38
#define  CHANNEL_PER_LINE      5

#define  FREQ_EPSILON          0.0005

#define  TRIGGER_WIN_MSG       "Press F2 for window select"

#include "../include/winscrn.h"

char    *band[MAX_BAND_NO];

char    *band_head[] = 
{
" BAND CODE     BASE TX LOW     BASE TX HIGH     BASE RX LOW     BASE RX HIGH",
"                  (MHz)           (MHz)            (MHz)           (MHz)    ",
" =========     ===========     ============     ===========     ============",
(char *)NULL
};
            
/* field validation function declarations */
int    chk_band_code();

/* For field definitions, see ../include/winscrn.h */
FIELD item[] = 
{
18,12,0,    band,band_head,TRUE,STRING, TRUE, 9, 0, NEW,"         ",   "",0,FALSE,chk_band_code,
-1,-1,DUMMY,NULL_AR,NULL_AR,TRUE,DUMMY,  TRUE, 0, 0, NEW,"",   "",0,FALSE,NULL_FUNC
};

FIELD_WIN fld_win[] = 
{
NULL_WIN, 4, 1,  12, MAX_BAND_LEN,
NULL_WIN, DUMMY,DUMMY,DUMMY,DUMMY
};

/*
char   *item_err_msg[] = 
{
   "Invalid band code",
   ""
};
*/
     
char   *prog_id = "emvc0f_01";
char   *screen_head = "VACANT CHANNEL ALLOCATION REPORT GENERATION";

char   *getenv();

char   passwd[20];
char   emc_uid[20];
char   band_code[10];

extern char msg[];

/* EXEC SQL BEGIN DECLARE SECTION; */ 


    double   o_tx_freq_hi;
    double   o_tx_freq_lo;
    double   o_rx_freq_hi;
    double   o_rx_freq_lo;
    double   o1_tx_freq_hi;
    double   o1_tx_freq_lo;
    double   o1_rx_freq_hi;
    double   o1_rx_freq_lo;
    double   o_channel_sep;
    double   o_tx_freq;
    double   o_rx_freq;
    /* VARCHAR  o_band_code[10]; */ 
struct { unsigned short len; unsigned char arr[10]; } o_band_code;

    char     o_dummy;

/* EXEC SQL END DECLARE SECTION; */ 


/* EXEC SQL INCLUDE SQLCA;
 */ 
/*
 * $Header: sqlca.h 24-apr-2003.12:50:58 mkandarp Exp $ sqlca.h 
 */

/* Copyright (c) 1985, 2003, Oracle Corporation.  All rights reserved.  */
 
/*
NAME
  SQLCA : SQL Communications Area.
FUNCTION
  Contains no code. Oracle fills in the SQLCA with status info
  during the execution of a SQL stmt.
NOTES
  **************************************************************
  ***                                                        ***
  *** This file is SOSD.  Porters must change the data types ***
  *** appropriately on their platform.  See notes/pcport.doc ***
  *** for more information.                                  ***
  ***                                                        ***
  **************************************************************

  If the symbol SQLCA_STORAGE_CLASS is defined, then the SQLCA
  will be defined to have this storage class. For example:
 
    #define SQLCA_STORAGE_CLASS extern
 
  will define the SQLCA as an extern.
 
  If the symbol SQLCA_INIT is defined, then the SQLCA will be
  statically initialized. Although this is not necessary in order
  to use the SQLCA, it is a good pgming practice not to have
  unitialized variables. However, some C compilers/OS's don't
  allow automatic variables to be init'd in this manner. Therefore,
  if you are INCLUDE'ing the SQLCA in a place where it would be
  an automatic AND your C compiler/OS doesn't allow this style
  of initialization, then SQLCA_INIT should be left undefined --
  all others can define SQLCA_INIT if they wish.

  If the symbol SQLCA_NONE is defined, then the SQLCA variable will
  not be defined at all.  The symbol SQLCA_NONE should not be defined
  in source modules that have embedded SQL.  However, source modules
  that have no embedded SQL, but need to manipulate a sqlca struct
  passed in as a parameter, can set the SQLCA_NONE symbol to avoid
  creation of an extraneous sqlca variable.
 
MODIFIED
    lvbcheng   07/31/98 -  long to int
    jbasu      12/12/94 -  Bug 217878: note this is an SOSD file
    losborne   08/11/92 -  No sqlca var if SQLCA_NONE macro set 
  Clare      12/06/84 - Ch SQLCA to not be an extern.
  Clare      10/21/85 - Add initialization.
  Bradbury   01/05/86 - Only initialize when SQLCA_INIT set
  Clare      06/12/86 - Add SQLCA_STORAGE_CLASS option.
*/
 
#ifndef SQLCA
#define SQLCA 1
 
struct   sqlca
         {
         /* ub1 */ char    sqlcaid[8];
         /* b4  */ int     sqlabc;
         /* b4  */ int     sqlcode;
         struct
           {
           /* ub2 */ unsigned short sqlerrml;
           /* ub1 */ char           sqlerrmc[70];
           } sqlerrm;
         /* ub1 */ char    sqlerrp[8];
         /* b4  */ int     sqlerrd[6];
         /* ub1 */ char    sqlwarn[8];
         /* ub1 */ char    sqlext[8];
         };

#ifndef SQLCA_NONE 
#ifdef   SQLCA_STORAGE_CLASS
SQLCA_STORAGE_CLASS struct sqlca sqlca
#else
         struct sqlca sqlca
#endif
 
#ifdef  SQLCA_INIT
         = {
         {'S', 'Q', 'L', 'C', 'A', ' ', ' ', ' '},
         sizeof(struct sqlca),
         0,
         { 0, {0}},
         {'N', 'O', 'T', ' ', 'S', 'E', 'T', ' '},
         {0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0}
         }
#endif
         ;
#endif
 
#endif
 
/* end SQLCA */



main(argc, argv)
int    argc;
char   **argv;
{
    char    *confirm_msg = "** CONFIRM [Y/N]? ";
    char    err_msg[80];
    char    answer;
    int     term_code;
    int     item_cnt;                   /* total no. of items               */
    int     last_item;                  /* index of last filled-up item     */

    int     x_pos, y_pos;               /* current cursor position          */
    int     curr_pos;                   /* current position of input string */
    int     loop;                       /* TRUE if entry continue on        */
                                        /* current item                     */
    int     modified = FALSE;           /* TRUE when current item has been  */
                                        /* modified                         */
    int     token;                      /* user-input character             */
    int     status = !(QUIT);
    int     direction;                  /* field shuttle direction          */
    int     select_by_window = FALSE;
    int     err_flag = FALSE;
    int     y1, y2, x1, x2;             /* co-ordinates of upper-left       */
                                        /* corner and lower-right corner of */
                                        /* selection window                 */
    int     win_cnt;                    /* no. of window lines              */
    int     entry;                      /* entry selected from selection    */
                                        /* window                           */
    int     page_cnt = 1;
    int     i;

    register int     j;
    struct tm  *tt;
char s[80];


    /* EXEC SQL WHENEVER NOT FOUND CONTINUE; */ 


    initscr();
    raw();
    noecho();
    clear();
    keypad(stdscr, TRUE); 

    if (argc != 6)
    {
       sprintf(err_msg, "Usage: esemvc0f user_id passwd emc_uid -P printer_id");
       goto force_exit;
    }

    if (strcmp(argv[4], "-P"))
    {
       sprintf(err_msg, "Usage: esemvc0f user_id passwd emc_uid -P printer_id");
       goto force_exit;
    }
    
    strcpy(user_id, argv[1]);
    strcpy(passwd, argv[2]);

    if (user_login(user_id, passwd, err_msg) != LOGIN_OK)
    {
        strip_blank(err_msg, TRAIL);
        strcat(err_msg, "  ");
        strcat(err_msg, user_id);
        strcat(err_msg, passwd);
        goto force_exit;
    }

    /* EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn; */ 


    strcpy(emc_uid, argv[3]);
    strcpy(printer_id, argv[5]);
    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);

    for (i = 0; item[i].xpos != -1; i++)
        ;

    item_cnt = i;

    emc_dir = getenv("EMC");
    disp_heading(prog_id, screen_head, sys_date);
    disp_entry_scn(item_cnt, item_cnt, NEW_SCREEN);
 
    for (i = 0; status != QUIT; )
    {
        x_pos = item[i].xpos; 
        y_pos = item[i].ypos;

        if (with_window(&item[i]))
            centre_msg(16, TRIGGER_WIN_MSG, (A_BOLD|A_BLINK), A_REVERSE);
        else
            clear_msg(16);

         if (select_by_window)
             select_by_window = FALSE;

        /**********************************************************/
        /* if data type of current field is sign field, then skip */
        /* the 1st position of current string and increment       */
        /* screen x-coordinate by 1, else keep 1st position       */
        /**********************************************************/
        if (is_sign(&item[i]))
        {
            curr_pos = 1;
            x_pos++;
        }
        else
            curr_pos = 0;
        move(y_pos, x_pos);
        loop = TRUE;

        for (; loop == TRUE;)
        {
            refresh();
            token = getch();

            attroff(A_REVERSE);
            getyx(stdscr, y_pos, x_pos);
            if (err_flag == TRUE)
            {
                clear_err();
                err_flag = FALSE;
            }
            move(y_pos, x_pos);
            attron(A_REVERSE);

            /**************************************/
            /* Function key definitions:          */
            /*    KEY_F(1)  - quit session        */
            /*    KEY_F(2)  - trigger sub-window  */
            /*    KEY_F(3)  - refresh screen      */
            /*    KEY_F(4)  - clear field         */
            /*    TAB,                            */
            /*    NEWLINE,                        */
            /*    KEY_DOWN  - next field          */
            /*    CTRL_B,                         */
            /*    KEY_UP    - next field          */
            /*    KEY_LEFT  - next char position  */
            /*    KEY_RIGHT - next char position  */
            /*    DELETE,                         */
            /*    BACKSPACE - delete current char */
            /*                and back 1 position */
            /**************************************/

            switch (token)
            {
                case KEY_F(1):
                    status = QUIT;
                    loop = FALSE;
                    break;

                case KEY_F(2):
                    if (!(with_window(&item[i])))
                    {
                        beep();
                        break;
                    }
                    hide_cursor();
                    clear_msg(16);
                    if (item[i].first_select == TRUE)
                    {
                        int    status;

                        status = prepare_win_lines(&fld_win[item[i].win_idx],
                                                   item[i].disp_arr,
                                                   &win_cnt,
                                                   err_msg);
                        if (status == ERROR)
                            goto force_exit;

                    }

                    y1 = fld_win[item[i].win_idx].win_ypos;
                    x1 = fld_win[item[i].win_idx].win_xpos;
                    y2 = y1 + fld_win[item[i].win_idx].win_lines - 1;
                    x2 = x1 + fld_win[item[i].win_idx].win_len - 1;
                    if (item[i].first_select == TRUE)
                    {
                        item[i].first_select = FALSE;
                        entry = select_entry(fld_win[item[i].win_idx].wp, TRUE,
                                             y1,x1,y2,x2, item[i].disp_arr,
                                             item[i].heading,
                                             fld_win[item[i].win_idx].win_len, 
                                             FALSE, win_cnt);
                    }
                    else
                        entry = select_entry(fld_win[item[i].win_idx].wp, TRUE,
                                             y1,x1,y2,x2, item[i].disp_arr,
                                             item[i].heading,
                                             fld_win[item[i].win_idx].win_len, 
                                             TRUE, win_cnt);

                    werase(fld_win[item[i].win_idx].wp);
                    clear_border(y1, x1, y2, x2);
                    attrset(A_REVERSE);
                    show_cursor();

                    if (entry != EXIT)
                    {
                        sscanf(item[i].disp_arr[entry], "%s", item[i].curr_str);
                        disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
                        mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
                        item[i].curr_len = strlen(item[i].curr_str);
                        item[i].state = MODIFIED;
                    }

                    select_by_window = TRUE;
                    loop = FALSE;
                    direction = FORWARD;
                    break;

                case KEY_F(3):
                    attroff(A_REVERSE);
                    refresh_screen(item_cnt, i);
                    if (with_window(&item[i]))
                        centre_msg(16, TRIGGER_WIN_MSG,
                                   (A_BOLD|A_BLINK), A_REVERSE);
                    move(y_pos, x_pos);
                    break; 

                case KEY_F(4):
                    init_field(&item[i], &curr_pos);
                    loop = FALSE; 
                    direction = RESTART; 
                    break; 

                case KEY_UP:
                case CTRL_B:
                    if (i == 0)
                        beep();
                    else
                    {
                        loop = FALSE;
                        direction = BACKWARD;
                    }
                    break;

                case KEY_DOWN:
                case NEWLINE:
                case TAB:
/*
sprintf(s," i len req: %d %d %d", i, item[i].curr_len, item[i].required);
mvaddstr(23,0,s);
                    if ((item[i].state == NEW) && (item[i].required))
                    if ((is_sign(&item[i]) && (item[i].curr_len == 1))
                     || (!is_sign(&item[i]) && empty(&item[i]))
                    && (item[i].required))
*/
                    /*********************************************************/
                    /* beep user if he wants to skip an input-required field */
                    /*********************************************************/
                    if (is_sign(&item[i]))
                    {
                        if ((item[i].curr_len == 1) && (item[i].required))
                        {
                            if (with_window(&item[i]))
                                beep();
                            else
                            {
                                err_flag = TRUE;
                                disp_err(INPUT_REQ_MSG);
                                attron(A_REVERSE);
                            }
                            break;
                        }
                    }
                    else
                        if (empty(&item[i]) && (item[i].required))
                        {
                            if (with_window(&item[i]))
                                beep();
                            else
                            {
                                err_flag = TRUE;
                                disp_err(INPUT_REQ_MSG);
                                attron(A_REVERSE);
                            }
                            break;
                        }

                    if (empty(&item[i]) 
                    ||  (is_sign(&item[i]) && (item[i].curr_len == 1)))
                        strcpy(item[i].curr_str, item[i].init_str);

                    loop = FALSE;
                    direction = FORWARD;
                    break;

                case KEY_LEFT:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                    }
                    break;

                case KEY_RIGHT:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* right boundary of current field  */
                    /************************************/
                    if (x_pos - item[i].xpos == item[i].curr_len)
                        beep();
                    else
                    {
                        x_pos++; curr_pos++;
                    }
                    break;

                case BACKSPACE:
                case DELETE:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                        if (is_float(&item[i]) 
                        && (item[i].curr_str[curr_pos] == DOT))
                            item[i].has_dot = FALSE;
                        move(y_pos, x_pos);
                        addch(BLANK);
                        modified = TRUE;
/*
                        if (x_pos == item[i].xpos)
                            item[i].state = NEW;
                        else
                            item[i].state = MODIFIED;
*/
                    }
                    break;

                case DOT:

                    /******************************************************/
                    /* beep user if data type of current field is integer */
                    /******************************************************/
                    if (is_int(&item[i]))
                    {
                        beep();
                        break;
                    }


                    /********************************************************/
                    /* beep user if data type of current field is float and */
                    /* user has already input one dot                       */
                    /********************************************************/
                    if (is_float(&item[i]))
                        if (item[i].has_dot)
                        {
                            beep();
                            break;
                        }
                        else
                            item[i].has_dot = TRUE;

                    if (item[i].state == NEW)
                        if (is_sign(&item[i]))
                        {
                            if (curr_pos == 1)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len - 1);
                                move(y_pos, x_pos);
                            }
                        }
                        else
                            if (curr_pos == 0)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len);
                                move(y_pos, x_pos);
                            }

                    addch(token);
                    item[i].curr_str[curr_pos] = token;
                    x_pos++; curr_pos++;
                    modified = TRUE;
                    item[i].state = MODIFIED;

                    if (x_pos - item[i].xpos == item[i].fld_len)
                    {
                        item[i].curr_str[item[i].fld_len] = '\0';
                        item[i].curr_len = item[i].fld_len;
                        loop = FALSE;
                        direction = FORWARD;
                    }

                    break;

                default:
                    if ((!isalnum(token)) && (token != MINUS) 
                    &&  (token != PLUS))
                    {
                        beep();
                        break;
                    }

                    if (!isdigit(token))
                    {
/*
mvaddstr(23, 0, "not digit");
*/
                        if (is_sign(&item[i]))
                        {
                            if ((token != MINUS) && (token != PLUS))
                            {
                                beep();
                                break;
                            }
                            else
                                if (curr_pos > 1)
                                {
                                    beep();
                                    break;
                                }
                        }
                        else
/*
sprintf(s, "should not be integer: %d %d %s", i, item[i].type, item[i].init_str);
mvaddstr(22, 0, s);
*/
                            if ((item[i].type != CHAR) 
                            &&  (item[i].type != STRING))
                            {
                                beep();
                                break;
                            }
                            else
                                if (isalpha(token))
                                    token = toupper(token); 
                    }
                    else
                        if (item[i].state == NEW)
                            if (is_sign(&item[i]))
                            {
                                if (curr_pos == 1)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len-1);
                                    move(y_pos, x_pos);
                                }
                            }
                            else
                                if (curr_pos == 0)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len);
                                    move(y_pos, x_pos);
                                }

                    if (((token == MINUS) || (token == PLUS)) 
                    &&  is_sign(&item[i]))
                    {
                        x_pos--; curr_pos--;  /* because we don't want to  */
                                              /* move cursor to 1 position */
                                              /* this statement is used    */
                                              /* to complement the         */
                                              /* 'x_pos++; curr_pos++'     */
                                              /* a few lines below         */
                        if (token == MINUS)
                        {
                            item[i].curr_str[0] = MINUS;
                            move(y_pos, x_pos);
                            addch(MINUS);
                        }
                            
                    }
                    else
                    {
                        item[i].curr_str[curr_pos] = token;
                        addch(token);
                    }

/*
sprintf(s, "modified");
mvaddstr(22, 0, s);
*/
                    if ((token != MINUS) && (token != PLUS))
                        item[i].state = MODIFIED;

                    modified = TRUE;
                    x_pos++; curr_pos++;

                    if (x_pos - item[i].xpos == item[i].fld_len)
                    {
                        item[i].curr_str[item[i].fld_len] = '\0';
                        item[i].curr_len = item[i].fld_len;
                        loop = FALSE;
                        direction = FORWARD;
                    }
                
                    break;
            }

            refresh();
            if (loop == FALSE)
            {
                if (!select_by_window)
                {
                    int    (*check_item)();

                    check_item = item[i].validate;
                    if (check_item != (int(*)())NULL)
                    {
                        if ((direction != BACKWARD) && (!empty(&item[i])))
                            if ((*check_item)(&i, err_msg) == ERROR)
                            {
                                err_flag = TRUE;
                                disp_err(err_msg);
                                attron(A_REVERSE);
                                loop = FALSE;
                                direction = RESTART;
                            }
                    }

/*
                    for (j = 0; j < item_cnt; j++)
                        if (is_sign(&item[j]))
                        {
                            if ((item[j].curr_len == 1) 
                            &&  (item[j].required == TRUE))
                            {
                                direction = RESTART;
                                err_flag = TRUE;
                                disp_err(INPUT_REQ_MSG);
                                attron(A_REVERSE);
                                i = j;
                                break;
                            }
                        }
*/
                } 

                switch (direction)
                {
                    case FORWARD:
                        i++;
                        break;
                    case BACKWARD:
                        i--;
                        break;
                    case RESTART:
                        break;
                }

                continue;
            }
            
            if (modified && (!select_by_window) && (!err_flag))
            {
                int    len = x_pos - item[i].xpos;
   
                if ((item[i].curr_len < len) || is_delete(token))
                {
                    item[i].curr_len = len;
                    item[i].curr_str[item[i].curr_len] = '\0';
                }
/*
disp_space(22,0,80);
sprintf(s,"i mode: %d %d", i, tx_mode);
mvaddstr(22,0,s);
*/

                modified = FALSE;
            }
  
            move(y_pos, x_pos);
        }

        if ((status != QUIT) && (i == item_cnt) && (!err_flag))
        {
            attrset(A_BOLD);
            mvaddstr(18, 47, confirm_msg);
            refresh();
            attrset(A_REVERSE);
            getyx(stdscr, y_pos, x_pos);
            x_pos += 2;
            disp_space(y_pos, x_pos, 1);
            attroff(A_REVERSE);
            move(y_pos, x_pos);
            refresh();
            read_str(&answer, "", 1, 0, 10, &term_code, A_REVERSE);
            answer = toupper(answer);
            disp_space(18, 47, 23);
            attron(A_REVERSE);
            if (answer == 'Y')
            {
                i = NEXT_START_PT;
    
                hide_cursor();
                centre_msg(23, "Processing ...", A_BLINK, A_REVERSE);
                strcpy(band_code, item[0].curr_str);
                print_vchannel_rpt();
                show_cursor();
                attrset(A_NORMAL);
                disp_space(23, 0, 80);
                refresh();
                attrset(A_REVERSE);
                
                for (j = NEXT_START_PT; j < item_cnt; j++)
                    init_field(&item[j], &curr_pos);
                beep();
            }
            else
                i = 0;
        }
    }

    attroff(A_BOLD);
    clear();
    endwin();

    user_logout();
    exit(0);


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);

force_exit:
    disp_err(err_msg); getch();
    clear(); show_cursor(); refresh();
    endwin();
    exit(1);
}


/**************************************************************/
/* Display new entry screen or refresh current screen to user */
/**************************************************************/

disp_entry_scn(item_cnt, curr_cnt, mode)
int    item_cnt;
int    curr_cnt;
int    mode;
{
    char    tmp_str[80];
    register int    i;

    attroff(A_REVERSE);
    mvaddstr(18, 0, "BAND CODE :");
 
    sprintf(tmp_str, "%s",
"F1-Quit   F3-Refresh   F4-Clear field   UP-Prev   DOWN/<Tab>-next");
    centre_msg(21, tmp_str, A_BOLD, A_REVERSE);

    if (mode == REFRESH)
    {
        for (i = 0; i < curr_cnt; i++)
        {
            disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
            mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
        }
        for (i = curr_cnt; i < item_cnt; i++)
            if (item[i].state == NEW)
                mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);
            else
            {
                disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
                mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
            }
    }
    else
        for (i = 0; i < item_cnt; i++)
            mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);

    refresh();

}


/**************************************************************/
/* call 'disp_entry_scn' to refresh current screen to user    */
/**************************************************************/

refresh_screen(item_cnt, curr_cnt)
int item_cnt;
int curr_cnt;
{
    clear();
    disp_heading(prog_id, screen_head, sys_date);
    disp_entry_scn(item_cnt, curr_cnt, REFRESH);
}


/*************************************************/
/* initialise current field to its initial value */
/*************************************************/

init_field(p_item, curr_pos)
FIELD *p_item;
int   *curr_pos;
{
    mvaddstr(p_item->ypos, p_item->xpos, p_item->init_str);
    if (is_sign(p_item))
    {
        p_item->curr_len = *curr_pos = 1;
        p_item->curr_str[1] = '\0';
    }
    else
    {
        p_item->curr_len = *curr_pos = 0;
        p_item->curr_str[0] = '\0';
    }

    if (!disp_only(p_item))
    {
        p_item->state = NEW;
        if (is_float(p_item))
            p_item->has_dot = FALSE;
    }

    if (with_window(p_item) && (p_item->first_select == FALSE))
        p_item->first_select = TRUE;
}


/**********************************************************************/
/*  prepare window lines for display                                  */
/**********************************************************************/

prepare_win_lines(fw, disp_arr, win_cnt, err_msg)
FIELD_WIN  *fw;
char       **disp_arr;
int        *win_cnt;
char       *err_msg;
{
    int    i;
char s[80];

    if (disp_arr == band)
    {
        /* EXEC SQL DECLARE C03 CURSOR FOR
                 SELECT  BAND_CODE, BASE_TX_LOW, BASE_TX_HIGH,
                         BASE_RX_LOW, BASE_RX_HIGH, CHANNEL_SPACING
                 FROM    FREQUENCY_BAND
                 ORDER BY BASE_TX_LOW; */ 


        /* EXEC SQL OPEN C03; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 0;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.stmt = sq0001;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )5;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqcmod = (unsigned int )0;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



        /* EXEC SQL
             FETCH C03
             INTO  :o_band_code, :o_tx_freq_lo, :o_tx_freq_hi,
                   :o_rx_freq_lo, :o_rx_freq_hi, :o_channel_sep; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 6;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )20;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqfoff = (         int )0;
        sqlstm.sqfmod = (unsigned int )2;
        sqlstm.sqhstv[0] = (unsigned char  *)&o_band_code;
        sqlstm.sqhstl[0] = (unsigned long )12;
        sqlstm.sqhsts[0] = (         int  )0;
        sqlstm.sqindv[0] = (         short *)0;
        sqlstm.sqinds[0] = (         int  )0;
        sqlstm.sqharm[0] = (unsigned long )0;
        sqlstm.sqadto[0] = (unsigned short )0;
        sqlstm.sqtdso[0] = (unsigned short )0;
        sqlstm.sqhstv[1] = (unsigned char  *)&o_tx_freq_lo;
        sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[1] = (         int  )0;
        sqlstm.sqindv[1] = (         short *)0;
        sqlstm.sqinds[1] = (         int  )0;
        sqlstm.sqharm[1] = (unsigned long )0;
        sqlstm.sqadto[1] = (unsigned short )0;
        sqlstm.sqtdso[1] = (unsigned short )0;
        sqlstm.sqhstv[2] = (unsigned char  *)&o_tx_freq_hi;
        sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[2] = (         int  )0;
        sqlstm.sqindv[2] = (         short *)0;
        sqlstm.sqinds[2] = (         int  )0;
        sqlstm.sqharm[2] = (unsigned long )0;
        sqlstm.sqadto[2] = (unsigned short )0;
        sqlstm.sqtdso[2] = (unsigned short )0;
        sqlstm.sqhstv[3] = (unsigned char  *)&o_rx_freq_lo;
        sqlstm.sqhstl[3] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[3] = (         int  )0;
        sqlstm.sqindv[3] = (         short *)0;
        sqlstm.sqinds[3] = (         int  )0;
        sqlstm.sqharm[3] = (unsigned long )0;
        sqlstm.sqadto[3] = (unsigned short )0;
        sqlstm.sqtdso[3] = (unsigned short )0;
        sqlstm.sqhstv[4] = (unsigned char  *)&o_rx_freq_hi;
        sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[4] = (         int  )0;
        sqlstm.sqindv[4] = (         short *)0;
        sqlstm.sqinds[4] = (         int  )0;
        sqlstm.sqharm[4] = (unsigned long )0;
        sqlstm.sqadto[4] = (unsigned short )0;
        sqlstm.sqtdso[4] = (unsigned short )0;
        sqlstm.sqhstv[5] = (unsigned char  *)&o_channel_sep;
        sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[5] = (         int  )0;
        sqlstm.sqindv[5] = (         short *)0;
        sqlstm.sqinds[5] = (         int  )0;
        sqlstm.sqharm[5] = (unsigned long )0;
        sqlstm.sqadto[5] = (unsigned short )0;
        sqlstm.sqtdso[5] = (unsigned short )0;
        sqlstm.sqphsv = sqlstm.sqhstv;
        sqlstm.sqphsl = sqlstm.sqhstl;
        sqlstm.sqphss = sqlstm.sqhsts;
        sqlstm.sqpind = sqlstm.sqindv;
        sqlstm.sqpins = sqlstm.sqinds;
        sqlstm.sqparm = sqlstm.sqharm;
        sqlstm.sqparc = sqlstm.sqharc;
        sqlstm.sqpadto = sqlstm.sqadto;
        sqlstm.sqptdso = sqlstm.sqtdso;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



        if (sqlca.sqlcode == NOT_FOUND)
        {
            sprintf(err_msg, "No band codes found in FREQUENCY_BAND");
            /* EXEC SQL CLOSE C03; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 6;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )59;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


            return ERROR;
        }
    
        for (i = 0; i < MAX_BAND_NO; i++)
        {
            o_band_code.arr[o_band_code.len] = '\0';
    
            if ((disp_arr[i] = (char *) malloc(MAX_BAND_LEN)) == (char *) NULL)
            {
                sprintf(err_msg, "Fail to allocate memory for disp_arr");
                return ERROR;
            }
            sprintf(disp_arr[i]," %9s%5s%11.5lf%5s%11.5lf%6s%11.5lf%5s%11.5lf  ",
                    o_band_code.arr, "", o_tx_freq_lo, "", o_tx_freq_hi, "",
                    o_rx_freq_lo, "", o_rx_freq_hi);

            /* EXEC SQL
                 FETCH C03
                 INTO  :o_band_code, :o_tx_freq_lo, :o_tx_freq_hi,
                       :o_rx_freq_lo, :o_rx_freq_hi, :o_channel_sep; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 6;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )74;
            sqlstm.selerr = (unsigned short)1;
            sqlstm.sqlpfmem = (unsigned int  )0;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlstm.sqfoff = (         int )0;
            sqlstm.sqfmod = (unsigned int )2;
            sqlstm.sqhstv[0] = (unsigned char  *)&o_band_code;
            sqlstm.sqhstl[0] = (unsigned long )12;
            sqlstm.sqhsts[0] = (         int  )0;
            sqlstm.sqindv[0] = (         short *)0;
            sqlstm.sqinds[0] = (         int  )0;
            sqlstm.sqharm[0] = (unsigned long )0;
            sqlstm.sqadto[0] = (unsigned short )0;
            sqlstm.sqtdso[0] = (unsigned short )0;
            sqlstm.sqhstv[1] = (unsigned char  *)&o_tx_freq_lo;
            sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[1] = (         int  )0;
            sqlstm.sqindv[1] = (         short *)0;
            sqlstm.sqinds[1] = (         int  )0;
            sqlstm.sqharm[1] = (unsigned long )0;
            sqlstm.sqadto[1] = (unsigned short )0;
            sqlstm.sqtdso[1] = (unsigned short )0;
            sqlstm.sqhstv[2] = (unsigned char  *)&o_tx_freq_hi;
            sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[2] = (         int  )0;
            sqlstm.sqindv[2] = (         short *)0;
            sqlstm.sqinds[2] = (         int  )0;
            sqlstm.sqharm[2] = (unsigned long )0;
            sqlstm.sqadto[2] = (unsigned short )0;
            sqlstm.sqtdso[2] = (unsigned short )0;
            sqlstm.sqhstv[3] = (unsigned char  *)&o_rx_freq_lo;
            sqlstm.sqhstl[3] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[3] = (         int  )0;
            sqlstm.sqindv[3] = (         short *)0;
            sqlstm.sqinds[3] = (         int  )0;
            sqlstm.sqharm[3] = (unsigned long )0;
            sqlstm.sqadto[3] = (unsigned short )0;
            sqlstm.sqtdso[3] = (unsigned short )0;
            sqlstm.sqhstv[4] = (unsigned char  *)&o_rx_freq_hi;
            sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[4] = (         int  )0;
            sqlstm.sqindv[4] = (         short *)0;
            sqlstm.sqinds[4] = (         int  )0;
            sqlstm.sqharm[4] = (unsigned long )0;
            sqlstm.sqadto[4] = (unsigned short )0;
            sqlstm.sqtdso[4] = (unsigned short )0;
            sqlstm.sqhstv[5] = (unsigned char  *)&o_channel_sep;
            sqlstm.sqhstl[5] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[5] = (         int  )0;
            sqlstm.sqindv[5] = (         short *)0;
            sqlstm.sqinds[5] = (         int  )0;
            sqlstm.sqharm[5] = (unsigned long )0;
            sqlstm.sqadto[5] = (unsigned short )0;
            sqlstm.sqtdso[5] = (unsigned short )0;
            sqlstm.sqphsv = sqlstm.sqhstv;
            sqlstm.sqphsl = sqlstm.sqhstl;
            sqlstm.sqphss = sqlstm.sqhsts;
            sqlstm.sqpind = sqlstm.sqindv;
            sqlstm.sqpins = sqlstm.sqinds;
            sqlstm.sqparm = sqlstm.sqharm;
            sqlstm.sqparc = sqlstm.sqharc;
            sqlstm.sqpadto = sqlstm.sqadto;
            sqlstm.sqptdso = sqlstm.sqtdso;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


    
            if (sqlca.sqlcode == NOT_FOUND)
            {
                /* EXEC SQL CLOSE C03; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 6;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )113;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                break;
            }
        }
    }

    if (i == MAX_BAND_NO)
    {
        sprintf(err_msg, "No. of band codes > MAX_BAND_NO");
        disp_err(err_msg);
    }

    fw->wp = newwin(fw->win_lines, fw->win_len, fw->win_ypos, fw->win_xpos);
    keypad(fw->wp, TRUE);

    *win_cnt = i + 1;
    return OK;


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    beep(); sleep(SLEEP_SEC);
    clear(); refresh();
    endwin();
    exit(1);
}


/************************************************************************/
/* check that input frequency band code exists in FREQUENCY_BAND        */
/************************************************************************/

int    chk_band_code(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
/*    strcpy(o_band_code.arr, item[*curr_cnt].curr_str);
    o_band_code.len = strlen(o_band_code.arr); */
    strcpy((char *)o_band_code.arr, item[*curr_cnt].curr_str);
    o_band_code.len = strlen((char *)o_band_code.arr);

/*
system("echo \"esemvc0f 1\" >> /tmp/debug");
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_band_code.arr );
system(msg);
*/

    /* EXEC SQL
         SELECT  'X'
         INTO    :o_dummy
         FROM    FREQUENCY_BAND
         WHERE   BAND_CODE = :o_band_code; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 6;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select 'X' into :b0  from FREQUENCY_BAND where BAND_CODE=\
:b1";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )128;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_dummy;
    sqlstm.sqhstl[0] = (unsigned long )1;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_band_code;
    sqlstm.sqhstl[1] = (unsigned long )12;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "Invalid band code");
        return ERROR;
    }

    return OK;


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    beep(); sleep(SLEEP_SEC);
    clear(); refresh();
    endwin();
    exit(1);
}


/**********************************************************************/
/*  print vacant channels report                                      */
/**********************************************************************/

print_vchannel_rpt()
{
    char   vc_fname[120], err_fname[100];
    char   cmdline[150];
    char   print_line[133];
    char   s_channel[24];
    char   err_msg[80];
    FILE   *vfp, *efp;
    int    page_cnt = 1;
    int    line_cnt = 0;
    int    channel_cnt = 0;
    int    i = 0;

char s[80];


    sprintf(vc_fname, "%s/vchannel/%s.%s", emc_dir, emc_uid, band_code);
    if((vfp = fopen(vc_fname, "w")) == (FILE *) NULL)
    {
        sprintf(err_msg, "Fail to open %s", vc_fname);
        goto force_exit;
    }

/*    strcpy(o_band_code.arr, band_code);
    o_band_code.len = strlen(o_band_code.arr); */
    strcpy((char *)o_band_code.arr, band_code);
    o_band_code.len = strlen((char *)o_band_code.arr);
    
/*
system("echo \"esemvc0f 2\" >> /tmp/debug");
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_band_code.arr );
system(msg);
*/

    /* EXEC SQL
         SELECT BASE_TX_LOW, BASE_TX_HIGH, BASE_RX_LOW, BASE_RX_HIGH,
                CHANNEL_SPACING
         INTO   :o_tx_freq_lo, :o_tx_freq_hi, :o_rx_freq_lo, :o_rx_freq_hi,
                :o_channel_sep
         FROM   FREQUENCY_BAND
         WHERE  BAND_CODE = :o_band_code; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 6;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select BASE_TX_LOW ,BASE_TX_HIGH ,BASE_RX_LOW ,BASE_RX_HI\
GH ,CHANNEL_SPACING into :b0,:b1,:b2,:b3,:b4  from FREQUENCY_BAND where BAND_C\
ODE=:b5";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )151;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_tx_freq_lo;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_tx_freq_hi;
    sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&o_rx_freq_lo;
    sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)&o_rx_freq_hi;
    sqlstm.sqhstl[3] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqhstv[4] = (unsigned char  *)&o_channel_sep;
    sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[4] = (         int  )0;
    sqlstm.sqindv[4] = (         short *)0;
    sqlstm.sqinds[4] = (         int  )0;
    sqlstm.sqharm[4] = (unsigned long )0;
    sqlstm.sqadto[4] = (unsigned short )0;
    sqlstm.sqtdso[4] = (unsigned short )0;
    sqlstm.sqhstv[5] = (unsigned char  *)&o_band_code;
    sqlstm.sqhstl[5] = (unsigned long )12;
    sqlstm.sqhsts[5] = (         int  )0;
    sqlstm.sqindv[5] = (         short *)0;
    sqlstm.sqinds[5] = (         int  )0;
    sqlstm.sqharm[5] = (unsigned long )0;
    sqlstm.sqadto[5] = (unsigned short )0;
    sqlstm.sqtdso[5] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    print_line[0] = '\0';
    print_vchannel_head(vfp, page_cnt);
    page_cnt++;

    for (o_tx_freq = o_tx_freq_lo, o_rx_freq = o_rx_freq_lo; 
         o_tx_freq <= o_tx_freq_hi; 
         o_tx_freq += o_channel_sep, o_rx_freq += o_channel_sep)
    {
        o1_tx_freq_lo = o_tx_freq - FREQ_EPSILON;
        o1_tx_freq_hi = o_tx_freq + FREQ_EPSILON;
        o1_rx_freq_lo = o_rx_freq - FREQ_EPSILON;
        o1_rx_freq_hi = o_rx_freq + FREQ_EPSILON;
/*
        EXEC SQL
             SELECT 'X'
             INTO   :o_dummy
             FROM   ASSIGN_CH
             WHERE  ABS(TX_FREQ - :o_tx_freq) <= :o_freq_epsilon;
*/

/*
system("echo \"esemvc0f 3\" >> /tmp/debug");
sprintf(msg, "echo \"%f %f\" >> /tmp/debug", o1_tx_freq_lo, o1_tx_freq_hi );
system(msg);
*/

        /* EXEC SQL
             SELECT 'X'
             INTO   :o_dummy
             FROM   ASSIGN_CH
             WHERE  TX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi
             OR     RX_FREQ between :o1_tx_freq_lo and :o1_tx_freq_hi; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 6;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.stmt = "select 'X' into :b0  from ASSIGN_CH where (TX_FREQ be\
tween :b1 and :b2 or RX_FREQ between :b1 and :b2)";
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )190;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqhstv[0] = (unsigned char  *)&o_dummy;
        sqlstm.sqhstl[0] = (unsigned long )1;
        sqlstm.sqhsts[0] = (         int  )0;
        sqlstm.sqindv[0] = (         short *)0;
        sqlstm.sqinds[0] = (         int  )0;
        sqlstm.sqharm[0] = (unsigned long )0;
        sqlstm.sqadto[0] = (unsigned short )0;
        sqlstm.sqtdso[0] = (unsigned short )0;
        sqlstm.sqhstv[1] = (unsigned char  *)&o1_tx_freq_lo;
        sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[1] = (         int  )0;
        sqlstm.sqindv[1] = (         short *)0;
        sqlstm.sqinds[1] = (         int  )0;
        sqlstm.sqharm[1] = (unsigned long )0;
        sqlstm.sqadto[1] = (unsigned short )0;
        sqlstm.sqtdso[1] = (unsigned short )0;
        sqlstm.sqhstv[2] = (unsigned char  *)&o1_tx_freq_hi;
        sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[2] = (         int  )0;
        sqlstm.sqindv[2] = (         short *)0;
        sqlstm.sqinds[2] = (         int  )0;
        sqlstm.sqharm[2] = (unsigned long )0;
        sqlstm.sqadto[2] = (unsigned short )0;
        sqlstm.sqtdso[2] = (unsigned short )0;
        sqlstm.sqhstv[3] = (unsigned char  *)&o1_tx_freq_lo;
        sqlstm.sqhstl[3] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[3] = (         int  )0;
        sqlstm.sqindv[3] = (         short *)0;
        sqlstm.sqinds[3] = (         int  )0;
        sqlstm.sqharm[3] = (unsigned long )0;
        sqlstm.sqadto[3] = (unsigned short )0;
        sqlstm.sqtdso[3] = (unsigned short )0;
        sqlstm.sqhstv[4] = (unsigned char  *)&o1_tx_freq_hi;
        sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[4] = (         int  )0;
        sqlstm.sqindv[4] = (         short *)0;
        sqlstm.sqinds[4] = (         int  )0;
        sqlstm.sqharm[4] = (unsigned long )0;
        sqlstm.sqadto[4] = (unsigned short )0;
        sqlstm.sqtdso[4] = (unsigned short )0;
        sqlstm.sqphsv = sqlstm.sqhstv;
        sqlstm.sqphsl = sqlstm.sqhstl;
        sqlstm.sqphss = sqlstm.sqhsts;
        sqlstm.sqpind = sqlstm.sqindv;
        sqlstm.sqpins = sqlstm.sqinds;
        sqlstm.sqparm = sqlstm.sqharm;
        sqlstm.sqparc = sqlstm.sqharc;
        sqlstm.sqpadto = sqlstm.sqadto;
        sqlstm.sqptdso = sqlstm.sqtdso;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


/*
             WHERE  TX_FREQ = :o_tx_freq
             OR     RX_FREQ = :o_tx_freq;
*/

        if (sqlca.sqlcode == NOT_FOUND)
        {

/*
system("echo \"esemvc0f 4\" >> /tmp/debug");
sprintf(msg, "echo \"%f %f\" >> /tmp/debug", o1_rx_freq_lo, o1_rx_freq_hi );
system(msg);
*/

            /* EXEC SQL
                 SELECT 'X'
                 INTO   :o_dummy
                 FROM   ASSIGN_CH
                 WHERE  TX_FREQ between :o1_rx_freq_lo and :o1_rx_freq_hi
                 OR     RX_FREQ between :o1_rx_freq_lo and :o1_rx_freq_hi; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 6;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.stmt = "select 'X' into :b0  from ASSIGN_CH where (TX_FRE\
Q between :b1 and :b2 or RX_FREQ between :b1 and :b2)";
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )225;
            sqlstm.selerr = (unsigned short)1;
            sqlstm.sqlpfmem = (unsigned int  )0;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlstm.sqhstv[0] = (unsigned char  *)&o_dummy;
            sqlstm.sqhstl[0] = (unsigned long )1;
            sqlstm.sqhsts[0] = (         int  )0;
            sqlstm.sqindv[0] = (         short *)0;
            sqlstm.sqinds[0] = (         int  )0;
            sqlstm.sqharm[0] = (unsigned long )0;
            sqlstm.sqadto[0] = (unsigned short )0;
            sqlstm.sqtdso[0] = (unsigned short )0;
            sqlstm.sqhstv[1] = (unsigned char  *)&o1_rx_freq_lo;
            sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[1] = (         int  )0;
            sqlstm.sqindv[1] = (         short *)0;
            sqlstm.sqinds[1] = (         int  )0;
            sqlstm.sqharm[1] = (unsigned long )0;
            sqlstm.sqadto[1] = (unsigned short )0;
            sqlstm.sqtdso[1] = (unsigned short )0;
            sqlstm.sqhstv[2] = (unsigned char  *)&o1_rx_freq_hi;
            sqlstm.sqhstl[2] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[2] = (         int  )0;
            sqlstm.sqindv[2] = (         short *)0;
            sqlstm.sqinds[2] = (         int  )0;
            sqlstm.sqharm[2] = (unsigned long )0;
            sqlstm.sqadto[2] = (unsigned short )0;
            sqlstm.sqtdso[2] = (unsigned short )0;
            sqlstm.sqhstv[3] = (unsigned char  *)&o1_rx_freq_lo;
            sqlstm.sqhstl[3] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[3] = (         int  )0;
            sqlstm.sqindv[3] = (         short *)0;
            sqlstm.sqinds[3] = (         int  )0;
            sqlstm.sqharm[3] = (unsigned long )0;
            sqlstm.sqadto[3] = (unsigned short )0;
            sqlstm.sqtdso[3] = (unsigned short )0;
            sqlstm.sqhstv[4] = (unsigned char  *)&o1_rx_freq_hi;
            sqlstm.sqhstl[4] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[4] = (         int  )0;
            sqlstm.sqindv[4] = (         short *)0;
            sqlstm.sqinds[4] = (         int  )0;
            sqlstm.sqharm[4] = (unsigned long )0;
            sqlstm.sqadto[4] = (unsigned short )0;
            sqlstm.sqtdso[4] = (unsigned short )0;
            sqlstm.sqphsv = sqlstm.sqhstv;
            sqlstm.sqphsl = sqlstm.sqhstl;
            sqlstm.sqphss = sqlstm.sqhsts;
            sqlstm.sqpind = sqlstm.sqindv;
            sqlstm.sqpins = sqlstm.sqinds;
            sqlstm.sqparm = sqlstm.sqharm;
            sqlstm.sqparc = sqlstm.sqharc;
            sqlstm.sqpadto = sqlstm.sqadto;
            sqlstm.sqptdso = sqlstm.sqtdso;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


/*
                 WHERE  TX_FREQ = :o_rx_freq
                 OR     RX_FREQ = :o_rx_freq;
*/

            if (sqlca.sqlcode == NOT_FOUND)
            {
                if ((line_cnt != 0) && (line_cnt % MAX_VCHANNEL_LINES == 0))
                {
                    print_vchannel_head(vfp, page_cnt);
                    line_cnt = 0;
                    page_cnt++;
                }

                if (channel_cnt % CHANNEL_PER_LINE == 0)
                {
                    if (channel_cnt > 0)
                    {
                        fprintf(vfp, "%s\n", print_line);
                        line_cnt++;
                    }
                    sprintf(print_line, "  ");
                }
                else
                    strcat(print_line, "   ");

                sprintf(s_channel, "%11.5lf/%-11.5lf", o_tx_freq, o_rx_freq);
                strcat(print_line, s_channel);
                channel_cnt++;
            }
        }
    }

    if (channel_cnt == 0)
    {
        disp_err("No vacant channel, press any key to continue");
        refresh();getch();
        fclose(vfp);
    }
    else
    {
        int    skip_lines;
        if (channel_cnt != 0)
            fprintf(vfp, "%s\n", print_line);
            skip_lines = 3 + (MAX_VCHANNEL_LINES - line_cnt);
            for (i = 0; i < skip_lines; i++)
                fprintf(vfp, "\n");
            fprintf(vfp, "%45sNO. OF VACANT CHANNELS PRINTED : %d", 
                    "", channel_cnt);
        fclose(vfp);
        sprintf(cmdline, "lp -dprinter1 %s > /dev/null", vc_fname);
        system(cmdline);
    }

    return OK;

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);

force_exit:
    disp_err(err_msg);
    beep(); getch();
    clear(); show_cursor(); refresh();
    endwin();
    exit(1);
}


/**********************************************************************/
/*  print vacant channel report heading                               */
/**********************************************************************/

print_vchannel_head(vfp, page_cnt)
FILE    *vfp;
int     page_cnt;
{

#ifdef DEBUG
    printf("print_vchannel_head\n");
#endif

   if (page_cnt > 1)
       fprintf(vfp, "\f");

   fprintf(vfp, "RUN DATE: %s%16s", sys_date, "");
   fprintf(vfp, "*****************************************************************");
   fprintf(vfp, "%16sPAGE   : %-d\n", "", page_cnt);
   fprintf(vfp, "RUN TIME: %s%16s", sys_time, "");
   fprintf(vfp, "*%63s*", "");
   fprintf(vfp, "%16sPROGRAM: esemvc0f\n", "");
   fprintf(vfp, "USER ID : %-19s%5s", emc_uid, "");
   fprintf(vfp, "*%20sFREQUENCY PRE-SELECTION%20s*\n", "", "");
   fprintf(vfp, "%34s", "");
   fprintf(vfp, "*%17sVACANT CHANNEL TRAFFIC REPORT%17s*\n", "", "");
   fprintf(vfp, "%34s", "");
   fprintf(vfp, "*%63s*\n", "");
   fprintf(vfp, "%34s", "");
   fprintf(vfp, "*   FREQ BAND : %-9s%39s*\n", band_code, "");
   fprintf(vfp, "%34s", "");
   fprintf(vfp, "*   BASE TX LOW (MHz) : %11.5lf   HIGH (MHz) : %11.5lf  *\n",
           o_tx_freq_lo, o_tx_freq_hi);
   fprintf(vfp, "%34s", "");
   fprintf(vfp, "*   BASE RX LOW (MHz) : %11.5lf   HIGH (MHz) : %11.5lf  *\n",
           o_rx_freq_lo, o_rx_freq_hi);
   fprintf(vfp, "%34s", "");
   fprintf(vfp, "*   CHANNEL SPACING (MHz) : %7.5f%29s*\n", o_channel_sep, "");
   fprintf(vfp, "%34s", "");
   fprintf(vfp, "*%63s*\n", "");
   fprintf(vfp, "%34s", "");
   fprintf(vfp, "*****************************************************************");
   fprintf(vfp, "\n\n\n");
   fprintf(vfp, "%55sBASE TX/RX FREQ (MHz)\n", "");
   fprintf(vfp, "=================================================="); 
   fprintf(vfp, "=================================================="); 
   fprintf(vfp, "============================\n\n"); 
}
