/**********************************************************************/
/*                                                                    */
/*    Module Name   :  emc_tables_maint (esemet0f.c)                  */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  <PERSON><PERSON> <PERSON><PERSON>                                      */
/*                                                                    */
/*    Callers       :  esemcn0f.c                                     */  
/*                                                                    */
/*    Parameters    :  user password                                  */
/*                  :  user type                                      */
/*                                                                    */
/*    Called Modules:  esemfbm (frequency band maintenance)           */
/*                     esemhgm (horizontal antenna gain maintenance)  */
/*                     esemvgm (vertical antenna gain maintenance)    */
/*                     esemctm (channel traffic maintenance)          */
/*                     esemwfm (weighting factor maintenance)         */
/*                     esemocm (off-channel rejection maintenance)    */
/*                     esemfam (SFX filter attenuation maintenance)   */
/*                     esemsbm (sub-district information maintenance) */
/*                     esemsdm (system description PMRS maintenance)  */
/*                     esemcfm (culling frequencies maintenance)      */
/*                     esemaum (EMC user authorisation maintenance)   */
/*                                                                    */
/*    Purpose       :  Accept user input option to run the            */
/*                     appropriate ORACLE forms program which is      */
/*                     for maintenance of the EMC table according to  */
/*                     user-chosen option.                            */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/
#include <stdlib.h>													/*20170707 Cyrus Add */
#include <stdio.h>
#include <string.h>
#include <time.h>
#include <curses.h>
#include "../include/define.h"
#include "../include/emcext.h"
#include "../include/global.h"
#include <ctype.h>

#define  ORDINARY_USER       'O'
#define  MAINTENANCE_USER    'M'
#define  SUPERVISOR          'S'

#define  MAX_FLD_LEN  3              /* max. field length of input field */

#define  LOGIN_OK     0

#define  NEXT_START_PT    0          /* field index at which user starts his */
                                     /* input for next co-site analysis      */

#define  MAINT_OPTION     10


#include "../include/screen.h"


int disp_heading (char *,char *,char *);							/*20170707 Cyrus Add */
void disp_entry_scn(int,int,int);									/*20170707 Cyrus Add */
int clear_err();													/*20170707 Cyrus Add */
void refresh_screen(int,int);										/*20170707 Cyrus Add */
void init_field(FIELD *, int *);									/*20170707 Cyrus Add */
void disp_err(char *);												/*20170707 Cyrus Add */
void disp_space(int,int,int);										/*20170707 Cyrus Add */
/*int isalnum(int);													20170707 Cyrus Add     #include <ctype.h>  no need to declar */
void show_cursor();													/*20170707 Cyrus Add */
int centre_msg(int, char *, int, int);								/*20170707 Cyrus Add */


/* field validation function declarations */
int    chk_option();

FIELD item[] = 
{
	{18,41,INTEGER,    TRUE, 3, 0, NEW, "   ",        "",0,FALSE,chk_option},
	{-1,-1,DUMMY,      TRUE, 0, 0, NEW, "",           "",0,FALSE,(int(*)())NULL}
};

char   *item_err_msg[] = 
{
   "Invalid option",
   ""
};
     
char   *prog_id = "emet0f_01";
char   *screen_head = "EMC TABLES MAINTENANCE";

char   global_user_type;

char   *getenv();


int emc_tables_maint(passwd, user_type)
char   *passwd;
char   user_type;
{
    char    err_msg[80];
    int     term_code;
    int     item_cnt;                   /* total no. of items               */
    int     last_item;                  /* index of last filled-up item     */
    int     x_pos, y_pos;               /* current cursor position          */
    int     curr_pos;                   /* current position of input string */
    int     loop;                       /* TRUE if entry continue on        */
                                        /* current item                     */
    int     modified = FALSE;           /* TRUE when current item has been  */
                                        /* modified                         */
    int     token;                      /* user-input character             */
    int     status = !(QUIT);
    int     direction = FORWARD;        /* field shuttle direction          */
    int     err_flag = FALSE;
    int     first_select = TRUE;        /* TRUE when cosite data is freshly */
                                        /* input                            */
    int     option;
    int     i;
    register int     j;
    struct tm  *tt;

    char    cmdline[150];
    char    *forms_dir;

char s[80];


/*
    initscr();
    raw();
    keypad(stdscr, TRUE); 
*/
    forms_dir = getenv("FORMS_DIR");
    global_user_type = user_type;

    for (i = 0; item[i].xpos != -1; i++)
        ;

    item_cnt = i;

    for (i = 0; status != QUIT; )
    {
        if ((!err_flag) && (direction != RESTART))
        {
            noecho();
	    attrset(A_NORMAL);
            clear();
            disp_heading(prog_id, screen_head, sys_date);
            disp_entry_scn(item_cnt, item_cnt, NEW_SCREEN);
        }
 
        x_pos = item[i].xpos; 
        y_pos = item[i].ypos;

        /**********************************************************/
        /* if data type of current field is sign field, then skip */
        /* the 1st position of current string and increment       */
        /* screen x-coordinate by 1, else keep 1st position       */
        /**********************************************************/
        if (is_sign(&item[i]))
        {
            curr_pos = 1;
            x_pos++;
        }
        else
            curr_pos = 0;
        move(y_pos, x_pos);
        loop = TRUE;

        for (; loop == TRUE;)
        {
            refresh();
            token = getch();

            if (err_flag)
            {
                err_flag = FALSE;
                attrset(A_NORMAL);
                getyx(stdscr, y_pos, x_pos);
                clear_err();
                move(y_pos, x_pos);
                attrset(A_REVERSE);
            }

            /**************************************/
            /* Function key definitions:          */
            /*    KEY_F(1)  - quit session        */
            /*    KEY_F(3)  - refresh screen      */
            /*    KEY_F(4)  - clear field         */
            /*    TAB,                            */
            /*    NEWLINE,                        */
            /*    KEY_DOWN  - next field          */
            /*    CTRL_B,                         */
            /*    KEY_UP    - next field          */
            /*    KEY_LEFT  - next char position  */
            /*    KEY_RIGHT - next char position  */
            /*    DELETE,                         */
            /*    BACKSPACE - delete current char */
            /*                and back 1 position */
            /**************************************/

            switch (token)
            {
                case KEY_F(1):
                    status = QUIT;
                    loop = FALSE;
                    break;
/*
                case KEY_F(2):
                    confirm = TRUE;
                    loop = FALSE;
                    break;
*/
                case KEY_F(3):
                    attroff(A_REVERSE);
                    refresh_screen(item_cnt, i);
                    move(y_pos, x_pos);
                    break; 

                case KEY_F(4):
                    init_field(&item[i], &curr_pos);
                    loop = FALSE; 
                    direction = RESTART; 
                    break; 

                case KEY_UP:
                case CTRL_B:
                    if (i == 0)
                        beep();
                    else
                    {
                        loop = FALSE;
                        direction = BACKWARD;
                    }
                    break;

                case KEY_DOWN:
                case NEWLINE:
                case TAB:
/*
sprintf(s," i len req: %d %d %d", i, item[i].curr_len, item[i].required);
mvaddstr(23,0,s);
                    if ((item[i].state == NEW) && (item[i].required))
                    if ((is_sign(&item[i]) && (item[i].curr_len == 1))
                     || (!is_sign(&item[i]) && empty(&item[i]))
                    && (item[i].required))
*/
                    /*********************************************************/
                    /* beep user if he wants to skip an input-required field */
                    /*********************************************************/
                    if (is_sign(&item[i]))
                    {
                        if ((item[i].curr_len == 1) && (item[i].required))
                        {
                            err_flag = TRUE;
                            disp_err(INPUT_REQ_MSG);
                            attron(A_REVERSE);
                            break;
                        }
                    }
                    else
                        if (empty(&item[i]) && (item[i].required))
                        {
                            err_flag = TRUE;
                            disp_err(INPUT_REQ_MSG);
                            attron(A_REVERSE);
                            break;
                        }

                    if (empty(&item[i]) 
                    ||  (is_sign(&item[i]) && (item[i].curr_len == 1)))
                        strcpy(item[i].curr_str, item[i].init_str);

                    loop = FALSE;
                    direction = FORWARD;
                    break;

                case KEY_LEFT:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                    }
                    break;

                case KEY_RIGHT:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* right boundary of current field  */
                    /************************************/
                    if (x_pos - item[i].xpos == item[i].curr_len)
                        beep();
                    else
                    {
                        x_pos++; curr_pos++;
                    }
                    break;

                case BACKSPACE:
                case DELETE:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                        if (is_float(&item[i]) 
                        && (item[i].curr_str[curr_pos] == DOT))
                            item[i].has_dot = FALSE;
                        move(y_pos, x_pos);
                        addch(BLANK);
                        modified = TRUE;
                    }
                    break;

                case DOT:

                    /******************************************************/
                    /* beep user if data type of current field is integer */
                    /******************************************************/
                    if (is_int(&item[i]))
                    {
                        beep();
                        break;
                    }


                    /********************************************************/
                    /* beep user if data type of current field is float and */
                    /* user has already input one dot                       */
					/* 20170710		Cyrus Add {} after if                   */
                    /********************************************************/
                    if (is_float(&item[i]))
					{
                        if (item[i].has_dot)
                        {
                            beep();
                            break;
                        }
                        else
						{
                            item[i].has_dot = TRUE;
						}
					}

                    if (item[i].state == NEW)
					
                        if (is_sign(&item[i]))
                        {
                            if (curr_pos == 1)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len - 1);
                                move(y_pos, x_pos);
                            }
                        }
                        else
                            if (curr_pos == 0)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len);
                                move(y_pos, x_pos);
                            }

                    addch(token);
                    item[i].curr_str[curr_pos] = token;
                    x_pos++; curr_pos++;
                    modified = TRUE;
                    item[i].state = MODIFIED;

						if (x_pos - item[i].xpos == item[i].fld_len)
						{
							item[i].curr_str[item[i].fld_len] = '\0';
							item[i].curr_len = item[i].fld_len;
							loop = FALSE;
							direction = FORWARD;
						}

                    break;

					default:
						if ((!isalnum(token)) && (token != MINUS) 
						&&  (token != PLUS))
						{
							beep();
							break;
						}

						if (!isdigit(token))
						{
	/*
	mvaddstr(23, 0, "not digit");
	*/
							if (is_sign(&item[i]))
							{
								if ((token != MINUS) && (token != PLUS))
								{
									beep();
									break;
								}
								else
									if (curr_pos > 1)
									{
										beep();
										break;
									}
							}
							else
	/*
	sprintf(s, "should not be integer: %d %d %s", i, item[i].type, item[i].init_str);
	mvaddstr(22, 0, s);
	*/
								if ((item[i].type != CHAR) 
								&&  (item[i].type != STRING))
								{
									beep();
									break;
								}
								else
									if (isalpha(token))
										token = toupper(token); 
						}	
						else
                        if (item[i].state == NEW)
                            if (is_sign(&item[i]))
                            {
                                if (curr_pos == 1)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len-1);
                                    move(y_pos, x_pos);
                                }
                            }
                            else
                                if (curr_pos == 0)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len);
                                    move(y_pos, x_pos);
                                }

						if (((token == MINUS) || (token == PLUS)) 
						&&  is_sign(&item[i]))
						{
							x_pos--; curr_pos--;  /* because we don't want to  */
												  /* move cursor to 1 position */
												  /* this statement is used    */
												  /* to complement the         */
												  /* 'x_pos++; curr_pos++'     */
												  /* a few lines below         */
							if (token == MINUS)
							{
								item[i].curr_str[0] = MINUS;
								move(y_pos, x_pos);
								addch(MINUS);
							}
								
						}
						else
						{
							item[i].curr_str[curr_pos] = token;
							addch(token);
						}	

/*
sprintf(s, "modified");
mvaddstr(22, 0, s);
*/
                    if ((token != MINUS) && (token != PLUS))
                        item[i].state = MODIFIED;

                    modified = TRUE;
                    x_pos++; curr_pos++;

                    if (x_pos - item[i].xpos == item[i].fld_len)
                    {
                        item[i].curr_str[item[i].fld_len] = '\0';
                        item[i].curr_len = item[i].fld_len;
                        loop = FALSE;
                        direction = FORWARD;
                    }
                
                    break;
            }

            refresh();
            if (loop == FALSE)
            {
                int    (*check_item)();

                check_item = item[i].validate;
                if (check_item != (int(*)())NULL)
                {
                    if ((direction != BACKWARD) && (!empty(&item[i])))
                        if ((*check_item)(&i) == ERROR)
                        {
                            err_flag = TRUE;
                            disp_err(item_err_msg[i]);
                            attron(A_REVERSE);
                            loop = FALSE;
                            direction = RESTART;
                        }
                }

                switch (direction)
                {
                    case FORWARD:
                        i++;
                        break;
                    case BACKWARD:
                        i--;
                        break;
                    case RESTART:
                        break;
                }

                continue;
            }
            
            if (modified && (!err_flag))
            {
                int    len = x_pos - item[i].xpos;
   
                if ((item[i].curr_len < len) || is_delete(token))
                {
                    item[i].curr_len = len;
                    item[i].curr_str[item[i].curr_len] = '\0';
                }
/*
disp_space(22,0,80);
sprintf(s,"i mode: %d %d", i, tx_mode);
mvaddstr(22,0,s);
*/

                modified = FALSE;
            }
  
            move(y_pos, x_pos);
        }

        if ((i == item_cnt) && (!err_flag))
        {
            option = atoi(item[i-1].curr_str);
            i = NEXT_START_PT;

            switch (option)
            {
                case 0 : 
                   status = QUIT;
                   break;
                case 1 : 
                   sprintf(cmdline, "runform30 %s/esemfbm -c vt220:vt220 %s/%s",
                           forms_dir, user_id, passwd);
                   system(cmdline);
                   break;
                case 2 : 
                   sprintf(cmdline, "runform30 %s/esemhgm -c vt220:vt220 %s/%s",
                           forms_dir, user_id, passwd);
                   system(cmdline);
                   break;
                case 3 : 
                   sprintf(cmdline, "runform30 %s/esemvgm -c vt220:vt220 %s/%s",
                           forms_dir, user_id, passwd);
                   system(cmdline); 
                   break;
                case 4 : 
                   sprintf(cmdline, "runform30 %s/esemctm -c vt220:vt220 %s/%s",
                           forms_dir, user_id, passwd);
                   system(cmdline); 
                   break;
                case 5 : 
                   sprintf(cmdline, "runform30 %s/esemwfm -c vt220:vt220 %s/%s",
                           forms_dir, user_id, passwd);
                   system(cmdline); 
                   break;
                case 6 : 
                   sprintf(cmdline, "runform30 %s/esemocm -c vt220:vt220 %s/%s",
                           forms_dir, user_id, passwd);
                   system(cmdline); 
                   break;
                case 7 : 
                   sprintf(cmdline, "runform30 %s/esemfam -c vt220:vt220 %s/%s",
                           forms_dir, user_id, passwd);
                   system(cmdline); 
                   break;
                case 8 : 
                   sprintf(cmdline, "runform30 %s/esemsbm -c vt220:vt220 %s/%s",
                           forms_dir, user_id, passwd);
                   system(cmdline); 
                   break;
                case 9 : 
                   sprintf(cmdline, "runform30 %s/esemsdm -c vt220:vt220 %s/%s",
                           forms_dir, user_id, passwd);
                   system(cmdline); 
                   break;
                case 10 : 
                   sprintf(cmdline, "runform30 %s/esemcfm -c vt220:vt220 %s/%s",
                           forms_dir, user_id, passwd);
                   system(cmdline); 
                   break;
                case 11 : 
                   sprintf(cmdline, "runform30 %s/esemaum -c vt220:vt220 %s/%s",
                           forms_dir, user_id, passwd);
                   system(cmdline); 
                   break;
            }

            show_cursor();
            attrset(A_NORMAL);
            disp_space(23, 0, 80);
            refresh();
            attrset(A_REVERSE);
                
            for (j = NEXT_START_PT; j < item_cnt; j++)
                init_field(&item[j], &curr_pos);
        }
    }

    attroff(A_BOLD);
    clear();
/* commented out by Chen Yung
    return;
*/
/* changed by Chen Yung */
/* original: */
/*    return; */
    return(0);


/*
force_exit:
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    getch(); clear(); show_cursor(); refresh();
    endwin();
    exit(1);
*/
}


/**************************************************************/
/* Display new entry screen or refresh current screen to user */
/**************************************************************/

void disp_entry_scn(item_cnt, curr_cnt, mode)
int    item_cnt;
int    curr_cnt;
int    mode;
{
    char    tmp_str[80];
    register int    i;

    attrset(A_NORMAL);
    mvaddstr(4,  24, "0.   Exit");
    mvaddstr(6,  24, "1.   FREQUENCY BANDS");
    mvaddstr(7,  24, "2.   HORIZONTAL ANTENNA GAIN");
    mvaddstr(8,  24, "3.   VERTICAL ANTENNA GAIN");
    mvaddstr(9,  24, "4.   MRS CHANNEL TRAFFIC");
    mvaddstr(10, 24, "5.   MRS WEIGHTING FACTORS");
    mvaddstr(11, 24, "6.   OFF-CHANNEL REJECTION");
    mvaddstr(12, 24, "7.   SFX FILTER ATTENUATION");
    mvaddstr(13, 24, "8.   SUB-DISTRICT CULLING DISTANCE");
    mvaddstr(14, 24, "9.   SYSTEM DESCRIPTION - PMRS");
    mvaddstr(15, 24, "10.  EMC CULLING FREQUENCY LIMITS");
    if (global_user_type == SUPERVISOR)
        mvaddstr(16, 24, "11.  EMC USER AUTHORISATION");
    mvaddstr(18, 24, "YOUR SELECTION : ");
 
    sprintf(tmp_str, "%s",
"F1-Quit   F3-Refresh   F4-Clear field   UP-Prev   DOWN/<Tab>-next");
 /*
    sprintf(tmp_str, "%s",
"F1-Quit   F2-Confirm   F3-Refresh   F4-Clear field   UP-Prev   DOWN/<Tab>-next");
*/
    centre_msg(21, tmp_str, A_BOLD, A_REVERSE);

    if (mode == REFRESH)
    {
        for (i = 0; i < curr_cnt; i++)
        {
            disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
            mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
        }
        for (i = curr_cnt; i < item_cnt; i++)
            if (item[i].state == NEW)
                mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);
            else
            {
                disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
                mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
            }
    }
    else
        for (i = 0; i < item_cnt; i++)
            mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);

    refresh();

}


/**************************************************************/
/* call 'disp_entry_scn' to refresh current screen to user    */
/**************************************************************/

void refresh_screen(item_cnt, curr_cnt)
int    item_cnt;
int    curr_cnt;
{
    clear();
    disp_heading(prog_id, screen_head, sys_date);
    disp_entry_scn(item_cnt, curr_cnt, REFRESH);
}


/*************************************************/
/* initialise current field to its initial value */
/*************************************************/

void init_field(p_item, curr_pos)
FIELD *p_item;
int   *curr_pos;
{
    mvaddstr(p_item->ypos, p_item->xpos, p_item->init_str);
    if (is_sign(p_item))
    {
        p_item->curr_len = *curr_pos = 1;
        p_item->curr_str[1] = '\0';
    }
    else
    {
        p_item->curr_len = *curr_pos = 0;
        p_item->curr_str[0] = '\0';
    }

    if (!disp_only(p_item))
    {
        p_item->state = NEW;
        if (is_float(p_item))
            p_item->has_dot = FALSE;
    }
}


/*********************************************************************/
/* check that input emc tables maintenance option is <= MAINT_OPTION */
/*********************************************************************/

int    chk_option(curr_cnt)
int    *curr_cnt;
{
    int    option;

    option = atoi(item[*curr_cnt].curr_str);
    if (global_user_type == SUPERVISOR)
        return((option > MAINT_OPTION + 1) ? ERROR : OK);
    else
        return((option > MAINT_OPTION) ? ERROR : OK);
}
