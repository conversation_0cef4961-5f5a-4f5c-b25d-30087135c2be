/************************************/
/*  Logical and Choice Definitions  */
/************************************/

#ifdef TRUE
#undef TRUE
#endif
#define   TRUE     1

#ifdef FALSE
#undef FALSE
#endif
#define   FALSE    0

#define BATCHED_EMC    1
#define COSITE         2

#define DESENSIT       0
#define INTERMOD       1

#define VICTIM         0
#define TRANSMITTER_1  1
#define TRANSMITTER_2  2

#define IS_PROPOSED    0
#define IS_EXIST       1


/************************************/
/*      Constants Definitions       */
/************************************/

#define MAX_POINTS         200

#define CULL_FREQ_LIMIT    3.0
#define MIN_CHANNEL_SEP    0.0125
#define MIN_ANT_HEIGHT     3.0

#define INTERMOD_ADJUST    20.0

#define CLUTTER            10.0
#define CLEARANCE_LOSS     10.0

#define FILE_OPEN_ERROR    -1
