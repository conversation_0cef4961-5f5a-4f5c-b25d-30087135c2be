EXIST  *eptr1, *eptr2, *eptr3;    /* pointers to the 2/3 intermod stations */

float  delta_dist1;               /* distance % victim and transmitter #1  */
float  delta_dist2;               /* distance % victim and transmitter #2  */
float  delta_dist3;               /* distance % victim and transmitter #3  */

float  delta_freq1;               /* freq diff % victim and transmitter #1 */
float  delta_freq2;               /* freq diff % victim and transmitter #2 */
float  delta_freq3;               /* freq diff % victim and transmitter #3 */

float  power_intr1;     /* interfering power from transmitter #1 to victim */
float  power_intr2;     /* interfering power from transmitter #2 to victim */
float  power_intr3;     /* interfering power from transmitter #3 to victim */

int    intermod_cnt;              /* no. of intermod report line printed   */

float  max_delta_freq;
float  min_delta_freq;

float  max_power_intr;
float  min_power_intr;
