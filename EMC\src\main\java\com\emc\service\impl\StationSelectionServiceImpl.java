package com.emc.service.impl;

import com.emc.constants.EmcConstants;
import com.emc.dao.StationDao;
import com.emc.dao.BaseCHDao;
import com.emc.dao.AddEqDao;
import com.emc.model.*;
import com.emc.service.StationSelectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Implementation of StationSelectionService.
 * This matches the original ProC select_station function.
 */
@Service
@Slf4j
public class StationSelectionServiceImpl implements StationSelectionService {
    
    private static final double FREQ_EPSILON = 0.0001;
    
    private final StationDao stationDao;
    private final BaseCHDao baseCHDao;
    private final AddEqDao addEqDao;
    
    // Data structures to hold selected stations and frequencies
    private List<Exist> existingStations = new ArrayList<>();
    private List<ExistFreq> frequencyList = new ArrayList<>();
    private int stationCount = 0;
    private int frequencyCount = 0;
    
    @Autowired
    public StationSelectionServiceImpl(StationDao stationDao, BaseCHDao baseCHDao, AddEqDao addEqDao) {
        this.stationDao = stationDao;
        this.baseCHDao = baseCHDao;
        this.addEqDao = addEqDao;
    }
    
    @Override
    public int selectStations(Propose prop, int cullGrid) {
        log.info("Selecting stations within culling distance {} for proposed station at grid {},{}", 
                cullGrid, prop.getEastGrid(), prop.getNorthGrid());
        
        try {
            // Clear previous selections
            existingStations.clear();
            frequencyList.clear();
            stationCount = 0;
            frequencyCount = 0;
            
            // Calculate bounding box for station selection
            int cullEast = prop.getEastGrid() + cullGrid;
            int cullWest = prop.getEastGrid() - cullGrid;
            int cullNorth = prop.getNorthGrid() + cullGrid;
            int cullSouth = prop.getNorthGrid() - cullGrid;
            
            // Find stations within bounding box
            List<Station> stations = stationDao.findStationsInBoundingBox(cullSouth, cullNorth, cullWest, cullEast);
            
            log.info("Found {} stations within bounding box", stations.size());
            
            // Process each station
            for (Station station : stations) {
                // Skip certain system types (matching original ProC logic)
                if (shouldSkipStation(station)) {
                    continue;
                }
                
                // Convert Station to Exist and add to list
                Exist exist = convertStationToExist(station);
                if (exist != null) {
                    exist.setStnNode(stationCount);
                    existingStations.add(exist);
                    
                    // Get frequencies for this station
                    List<BaseCH> baseChannels = baseCHDao.findBySystemId(
                        station.getSysCategory(), station.getSysType(), 
                        station.getSysNo(), station.getSysSuffix(), station.getBaseNo());
                    
                    // Add frequencies to frequency list
                    for (BaseCH baseCH : baseChannels) {
                        ExistFreq freq = convertBaseCHToExistFreq(baseCH, stationCount);
                        frequencyList.add(freq);
                        frequencyCount++;
                    }
                    
                    stationCount++;
                }
            }
            
            log.info("Selected {} stations with {} frequencies for EMC analysis", 
                    stationCount, frequencyCount);
            return EmcConstants.OK;
            
        } catch (Exception e) {
            log.error("Error selecting stations for EMC analysis", e);
            return EmcConstants.ERROR;
        }
    }
    
    /**
     * Determines if a station should be skipped based on system type.
     * This matches the original ProC logic for filtering certain system types.
     */
    private boolean shouldSkipStation(Station station) {
        // Skip pager systems (system types "06" and "40")
        String sysType = station.getSysType();
        return "06".equals(sysType) || "40".equals(sysType);
    }
    
    /**
     * Converts a Station object to an Exist object.
     */
    private Exist convertStationToExist(Station station) {
        try {
            Exist exist = new Exist();
            
            exist.setSysCategory(station.getSysCategory());
            exist.setSysType(station.getSysType());
            exist.setSysNo(station.getSysNo());
            exist.setSysSuffix(station.getSysSuffix());
            exist.setBaseNo(station.getBaseNo());
            exist.setEastGrid(station.getEast());
            exist.setNorthGrid(station.getNorth());
            exist.setSubDistrict(station.getSubDistrict());
            exist.setStationType(station.getStationType());
            exist.setAntHeight(station.getHalt());
            exist.setAzMaxRad(station.getAzMaxRad());
            exist.setPwDbw(station.getPwDbw());
            exist.setLicType(station.getLicType());
            exist.setLicNo(station.getLicNo());
            exist.setCancelDate(station.getCancelDate());
            
            // Get feeder loss from ADD_EQ table
            float feederLoss = addEqDao.findFeederLoss(
                station.getSysCategory(), station.getSysType(),
                station.getSysNo(), station.getSysSuffix(), station.getBaseNo());
            exist.setFeedLoss((int) feederLoss);
            
            // Set default values for fields not in Station
            exist.setDesenAttDb(0.0);
            exist.setIntmodAttDb(0.0);
            exist.setAntenna("");
            exist.setAzMaxRadR(0.0);
            exist.setSfxFilter("");
            exist.setHeightAsl(0.0);
            exist.setDistType('U'); // Default district type
            exist.setNoiseCode(0);
            exist.setDistIndex(0);
            exist.setMode('B'); // Default to both TX and RX
            
            return exist;
            
        } catch (Exception e) {
            log.error("Error converting Station to Exist", e);
            return null;
        }
    }
    
    /**
     * Converts a BaseCH object to an ExistFreq object.
     */
    private ExistFreq convertBaseCHToExistFreq(BaseCH baseCH, int stnNode) {
        ExistFreq freq = new ExistFreq();
        
        freq.setTxFreq(baseCH.getTxFreq());
        freq.setRxFreq(baseCH.getRxFreq());
        freq.setStnNode(stnNode);
        
        // Convert frequencies to channels (simplified conversion)
        freq.setTxChannel(frequencyToChannel(baseCH.getTxFreq()));
        freq.setRxChannel(frequencyToChannel(baseCH.getRxFreq()));
        
        return freq;
    }
    
    /**
     * Converts frequency to channel number (simplified conversion).
     * In a real implementation, this would use proper frequency-to-channel mapping.
     */
    private int frequencyToChannel(double frequency) {
        // This is a simplified conversion - in reality, this would depend on the band
        // and specific frequency allocation plan
        return (int) (frequency * 1000) % 10000;
    }
    
    @Override
    public List<Exist> getExistingStations() {
        return new ArrayList<>(existingStations);
    }
    
    @Override
    public List<ExistFreq> getFrequencyList() {
        return new ArrayList<>(frequencyList);
    }
    
    @Override
    public int getStationCount() {
        return stationCount;
    }
    
    @Override
    public int getFrequencyCount() {
        return frequencyCount;
    }
}
