/* @(#)cda_msg.h	6.1	(ULTRIX)	11/19/91	*/
/********************************************************************************************************************************/
/* Created  2-JUL-1990 13:37:01 by VAX SDL V3.2-12     Source:  2-JUL-1990 13:36:47 DDIF$LOCAL_ROOT:[DDIFBUILD.LIB.SRC]CDA$MSG.SD */
/********************************************************************************************************************************/
 
/*** MODULE $CDADEF ***/
/*                                                                          */
/* This SDL File Generated by VAX-11 Message V04-00 on  2-JUL-1990 13:36:48.27 */
/*                                                                          */
/*++                                                                        */
/*                                                                          */
/*   COPYRIGHT (C) 1989, 1990 BY					    */
/*   DIGITAL EQUIPMENT CORPORATION, MAYNARD, MASS.                          */
/*   ALL RIGHTS RESERVED.                                                   */
/*                                                                          */
/*   THIS SOFTWARE IS FURNISHED UNDER A LICENSE AND MAY BE USED AND  COPIED */
/*   ONLY  IN  ACCORDANCE  WITH  THE  TERMS  OF  SUCH  LICENSE AND WITH THE */
/*   INCLUSION OF THE ABOVE COPYRIGHT NOTICE.  THIS SOFTWARE OR  ANY  OTHER */
/*   COPIES  THEREOF MAY NOT BE PROVIDED OR OTHERWISE MADE AVAILABLE TO ANY */
/*   OTHER PERSON.  NO TITLE TO AND OWNERSHIP OF  THE  SOFTWARE  IS  HEREBY */
/*   TRANSFERRED.                                                           */
/*                                                                          */
/*   THE INFORMATION IN THIS SOFTWARE IS SUBJECT TO CHANGE  WITHOUT  NOTICE */
/*   AND  SHOULD  NOT  BE  CONSTRUED  AS  A COMMITMENT BY DIGITAL EQUIPMENT */
/*   CORPORATION.                                                           */
/*                                                                          */
/*   DIGITAL ASSUMES NO RESPONSIBILITY FOR THE USE OR  RELIABILITY  OF  ITS */
/*   SOFTWARE ON EQUIPMENT WHICH IS NOT SUPPLIED BY DIGITAL.                */
/*                                                                          */
/*   ABSTRACT:                                                              */
/*	THIS FILE DEFINES CONDITION VALUES RETURNED BY CDA TOOLKIT          */
/*	PROCEDURES AND THE MESSAGE TEXT.                                    */
/*                                                                          */
/*-                                                                         */
#define CDA$_FACILITY 436
/*	[START_MESSAGES]                                                    */
#define CDA$_NORMAL 28606465
/*	[IDENT]NORMAL                                                       */
/*	[MSG_TEXT]NORMAL SUCCESSFUL COMPLETION                              */
/*	[MSG_EXP]                                                           */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_DEFAULT 28606473
/*	[IDENT]DEFAULT                                                      */
/*	[MSG_TEXT]ITEM PRESENT BY DEFAULT                                   */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$LOCATE_ITEM, WHICH DETERMINED   */
/*	[MORE]THAT THE ITEM WAS PRESENT BY DEFAULT IN THE INPUT STREAM.     */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_SUSPEND 28606481
/*	[IDENT]SUSPEND                                                      */
/*	[MSG_TEXT]CONVERTER IS SUSPENDED                                    */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$CONVERT, WHICH DETERMINED THAT  */
/*	[MORE]THE BACK-END MODULE SUSPENDED CONVERSION.                     */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INHERIT 28606489
/*	[IDENT]INHERIT                                                      */
/*	[MSG_TEXT]ITEM PRESENT BY INHERITANCE                               */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$LOCATE_ITEM, WHICH DETERMINED   */
/*	[MORE]THAT THE ITEM WAS PRESENT BY INHERITANCE IN THE INPUT STREAM. */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INFINPLOG 28607267
/*	[IDENT]INFINPLOG                                                    */
/*	[MSG_TEXT]INFORMATIONAL MESSAGES PRODUCED DURING INPUT CONVERSION, SEE ERROR LOG */
/*	[MSG_EXP]THE INPUT CONVERSION COMPLETED BUT SOME INFORMATIONAL MESSAGES */
/*	[MORE]WERE PRODUCED.  REFER TO THE ERROR LOG FOR MORE DETAILS.      */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INFOUTLOG 28607275
/*	[IDENT]INFOUTLOG                                                    */
/*	[MSG_TEXT]INFORMATIONAL MESSAGES PRODUCED DURING OUTPUT CONVERSION, SEE ERROR LOG */
/*	[MSG_EXP]THE OUTPUT CONVERSION COMPLETED BUT SOME INFORMATIONAL MESSAGES */
/*	[MORE]WERE PRODUCED.  REFER TO THE ERROR LOG FOR MORE DETAILS.      */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_ENDOFDOC 28608866
/*	[IDENT]ENDOFDOC                                                     */
/*	[MSG_TEXT]END OF DOCUMENT                                           */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$GET_AGGREGATE, WHICH DETERMINED */
/*	[MORE]THAT NO MORE AGGREGATES EXIST IN THE DOCUMENT.                */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_ENDOFSEQ 28608874
/*	[IDENT]ENDOFSEQ                                                     */
/*	[MSG_TEXT]END OF SEQUENCE                                           */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$NEXT_AGGREGATE REFERENCING AN   */
/*	[MORE]AGGREGATE THAT WAS AT THE END OF A SEQUENCE.                  */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_EMPTY 28608882
/*	[IDENT]EMPTY                                                        */
/*	[MSG_TEXT]EMPTY ITEM                                                */
/*	[MSG_EXP]THE APPLICATION CALLED A CDA ACCESS PROCEDURE REFERENCING AN */
/*	[MORE]ITEM THAT IS EMPTY.                                           */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INDEX 28608890
/*	[IDENT]INDEX                                                        */
/*	[MSG_TEXT]INDEX OUT OF RANGE                                        */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$LOCATE_ITEM, CDA$STORE_ITEM, OR */
/*	[MORE]CDA$ERASE_ITEM REFERENCING AN ARRAY-VALUED ITEM, BUT THE INDEX */
/*	[MORE]IS OUT OF RANGE.                                              */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVINSERT 28608898
/*	[IDENT]INVINSERT                                                    */
/*	[MSG_TEXT]INVALID INSERT                                            */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$INSERT_AGGREGATE OR             */
/*	[MORE]CDA$STORE_ITEM REFERENCING AN AGGREGATE THAT WAS ALREADY PART */
/*	[MORE]OF A SEQUENCE, BUT WAS NOT THE FIRST AGGREGATE OF THE SEQUENCE. */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVAGGTYP 28608906
/*	[IDENT]INVAGGTYP                                                    */
/*	[MSG_TEXT]INVALID AGGREGATE TYPE                                    */
/*	[MSG_EXP]THE APPLICATION CALLED A CDA ACCESS PROCEDURE REFERENCING AN */
/*	[MORE]AGGREGATE TYPE CODE THAT IS UNDEFINED, OR AN AGGREGATE THAT HAS */
/*	[MORE]AN UNDEFINED TYPE CODE.                                       */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVITMCOD 28608914
/*	[IDENT]INVITMCOD                                                    */
/*	[MSG_TEXT]INVALID ITEM CODE                                         */
/*	[MSG_EXP]THE APPLICATION CALLED A CDA ACCESS PROCEDURE REFERENCING AN */
/*	[MORE]AGGREGATE ITEM CODE THAT IS NOT DEFINED.                      */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVDOC 28608922
/*	[IDENT]INVDOC                                                       */
/*	[MSG_TEXT]INVALID DOCUMENT SYNTAX                                   */
/*	[MSG_EXP]THE CDA ACCESS PROCEDURES DETERMINED THAT THE DOCUMENT     */
/*	[MORE]CONTAINS INVALID SYNTAX.                                      */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVITMLST 28608930
/*	[IDENT]INVITMLST                                                    */
/*	[MSG_TEXT]INVALID ITEM LIST                                         */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$OPEN_FILE OR                    */
/*	[MORE]CDA$CREATE_ROOT_AGGREGATE WITH A PROCESSING OPTIONS ITEM LIST */
/*	[MORE]THAT CONTAINED AN INVALID ITEM.                               */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_VAREMPTY 28608938
/*	[IDENT]VAREMPTY                                                     */
/*	[MSG_TEXT]EMPTY VARIANT ITEM                                        */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$LOCATE_ITEM, CDA$STORE_ITEM, OR */
/*	[MORE]CDA$ERASE_ITEM REFERENCING AN ITEM THAT HAS A VARIABLE DATA   */
/*	[MORE]TYPE.  THE ITEM THAT SPECIFIES THE DATA TYPE IS EMPTY.        */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_VARINDEX 28608946
/*	[IDENT]VARINDEX                                                     */
/*	[MSG_TEXT]VARIANT INDEX OUT OF RANGE                                */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$LOCATE_ITEM, CDA$STORE_ITEM, OR */
/*	[MORE]CDA$ERASE_ITEM REFERENCING AN ARRAY-VALUED ITEM THAT HAS A    */
/*	[MORE]VARIABLE DATA TYPE, BUT THE INDEX IS OUT OF RANGE FOR THE ITEM */
/*	[MORE]THAT SPECIFIES THE DATA TYPE.                                 */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_VARVALUE 28608954
/*	[IDENT]VARVALUE                                                     */
/*	[MSG_TEXT]VARIANT VALUE OUT OF RANGE                                */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$LOCATE_ITEM, CDA$STORE_ITEM, OR */
/*	[MORE]CDA$ERASE_ITEM REFERENCING AN ITEM THAT HAS A VARIABLE        */
/*	[MORE]DATA TYPE, BUT THE ITEM THAT SPECIFIES THE DATA TYPE HAS AN   */
/*	[MORE]INVALID VALUE.                                                */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVTAGCOD 28608962
/*	[IDENT]INVTAGCOD                                                    */
/*	[MSG_TEXT]INVALID TAG CODE                                          */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$_STORE_ITEM REFERENCING AN ITEM */
/*	[MORE]THAT HAS A SPECIAL TAG ENCODING, BUT THE VALUE OF THE ADD-INFO */
/*	[MORE]PARAMETER IS NOT DEFINED FOR THE ITEM.                        */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVBUFLEN 28608970
/*	[IDENT]INVBUFLEN                                                    */
/*	[MSG_TEXT]INVALID BUFFER LENGTH                                     */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$_STORE_ITEM REFERENCING AN ITEM */
/*	[MORE]THAT IS REQUIRED TO HAVE A SPECIFIED BUFFER LENGTH.  THE      */
/*	[MORE]VALUE OF THE BUFFER LENGTH PARAMETER IS NOT THE REQUIRED VALUE. */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_ALLOCFAIL 28608978
/*	[IDENT]ALLOCFAIL                                                    */
/*	[MSG_TEXT]MEMORY ALLOCATION FAILURE                                 */
/*	[MSG_EXP]THE STANDARD MEMORY ALLOCATION PROCEDURE                   */
/*	[MORE]FAILED TO ALLOCATE DYNAMIC MEMORY.                            */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVFUNCOD 28608986
/*	[IDENT]INVFUNCOD                                                    */
/*	[MSG_TEXT]INVALID FUNCTION CODE                                     */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$CONVERT WITH AN INVALID FUNCTION */
/*	[MORE]CODE.                                                         */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_UNSUPFMT 28608994
/*	[IDENT]UNSUPFMT                                                     */
/*	[MSG_TEXT]UNSUPPORTED DOCUMENT FORMAT                               */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$CONVERT WITH AN UNSUPPORTED     */
/*	[MORE]DOCUMENT FORMAT NAME.  THE DOCUMENT FORMAT NAME MAY BE MISSPELLED, */
/*	[MORE]OR THE REQUIRED CONVERSION MODULE MAY NOT BE INSTALLED.       */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_READONLY 28609002
/*	[IDENT]READONLY                                                     */
/*	[MSG_TEXT]AGGREGATE IS READ-ONLY                                    */
/*	[MSG_EXP]THE APPLICATION REQUESTED INPUT PROCESSING OPTIONS THAT    */
/*	[MORE]REQUIRE AN AGGREGATE TO BE READ-ONLY.  THE APPLICATION ATTEMPTED */
/*	[MORE]TO WRITE OR DELETE THE AGGREGATE.                             */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_DEFNOTFOU 28609010
/*	[IDENT]DEFNOTFOU                                                    */
/*	[MSG_TEXT]DEFINITION NOT FOUND                                      */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$FIND_DEFINITION REFERENCING AN  */
/*	[MORE]ENTITY THAT IS NOT DEFINED.                                   */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_READFAIL 28609018
/*	[IDENT]READFAIL                                                     */
/*	[MSG_TEXT]READ FAILURE                                              */
/*	[MSG_EXP]THE STANDARD READ FUNCTION HAS FAILED.                     */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_WRITFAIL 28609026
/*	[IDENT]WRITFAIL                                                     */
/*	[MSG_TEXT]WRITE FAILURE                                             */
/*	[MSG_EXP]THE STANDARD WRITE FUNCTION HAS FAILED.                    */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_OPENFAIL 28609034
/*	[IDENT]OPENFAIL                                                     */
/*	[MSG_TEXT]OPEN FAILURE                                              */
/*	[MSG_EXP]THE STANDARD OPEN FUNCTION HAS FAILED.                     */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_CLOSEFAIL 28609042
/*	[IDENT]CLOSEFAIL                                                    */
/*	[MSG_TEXT]CLOSE FAILURE                                             */
/*	[MSG_EXP]THE STANDARD CLOSE FUNCTION HAS FAILED.                    */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVSCOCOD 28609050
/*	[IDENT]INVSCOCOD                                                    */
/*	[MSG_TEXT]INVALID SCOPE CODE                                        */
/*	[MSG_EXP]THE APPLICATION CALLED CDA$ENTER_SCOPE OR CDA$LEAVE_SCOPE  */
/*	[MORE]REFERENCING A SCOPE CODE THAT IS NOT DEFINED OR INVALID       */
/*	[MORE]IN FOLLOWING CORRECT DOCUMENT SCOPING RULES.                  */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVSCOTRAN 28609058
/*	[IDENT]INVSCOTRAN                                                   */
/*	[MSG_TEXT]INVALID SCOPE TRANSITION                                  */
/*	[MSG_EXP]THE APPLICATION MADE AN CALL TO CDA$ENTER_SCOPE OR         */
/*	[MORE]CDA$LEAVE_SCOPE THAT DID NOT FOLLOW CORRECT SCOPING RULES.    */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_ERRINPLOG 28609066
/*	[IDENT]ERRINPLOG                                                    */
/*	[MSG_TEXT]ERROR MESSAGES PRODUCED DURING INPUT CONVERSION, SEE ERROR LOG */
/*	[MSG_EXP]THE INPUT CONVERSION DID NOT COMPLETE AND SOME ERROR MESSAGES */
/*	[MORE]WERE PRODUCED.  REFER TO THE ERROR LOG FOR MORE DETAILS.      */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_ERROUTLOG 28609074
/*	[IDENT]ERROUTLOG                                                    */
/*	[MSG_TEXT]ERROR MESSAGES PRODUCED DURING OUTPUT CONVERSION, SEE ERROR LOG */
/*	[MSG_EXP]THE OUTPUT CONVERSION DID NOT COMPLETE AND SOME ERROR MESSAGES */
/*	[MORE]WERE PRODUCED.  REFER TO THE ERROR LOG FOR MORE DETAILS.      */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVDATLEN 28609082
/*	[IDENT]INVDATLEN                                                    */
/*	[MSG_TEXT]INVALID DATA LENGTH                                       */
/*	[MSG_EXP]THE LENGTH OF THE VALUE DATA EXCEEDED THE SPECIFIED        */
/*	[MORE]LENGTH FOR THE DATA TYPE.                                     */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_UNSUPCNV 28609090
/*	[IDENT]UNSUPCNV                                                     */
/*	[MSG_TEXT]UNSUPPORTED DOCUMENT CONVERSION                           */
/*	[MSG_EXP]THE INPUT AND OUTPUT DOCUMENT FORMATS ARE INCOMPATIBLE FOR CONVERSION */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVOPTION 28609098
/*	[IDENT]INVOPTION                                                    */
/*	[MSG_TEXT]INVALID CONVERTER OPTION                                  */
/*	[MSG_EXP]AN INVALID OPTION WAS SPECIFIED FOR THE CONVERTER.  REFER TO THE */
/*	[MORE]DOCUMENTATION FOR THIS CONVERTER TO SEE THE VALID OPTIONS.    */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_VERSKEW 28609106
/*	[IDENT]VERSKEW                                                      */
/*	[MSG_TEXT]MAJOR VERSION SKEW BETWEEN INPUT FILE AND CDA TOOLKT.     */
/*	[MSG_EXP]THE FILE'S MAJOR VERSION IS DIFFERENT FROM THE TOOLKIT'S.  */
/*	[MORE]THUS, THE TOOLKIT CANNOT PROPERLY PROCESS THE FILE.           */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVADDINF 28609114
/*	[IDENT]INVADDINF                                                    */
/*	[MSG_TEXT]INVALID ADDITIONAL INFORMATION                            */
/*	[MSG_EXP]THE ADD-INFO PARAMETER IN A CALL TO CDA$LOCATE_ITEM OR     */
/*	[MORE]CDA$STORE_ITEM IS INVALID.                                    */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVFLTVAL 28609122
/*	[IDENT]INVFLTVAL                                                    */
/*	[MSG_TEXT]INVALID FLOATING POINT VALUE                              */
/*	[MSG_EXP]A FLOATING POINT DATUM HAS A RESERVED VALUE.               */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_FLTTRN 28609130
/*	[IDENT]FLTTRN                                                       */
/*	[MSG_TEXT]FLOATING-POINT TRUNCATION                                 */
/*	[MSG_EXP]DURING CDA$LOCATE_ITEM FOR A GENERAL FLOATING-POINT VALUE, */
/*	[MORE]FLOATING TRUNCATION OCCURRED.                                 */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVINPDMN 28609138
/*	[IDENT]INVINPDMN                                                    */
/*	[MSG_TEXT]INVALID INPUT DOMAIN                                      */
/*	[MSG_EXP]AN INVALID INPUT DOMAIN WAS SPECIFIED FOR THE FRONT        */
/*	[MORE]END.  ONLY DDIF AND DTIF ARE SUPPORTED AS DOMAINS.            */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INVOUTDMN 28609146
/*	[IDENT]INVOUTDMN                                                    */
/*	[MSG_TEXT]INVALID OUTPUT DOMAIN                                     */
/*	[MSG_EXP]AN INVALID OUTPUT DOMAIN WAS SPECIFIED FOR THE BACK        */
/*	[MORE]END.  ONLY DDIF AND DTIF ARE SUPPORTED AS DOMAINS.            */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_DCVNOTFND 28609154
/*	[IDENT]DCVNOTFND                                                    */
/*	[MSG_TEXT]DOMAIN CONVERTER NOT FOUND                                */
/*	[MSG_EXP]THE REQUIRED DOMAIN CONVERTER COULD NOT BE FOUND.          */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_ICVNOTFND 28609162
/*	[IDENT]ICVNOTFND                                                    */
/*	[MSG_TEXT]INPUT CONVERTER NOT FOUND                                 */
/*	[MSG_EXP]THE SPECIFIED INPUT CONVERTER COULD NOT BE FOUND.          */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_OCVNOTFND 28609170
/*	[IDENT]OCVNOTFND                                                    */
/*	[MSG_TEXT]OUTPUT CONVERTER NOT FOUND                                */
/*	[MSG_EXP]THE SPECIFIED OUTPUT CONVERTER COULD NOT BE FOUND.         */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
#define CDA$_INTERR 28609668
/*	[IDENT]INTERR                                                       */
/*	[MSG_TEXT]INTERNAL ERROR                                            */
/*	[MSG_EXP]THE CDA TOOLKIT DETECTED AN INTERNAL ERROR.                */
/*	[MSG_ACT]                                                           */
/*	[END_MSG]                                                           */
/*	[END_MESSAGES]                                                      */
