#ifndef ERROR
#define ERROR    1
#endif

#ifndef OK
#define OK       0
#endif

#ifndef  DUMMY
#define  DUMMY        -1
#endif

/*   field state   */
#define  NEW          0
#define  MODIFIED     1
#define  DISP         2

/*   field data type   */
#define  CHAR         0
#define  FLOAT        1
#define  FREQ         2
#define  INTEGER      3
#define  SIGN_FLOAT   4
#define  SIGN_INTEGER 5
#define  STRING       6
#define  GRID         7

/*   useful character constants   */
#define  BLANK        ' '
#define  ZERO         '0'
#define  DOT          '.'
#define  MINUS        '-'
#define  PLUS         '+'

/*   special character constants   */
#define  BACKSPACE    '\b'
#define  DELETE       127
#define  NEWLINE      '\n'
#define  TAB          '\t'
#define  CTRL_B       2

/*   screen movement directions   */
#define  BACKWARD     0
#define  FORWARD      1
#define  RESTART      2

/*   clear screen mode   */
#define  NEW_SCREEN   0
#define  REFRESH      1

#define  QUIT         1

/*   strip blank mode   */
#define  TRAIL          0
#define  LEADING        1

/*  field attributes checking functions  */
#define  empty(p)     (((p)->curr_len == 0)? TRUE : FALSE)
#define  is_float(p)  (((p)->type == FLOAT) || ((p)->type == SIGN_FLOAT) || \
                       ((p)->type == FREQ))
#define  is_freq(p)   ((p)->type == FREQ)
#define  is_int(p)    (((p)->type == INTEGER) || ((p)->type == SIGN_INTEGER))
#define  is_num(p)    (is_float(p) || is_int(p))
#define  is_sign(p)   (((p)->type == SIGN_INTEGER) || ((p)->type == SIGN_FLOAT))
#define  is_string(p) ((p)->type == STRING)
#define  disp_only(p) ((p)->state == DISP)
#define  is_delete(t) (((t) == DELETE) || ((t) == BACKSPACE))

#define  INPUT_REQ_MSG  "Field input required"


struct item_tag
{
    int    ypos;                     /* y-position on screen          */
    int    xpos;                     /* y-position on screen          */
    int    type;                     /* field data type               */
    int    required;                 /* input required: TRUE or FALSE */
    int    fld_len;                  /* max. field length             */
    int    dec_place;                /* decimal place in curr_str     */ 
    int    state;                    /* field state: NEW or MODIFIED  */
    char   init_str[MAX_FLD_LEN];    /* initial field value           */
    char   curr_str[MAX_FLD_LEN];    /* current field value           */
    int    curr_len;                 /* current field length          */
    int    has_dot;                  /* has a dot in curr_str (only   */
                                     /* apply to FLOAT and SIGN_FLOAT */
    int    (*validate)();            /* pointer to field validation   */
                                     /* function                      */
};

typedef struct item_tag  FIELD;
