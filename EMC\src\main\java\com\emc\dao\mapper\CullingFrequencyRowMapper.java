package com.emc.dao.mapper;

import com.emc.model.CullingFrequency;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for CullingFrequency entity.
 */
public class CullingFrequencyRowMapper implements RowMapper<CullingFrequency> {
    
    @Override
    public CullingFrequency mapRow(ResultSet rs, int rowNum) throws SQLException {
        CullingFrequency cullingFrequency = new CullingFrequency();
        cullingFrequency.setDesenCull(rs.getFloat("DESENSITISATION"));
        cullingFrequency.setIntmod2Cull(rs.getFloat("SIGNAL_2_INTERMOD"));
        cullingFrequency.setIntmod3Cull(rs.getFloat("SIGNAL_3_INTERMOD"));
        return cullingFrequency;
    }
}
