echo "-------esemagmr"
make  -f new_proc.mk EXE=esemagmr OBJS='esemagmr.o utility.o login.o' build

echo "-------esemba0x"
rm esemimi0r.o
make -f new_proc.mk EXE=esemba0x OBJS='esemba0x.o esemrl0x.o esemtn0x.o esemco0r.o esemin0x.o esemss0x.o esemda0r.o esemsb0x.o esemgd0x.o esemdc0r.o esemdl0x.o esemim0r.o utility.o login.o' build


echo "-------esembe0f"
make -f new_curses.mk EXE=esembe0f OBJS='esembe0f.o screen.o utility.o login.o' build

echo "-------esemcn0f"
make -f new_curses.mk EXE=esemcn0f OBJS='esemcn0f.o esemfp0f.o esemet0f.o screen.o utility.o login.o' build

echo "-------esemcs0f"
rm esemim0r.o
make -f new_curses.mk EXE=esemcs0f OBJS='esemcs0f.o esemsl0r.o screen.o utility.o login.o esemim0r.o esemsb0x.o' build

echo "-------esemct0x"
make -f new_proc.mk EXE=esemct0x OBJS='esemct0x.o utility.o login.o' build

echo "-------esemctmr"
make -f new_proc.mk EXE=esemctmr OBJS='esemctmr.o utility.o login.o' build

echo "-------esemep0f"
make -f new_curses.mk EXE=esemep0f OBJS='esemep0f.o screen.o utility.o login.o' build

echo "-------esemer0f"
make -f new_curses.mk EXE=esemer0f OBJS='esemer0f.o screen.o utility.o' build

echo "-------esemfamr"
make -f new_proc.mk EXE=esemfamr OBJS='esemfamr.o utility.o login.o' build

echo "-------esemfp0f"
#make -f new_curses.mk EXE=esemfp0f OBJS='esemet0f.o esemcn0f.o esemfp0f.o esemct0x.o screen.o utility.o login.o'
make -f new_curses.mk EXE=esemfp0f OBJS='esemet0f.o esemfp0f.o esemct0x.o screen.o utility.o login.o' build

echo "-------esemns0f"
make -f new_curses.mk EXE=esemns0f OBJS='esemns0f.o screen.o utility.o login.o' build
echo "-------esemsc0f"
make -f new_curses.mk EXE=esemsc0f OBJS='esemsc0f.o screen.o utility.o login.o' build
echo "-------esemvc0f"
make -f new_curses.mk EXE=esemvc0f OBJS='esemvc0f.o screen.o utility.o login.o' build
echo "-------"

