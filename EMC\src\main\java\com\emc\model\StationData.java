package com.emc.model;

import lombok.Data;
import java.util.List;

@Data
public class StationData {
    private String emcUid;           // Characters 0-18
    private int eastGrid;            // Characters 19-23
    private int northGrid;           // Characters 24-28
    private String subDistrict;      // Characters 29-31
    private char stationType;        // Character 32
    private String antenna;          // Characters 33-34
    private int azMaxRad;           // Characters 35-37
    private int antennaHeight;      // Characters 38-40
    private String powerDbw;        // Characters 41-45
    private String feedLoss;        // Characters 46-48
    private String desenAtten;      // Characters 49-53
    private String intmodAtten;     // Characters 54-58
    private String sfxFilter;       // Characters 59-68
    private List<FrequencyPair> frequencies; // Starting from character 69
}