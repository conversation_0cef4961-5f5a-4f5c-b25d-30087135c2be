package com.emc.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * JDBC configuration for the EMC application.
 */
@Configuration
@EnableTransactionManagement
public class JdbcConfig {
    
    /**
     * Creates a NamedParameterJdbcTemplate bean.
     * 
     * @param dataSource The data source
     * @return NamedParameterJdbcTemplate instance
     */
    @Bean
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate(DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }
    
    /**
     * Creates a transaction manager for JDBC operations.
     * 
     * @param dataSource The data source
     * @return PlatformTransactionManager instance
     */
    @Bean
    public PlatformTransactionManager transactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
