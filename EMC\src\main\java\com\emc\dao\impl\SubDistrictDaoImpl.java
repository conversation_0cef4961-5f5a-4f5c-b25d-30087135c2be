package com.emc.dao.impl;

import com.emc.dao.SubDistrictDao;
import com.emc.dao.mapper.SubDistrictRowMapper;
import com.emc.model.SubDistrict;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * JDBC implementation of SubDistrictDao.
 */
@Repository
@Slf4j
public class SubDistrictDaoImpl implements SubDistrictDao {
    
    private final JdbcTemplate jdbcTemplate;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final SubDistrictRowMapper rowMapper;
    
    // SQL queries
    private static final String SELECT_ALL = 
        "SELECT SUBDISTRICT, NOISE_CODE, DISTRICT_TYPE, LOW_VHF_CULL_REG, LOW_VHF_CULL_HD, " +
        "VHF_CULL_REG, VHF_CULL_HD, UHF_CULL_REG, UHF_CULL_HD, UHF_800_CULL_REG, UHF_800_CULL_HD " +
        "FROM SUBDISTRICT_TAB";
    
    private static final String SELECT_BY_CODE = SELECT_ALL + " WHERE SUBDISTRICT = ?";
    private static final String SELECT_BY_DIST_TYPE = SELECT_ALL + " WHERE DISTRICT_TYPE = ?";
    private static final String SELECT_BY_NOISE_CODE = SELECT_ALL + " WHERE NOISE_CODE = ?";
    private static final String SELECT_ALL_ORDERED = SELECT_ALL + " ORDER BY SUBDISTRICT";
    
    private static final String INSERT = 
        "INSERT INTO SUBDISTRICT_TAB (SUBDISTRICT, NOISE_CODE, DISTRICT_TYPE, LOW_VHF_CULL_REG, " +
        "LOW_VHF_CULL_HD, VHF_CULL_REG, VHF_CULL_HD, UHF_CULL_REG, UHF_CULL_HD, UHF_800_CULL_REG, UHF_800_CULL_HD) " +
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    private static final String UPDATE = 
        "UPDATE SUBDISTRICT_TAB SET NOISE_CODE = ?, DISTRICT_TYPE = ?, LOW_VHF_CULL_REG = ?, " +
        "LOW_VHF_CULL_HD = ?, VHF_CULL_REG = ?, VHF_CULL_HD = ?, UHF_CULL_REG = ?, UHF_CULL_HD = ?, " +
        "UHF_800_CULL_REG = ?, UHF_800_CULL_HD = ? WHERE SUBDISTRICT = ?";
    
    private static final String DELETE = "DELETE FROM SUBDISTRICT_TAB WHERE SUBDISTRICT = ?";
    private static final String EXISTS = "SELECT COUNT(*) FROM SUBDISTRICT_TAB WHERE SUBDISTRICT = ?";
    private static final String COUNT = "SELECT COUNT(*) FROM SUBDISTRICT_TAB";
    
    @Autowired
    public SubDistrictDaoImpl(JdbcTemplate jdbcTemplate, NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.rowMapper = new SubDistrictRowMapper();
    }
    
    @Override
    public SubDistrict save(SubDistrict entity) {
        log.debug("Saving SubDistrict: {}", entity.getSubDistrictCode());
        
        jdbcTemplate.update(INSERT,
            entity.getSubDistrictCode(),
            entity.getNoiseCode(),
            entity.getDistType(),
            entity.getLowVhfReg(),
            entity.getLowVhfHd(),
            entity.getVhfReg(),
            entity.getVhfHd(),
            entity.getUhfReg(),
            entity.getUhfHd(),
            entity.getUhf800Reg(),
            entity.getUhf800Hd()
        );
        
        return entity;
    }
    
    @Override
    public SubDistrict update(SubDistrict entity) {
        log.debug("Updating SubDistrict: {}", entity.getSubDistrictCode());
        
        int rowsAffected = jdbcTemplate.update(UPDATE,
            entity.getNoiseCode(),
            entity.getDistType(),
            entity.getLowVhfReg(),
            entity.getLowVhfHd(),
            entity.getVhfReg(),
            entity.getVhfHd(),
            entity.getUhfReg(),
            entity.getUhfHd(),
            entity.getUhf800Reg(),
            entity.getUhf800Hd(),
            entity.getSubDistrictCode()
        );
        
        if (rowsAffected == 0) {
            throw new RuntimeException("SubDistrict not found for update: " + entity.getSubDistrictCode());
        }
        
        return entity;
    }
    
    @Override
    public Optional<SubDistrict> findById(String id) {
        return findBySubDistrictCode(id);
    }
    
    @Override
    public List<SubDistrict> findAll() {
        log.debug("Finding all SubDistricts");
        return jdbcTemplate.query(SELECT_ALL, rowMapper);
    }
    
    @Override
    public boolean deleteById(String id) {
        log.debug("Deleting SubDistrict: {}", id);
        int rowsAffected = jdbcTemplate.update(DELETE, id);
        return rowsAffected > 0;
    }
    
    @Override
    public boolean existsById(String id) {
        Integer count = jdbcTemplate.queryForObject(EXISTS, Integer.class, id);
        return count != null && count > 0;
    }
    
    @Override
    public long count() {
        Integer count = jdbcTemplate.queryForObject(COUNT, Integer.class);
        return count != null ? count : 0;
    }
    
    @Override
    public Optional<SubDistrict> findBySubDistrictCode(String subDistrictCode) {
        log.debug("Finding SubDistrict by code: {}", subDistrictCode);
        try {
            SubDistrict subDistrict = jdbcTemplate.queryForObject(SELECT_BY_CODE, rowMapper, subDistrictCode);
            return Optional.ofNullable(subDistrict);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    @Override
    public List<SubDistrict> findByDistType(String distType) {
        log.debug("Finding SubDistricts by district type: {}", distType);
        return jdbcTemplate.query(SELECT_BY_DIST_TYPE, rowMapper, distType);
    }
    
    @Override
    public List<SubDistrict> findByNoiseCode(int noiseCode) {
        log.debug("Finding SubDistricts by noise code: {}", noiseCode);
        return jdbcTemplate.query(SELECT_BY_NOISE_CODE, rowMapper, noiseCode);
    }
    
    @Override
    public List<SubDistrict> findAllOrderBySubDistrictCode() {
        log.debug("Finding all SubDistricts ordered by code");
        return jdbcTemplate.query(SELECT_ALL_ORDERED, rowMapper);
    }
}
