/**********************************************************************/
/*                                                                    */
/*    Module Name   :  utility.c                                      */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C<PERSON> <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  EMC-related programs                           */  
/*                                                                    */
/*    Parameters    :                                                 */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  This programs contains a number of useful      */
/*                     common functions. The following is a list      */
/*                     of these functions:                            */
/*                         get_sys_date_time                          */
/*                         strip_blank                                */
/*                         pad_space                                  */
/*                         pad_n_space                                */
/*                         cal_dist                                   */
/*                         file_exist                                 */
/*                         str_toupper                                */
/*                         isnumber                                   */
/*                         is_space                                   */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdio.h>
#include <time.h>
#include <string.h>
#include <math.h>

#ifndef   TRUE
#define  TRUE    1
#endif

#ifndef   FALSE
#define  FALSE   0
#endif

#define  TRAIL          0
#define  LEADING        1

#define  BLANK          32


/**********************************************************************/
/*    get system date (yymmdd) and system time (hhmmss)               */
/**********************************************************************/

get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss)
char *sys_date, *sys_time, *yymmdd, *hhmmss;
{
    time_t        sys_sec;
    struct tm     *tt;

    sys_sec = time((long *)0);
    tt = localtime(&sys_sec);
    sprintf(sys_date, "%02d/%02d/%02d", tt->tm_mday, tt->tm_mon+1, tt->tm_year%100);
    sprintf(sys_time, "%02d:%02d:%02d", tt->tm_hour, tt->tm_min, tt->tm_sec);
    sprintf(yymmdd, "%02d%02d%02d", tt->tm_year, tt->tm_mon+1, tt->tm_mday);
    sprintf(hhmmss, "%02d%02d%02d", tt->tm_hour, tt->tm_min, tt->tm_sec);
}


/**********************************************************************/
/*    remove leading or trailing blanks of 'str' depending on 'mode'  */
/**********************************************************************/

strip_blank(str, mode)
char  *str;
int    mode;
{
    char   tmp_str[512];
    int    i;
    int    slen;

    slen = strlen(str);

    if (mode == TRAIL)
    {
        for (i = slen - 1; (i >= 0) && (str[i] == BLANK); i--)
            ;
        str[i+1] = '\0';
    }

    if (mode == LEADING)
    {
        strcpy(tmp_str, str);
        for (i = 0; (i < slen) && (tmp_str[i] == BLANK); i++)
            ;
        if (i < slen)
            strcpy(str, tmp_str+i); 
    }
}


/*****************************************************************/
/*    pad space to 'str' using 'len' as terminator               */
/*****************************************************************/

pad_space(str, len)
char  *str;
int    len;
{
    int   i; 


    for (i = strlen(str); i < len; i++)
        str[i] = BLANK;

    str[len] = '\0';
}


/*****************************************************************/
/*    pad n spaces to 'str'                                      */
/*****************************************************************/

pad_n_space(str, n)
char  *str;
int    n;
{
    int   i, j; 


    for (i = strlen(str), j = 0; j < n; i++, j++)
        str[i] = BLANK;

    str[i] = '\0';
}


/*****************************************************************/
/*    search 1st/last occurrence of 'c' in 's', depending on     */
/*    'direction'                                                */
/*****************************************************************/

chr_in_str(str, c, direction)
char   *str;
char   c;
int    direction;
{
    char   s[80];
    int    len;
    register int    i, j;

    strcpy(s, str);
    len = strlen(s);

    if (direction == TRAIL)
    {
        for (i = len, j = 0; i >= 0; i--, j++)
            if (s[i] == c)
                break;
    }
    else
    {
        for(i = 0; i < len; i++)
            if (s[i] == c)
                break;
    }
     
    return((direction == LEADING)? i : j);
}
    
/*****************************************************************
 *            C a l    D i s t                                   *
 *****************************************************************/
/*
c++
c   This  calculates the distanse in km between two hong kong grid locations.
c   Note that the input is 4-digit grid.
c--
*/
float cal_dist (grida, gridb)
float grida[2],gridb[2];            
{ float f;
  struct {double x,y;} z;
/*  float  cabs(); */

  z.x = (double)(grida[0]-gridb[0]);
  z.y = (double)(grida[1]-gridb[1]);
/*  f = (float)(0.01 * cabs(z)); */
  f = (float)(0.01 * sqrt((z.x * z.x) + (z.y * z.y)));

  return (f < 0.001 ? 0.001 : f); 
/*
  return ((f = 0.01 * cabs(z)) < 0.001 ? 0.001 : f); 
*/
}


/*****************************************************************/
/*    determine whether the file 'fname' exists or not           */
/*****************************************************************/

file_exist(fname)
char   *fname;
{
    FILE   *fp;

    if ((fp = fopen(fname, "r")) == (FILE *)NULL)
        return FALSE;

    fclose(fp);
/*    fclose(fname); */
    return TRUE;
}


/*****************************************************************/
/*    convert the whole string to uppercase                      */
/*****************************************************************/

str_toupper(str)
char    *str;
{
    int    i;

    for (i = 0; str[i] != '\0'; i++)
        str[i] = toupper(str[i]);
}


/*****************************************************************/
/*    determine whether string 'cptr' represents a number        */
/*****************************************************************/

isnumber(cptr)
char *cptr;
{
  int state=0;

  while (*cptr!='\0')
  { switch (state) 
    { case 0:  if (isspace(*cptr))
                 break;
               else
                 state = 1; 
      case 1:  if (*cptr=='.')
                 state = 2;
               else if (isspace(*cptr))
                 state = 3; 
               else if (!isdigit(*cptr))
                 state = 9; 
               break;
      case 2:  if (isdigit(*cptr))
                 state = 2;
               else if (isspace(*cptr))
                 state = 3; 
               else
                 state = 9; 
               break;
      case 3:  if (!isspace(*cptr))
                 state = 9;
               break;
    }
    cptr++;
  }
  return (state!=9);
}


/*****************************************************************/
/*    determine whether string 'cptr' is filled with spaces      */
/*****************************************************************/

is_space(cptr)
char *cptr;
{
    int    len, i;

    len = strlen(cptr);

    for (i = 0; i < len; i++)
        if (cptr[i] != BLANK)
            break;

   return((i == len) ? TRUE : FALSE);
}
