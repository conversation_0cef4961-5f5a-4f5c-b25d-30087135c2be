/**********************************************************************/
/*                                                                    */
/*    Module Name   :  get_terrain_profile (esemtn0x.pc)              */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  <PERSON><PERSON> <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  get_diffract_loss (esemdl0x.c)                 */  
/*                                                                    */
/*    Parameters    :  grid of transmitting station                   */
/*                     grid of receiving station                      */
/*                                                                    */
/*    Called Modules:  cal_dist (utility.c)                           */
/*                     get_local_height (esemtn0x.pc)                 */
/*                                                                    */
/*    Purpose       :  Derive the intermediate points info. from      */
/*                     transmitting station to receiving station.     */
/*                     Info. is stored in the following arrays:       */
/*                         grid,                                      */
/*                         dist (distance from Tx to Rx station),     */
/*                         height                                     */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include "../include/emcext.h"
#include "../include/macros.h"
#include "../include/define.h"
#include "../include/east.h"
#include "../include/north.h"
#include "../include/trainext.h"
#include "../include/presuext.h"

#define  BASE_EAST    110.
#define  BASE_NORTH   465.
#define  STEP         15.24






float    cal_dist();
float    get_local_height();

extern char msg[];

int get_terrain_profile(grid_a, grid_b)
float  grid_a[], grid_b[];
{
    float  grid[2];
    float  grid_1[2], grid_2[2];
    float  east_dist, north_dist;
    float  east_inc, north_inc;
    int    i;


/*
#ifdef DEBUG
    printf("get_terrain_profile\n");
#endif
*/

    grid_1[0] = grid_a[0] / 10;
    grid_1[1] = grid_a[1] / 10;
    grid_2[0] = grid_b[0] / 10;
    grid_2[1] = grid_b[1] / 10;

    east_dist  = grid_2[0] - grid_1[0];
    north_dist = grid_2[1] - grid_1[1];

    n_points = (int)max(abs(east_dist)/STEP, abs(north_dist)/STEP);
    n_points = max(n_points, 1);

    east_inc  = east_dist / n_points;
    north_inc = north_dist / n_points;

    grid[0] = grid_1[0];
    grid[1] = grid_1[1];

/*
printf("n_points east_in north_inc: %d %f %f\n", n_points, east_inc, north_inc);
*/
    for (i = 0; i < n_points - 1; i++)
    {
        grid[0]   = grid[0] + east_inc;
        grid[1]   = grid[1] + north_inc;
        dist[i]   = cal_dist(grid, grid_1) * 1000;
        height[i] = get_local_height(grid[0], grid[1]);

/*
system("echo \"esemtn0x height\" >> /tmp/debug");
sprintf(msg, "echo \"%f %f\" >> /tmp/debug", height[i], dist[i]);
system(msg);
*/

/*
printf("height dist: %f %f\n", height[i], dist[i]);
*/
    }

exit(1);


}


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  get_local_height (esemtn0x.pc)                 */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  main (esemba0x.c)                              */  
/*                     select_station (esemss0x.pc)                   */  
/*                     get_terrain_profile (esemtn0x.pc)              */  
/*                                                                    */
/*    Parameters    :  grid_east                                      */
/*                     grid_north                                     */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Determine the terrain local height of the      */
/*                     given point by either getting the height       */
/*                     from FINES or by interpolation from points     */
/*                     obtained from TERRAIN.                         */
/*                     Note that all calculations still assume        */
/*                     4-digit grid point (ie., east-2300 is treated  */
/*                     as east-230). Only when referring to TERRAIN   */
/*                     and FINES that grid is treated as 5 digits     */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

float  get_local_height(grid_east, grid_north)
float  grid_east, grid_north;
{
    float  loc_height;
    float  east_ref, north_ref;
    float  hp1, hp2;
    float  x, y;
    int    i;
    int    fine_terrain_required = 0;

    EXEC SQL BEGIN DECLARE SECTION;

    int    o_grid_east;
    int    o_grid_north;
    int    o_loc_height;
    int    o_grid_east_r;
    int    o_grid_north_r;
    int    o_h1; 
    int    o_h2;
    int    o_h3;
    int    o_h4;

    EXEC SQL END DECLARE SECTION;

    EXEC SQL INCLUDE SQLCA;


    EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn;
    EXEC SQL WHENEVER NOT FOUND CONTINUE;

#ifdef DEBUG
    printf("get_local_height: %f %f\n", grid_east, grid_north);
#endif

    /**********************************************************************/
    /*  check whether the given point is within the fine terrain area     */
    /*  if yes, use FINES to obtain local terrain height                  */
    /**********************************************************************/

    for (i = 0; i < fine_terrain_cnt; i++)
        if ((grid_east > (float)fine_terrain_east[i] - 7)
        &&  (grid_east < (float)fine_terrain_east[i] + 7)
        &&  (grid_north > (float)fine_terrain_north[i] - 7)
        &&  (grid_north < (float)fine_terrain_north[i] + 7))
        {
            fine_terrain_required = 1;
            break;
        }

    if (fine_terrain_required)
    {
       o_grid_east  = (int)(grid_east - 1);
       o_grid_north = (int)(grid_north - 1); 
       o_grid_east  = o_grid_east * 10;
       o_grid_north = o_grid_north * 10;

/*
system("echo \"esemtn0x 1\" >> /tmp/debug");
sprintf(msg, "echo \"%d %d\" >> /tmp/debug", o_grid_north, o_grid_east);
system(msg);
*/
       EXEC SQL
            SELECT DISTINCT H1 
            into   :o_loc_height
            FROM   FINES
            WHERE  (NORTH >= :o_grid_north)
            AND    (EAST >= :o_grid_east);
/*
            WHERE  (NORTH*100000 + EAST) >= 
                   (:o_grid_north*100000 + :o_grid_east);
*/

       if (sqlca.sqlcode == NOT_FOUND)
       {
           printf("No FINES record matched for east/north grid: %d/%d\n",
                  o_grid_east, o_grid_north);
           exit(1);
       }

/*
       printf("fine_terrain: %f\n", o_loc_height);
*/
       return((float)o_loc_height);
    }


    /**********************************************************************/
    /*  else, use interpolation and obtain local terrain height from      */
    /*  TERRAIN                                                           */
    /**********************************************************************/
    east_ref  = BASE_EAST + STEP * (1.+(int)((grid_east-BASE_EAST)/STEP));
    north_ref = BASE_NORTH + STEP * (1.+(int)((grid_north-BASE_NORTH)/STEP));

    o_grid_east  = (int)(east_ref + 0.5) * 10;
    o_grid_north = (int)(north_ref + 0.5) * 10;

/*
system("echo \"esemtn0x 2\" >> /tmp/debug");
sprintf(msg, "echo \"%d %d\" >> /tmp/debug", o_grid_north, o_grid_east);
system(msg);
*/

    EXEC SQL
         SELECT EAST, NORTH, H1, H2, H3, H4
         INTO   :o_grid_east_r, :o_grid_north_r,
                :o_h1, :o_h2, :o_h3, :o_h4
         FROM   TERRAIN
         WHERE  NORTH = :o_grid_north
         AND    EAST  = :o_grid_east;

    /**********************************************************************/
    /*  if no TERRAIN record matched, use ieast[] and inorth[] and search */
    /*  TERRAIN again                                                     */
    /**********************************************************************/
    if (sqlca.sqlcode == NOT_FOUND)
    {
        o_grid_east  = (int)grid_east;
        o_grid_north = (int)grid_north;
/*
        for (i = 0; i < 390; i++)
            if (ieast[i] >= o_grid_east) break;
        if (i >= 390)
*/
        for (i = 0; ieast[i] != DUMMY; i++)
            if (ieast[i] >= o_grid_east)
                break;
        if (ieast[i] == DUMMY)
        {
            fprintf(afp, "No TERRAIN in (ieast) for east/north grid: %d/%d\n",
                   o_grid_east, o_grid_north);
            fflush(afp);
            exit(1);
        }
        o_grid_east = ieast[i] * 10;

/*
        for (i = 0; i < 290; i++)
            if (inorth[i] >= o_grid_north) break;
        if (i >= 290)
*/
        for (i = 0; inorth[i] != DUMMY; i++)
            if (inorth[i] >= o_grid_north) 
                break;
        if (inorth[i] == DUMMY)
        {
            fprintf(afp, "No TERRAIN in (inorth) for east/north grid: %d/%d\n",
                   o_grid_east, o_grid_north);
            fflush(afp);
            exit(1);
        }
        o_grid_north = inorth[i] * 10;

/*
system("echo \"esemtn0x 3\" >> /tmp/debug");
sprintf(msg, "echo \"%d %d\" >> /tmp/debug", o_grid_north, o_grid_east);
system(msg);
*/

        EXEC SQL
             SELECT EAST, NORTH, H1, H2, H3, H4
             INTO   :o_grid_east_r, :o_grid_north_r,
                    :o_h1, :o_h2, :o_h3, :o_h4
             FROM   TERRAIN
             WHERE  NORTH = :o_grid_north
             AND    EAST  = :o_grid_east;

       if (sqlca.sqlcode == NOT_FOUND)
       {
           fprintf(afp, "No TERRAIN record for east/north grid: %d/%d\n",
                  o_grid_east, o_grid_north);
           fflush(afp);
           exit(1);
       }
    }

    o_grid_east_r  = o_grid_east_r / 10;
    o_grid_north_r = o_grid_north_r / 10;

    y = (o_grid_north_r - grid_north) / STEP;
    x = (o_grid_east_r - grid_east) / STEP;

    hp1 = o_h1 + (o_h2 - o_h1) * y;
    hp2 = o_h4 + (o_h3 - o_h4) * y;

/*
    printf("east_ref north_ref o_grid_east o_grid_north: %f %f %d %d\n", 
           east_ref,north_ref,o_grid_east_r,o_grid_north_r);
    printf("o_h1 o_h2 o_h3 o_h4 y x : %d %d %d %d %f %f\n",
           o_h1,o_h2,o_h3,o_h4,y,x);
    printf("hp1 hp2 (hp1 + (hp2 - hp1) * x): %f %f %f\n",
    hp1, hp2, (hp1 + (hp2 - hp1) * x));
*/

    return(hp1 + (hp2 - hp1) * x);


sqlerr_rtn:
    printf("%s\n", sqlca.sqlerrm.sqlerrmc);
    exit(1);

}
