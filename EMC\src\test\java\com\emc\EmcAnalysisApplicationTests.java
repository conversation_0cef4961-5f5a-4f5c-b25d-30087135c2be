package com.emc;

import com.emc.model.EmcAnalysisRequest;
import com.emc.model.EmcAnalysisResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
public class EmcAnalysisApplicationTests {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void contextLoads() {
    }

    @Test
    void testEmcAnalysisEndToEnd() throws Exception {
        // Setup
        EmcAnalysisRequest request = new EmcAnalysisRequest();
        request.setPrinterId("printer1");
        request.setPrintFile("test_print");
        request.setEmcUid("TEST_EMC_UID");
        request.setInteractive(false);
        
        // Sample batch content with one station
        String batchContent = 
            "TEST_EMC_UID       12345678901234567890" +
            "12345" + // East grid
            "67890" + // North grid
            "ABC" +   // Sub-district
            "X" +     // Station type
            "YZ" +    // Antenna
            "123" +   // Azimuth
            "045" +   // Antenna height
            "+1234" + // Power in dBW
            "123" +   // Feed loss
            "+1234" + // Desensitization attenuation
            "+1234" + // Intermodulation attenuation
            "FILTER123" + // SFX filter
            "12345678901" + // TX frequency
            "12345678901";  // RX frequency
        
        request.setBatchContent(batchContent);
        
        // Execute
        MvcResult result = mockMvc.perform(post("/api/emc/analyze")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andReturn();
        
        // Parse the response
        String responseContent = result.getResponse().getContentAsString();
        EmcAnalysisResult analysisResult = objectMapper.readValue(responseContent, EmcAnalysisResult.class);
        
        // Verify
        assertNotNull(analysisResult);
        assertEquals("TEST_EMC_UID", analysisResult.getEmcUid());
        // Note: In a real integration test, we would verify more details about the result
    }
}
