package com.emc;

import com.emc.model.EmcAnalysisRequest;
import com.emc.model.EmcAnalysisResult;
import com.emc.model.Exist;
import com.emc.model.ExistFreq;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
public class EmcAnalysisApplicationTests {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void contextLoads() {
    }

    @Test
    void testEmcAnalysisEndToEnd() throws Exception {
        // Setup
        EmcAnalysisRequest request = new EmcAnalysisRequest();
        request.setEmcUid("TEST_EMC_UID");
//        request.setInteractive(false);
        
        // Create test batch content with Exist objects
        Exist exist = new Exist();
        exist.setEmcUid("TEST_EMC_UID");
        exist.setEastGrid(12345);
        exist.setNorthGrid(67890);
        exist.setSubDistrict("ABC");
        exist.setStationType('X');
        exist.setAntenna("YZ");
        exist.setAzMaxRad(123);
        exist.setAntHeight(45);
        exist.setPwDbw(12.34);
        exist.setFeedLoss(123);
        exist.setDesenAttDb(12.34);
        exist.setIntmodAttDb(12.34);
        exist.setSfxFilter("FILTER123");

        // Add frequency information
        ExistFreq freq = new ExistFreq();
        freq.setTxFreq(123.456);
        freq.setRxFreq(123.456);
        freq.setTxChannel(9877); // 123.456 / 0.0125 + 0.5
        freq.setRxChannel(9877);
        exist.setFrequencies(Arrays.asList(freq));

        List<Exist> batchContent = Arrays.asList(exist);
        request.setBatchContent(batchContent);
        
        // Execute
        MvcResult result = mockMvc.perform(post("/api/emc/analyze")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andReturn();
        
        // Parse the response
        String responseContent = result.getResponse().getContentAsString();
        EmcAnalysisResult analysisResult = objectMapper.readValue(responseContent, EmcAnalysisResult.class);
        
        // Verify
        assertNotNull(analysisResult);
        assertEquals("TEST_EMC_UID", analysisResult.getEmcUid());
        // Note: In a real integration test, we would verify more details about the result
    }
}
