package com.emc.service;

import com.emc.model.EmcAnalysisRequest;
import com.emc.model.EmcAnalysisResult;

/**
 * Service interface for EMC analysis.
 * This is the main service that orchestrates the EMC analysis process.
 */
public interface EmcAnalysisService {
    
    /**
     * Performs EMC analysis based on the provided request.
     * 
     * @param request The EMC analysis request
     * @return The result of the EMC analysis
     */
    EmcAnalysisResult performAnalysis(EmcAnalysisRequest request);
}
