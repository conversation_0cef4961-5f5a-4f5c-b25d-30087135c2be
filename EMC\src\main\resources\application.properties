# Server configuration
server.port=8080

# MariaDB Database configuration
spring.datasource.driver-class-name=org.mariadb.jdbc.Driver
spring.datasource.url=*******************************
spring.datasource.username=root
spring.datasource.password=P@ssw0rd

# JDBC Configuration
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# Transaction Configuration
spring.transaction.default-timeout=30

# Application specific properties
emc.directory=${user.dir}/src/main/resources/emc_data
emc.batch.directory=${emc.directory}/batch
emc.audit.directory=${emc.directory}/audit
emc.summary.directory=${emc.directory}/summary
emc.interactive.directory=${emc.directory}/src/main/resources/interactive
