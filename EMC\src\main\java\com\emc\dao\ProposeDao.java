package com.emc.dao;

import com.emc.model.Propose;
import java.util.List;
import java.util.Optional;

/**
 * DAO interface for Propose entity operations.
 */
public interface ProposeDao extends BaseDao<Propose, String> {
    
    /**
     * Find proposed station by EMC UID.
     * 
     * @param emcUid The EMC UID
     * @return Optional containing the proposed station if found
     */
    Optional<Propose> findByEmcUid(String emcUid);
    
    /**
     * Find proposed stations by system category.
     * 
     * @param sysCategory The system category
     * @return List of proposed stations with the specified system category
     */
    List<Propose> findBySysCategory(char sysCategory);
    
    /**
     * Find proposed stations by system type.
     * 
     * @param sysType The system type
     * @return List of proposed stations with the specified system type
     */
    List<Propose> findBySysType(String sysType);
    
    /**
     * Find proposed stations by sub-district.
     * 
     * @param subDistrict The sub-district code
     * @return List of proposed stations in the specified sub-district
     */
    List<Propose> findBySubDistrict(String subDistrict);
    
    /**
     * Find proposed stations by station type.
     * 
     * @param stationType The station type
     * @return List of proposed stations with the specified station type
     */
    List<Propose> findByStationType(char stationType);
    
    /**
     * Find proposed stations within a grid range.
     * 
     * @param minEastGrid Minimum east grid coordinate
     * @param maxEastGrid Maximum east grid coordinate
     * @param minNorthGrid Minimum north grid coordinate
     * @param maxNorthGrid Maximum north grid coordinate
     * @return List of proposed stations within the specified range
     */
    List<Propose> findByGridRange(int minEastGrid, int maxEastGrid, 
                                 int minNorthGrid, int maxNorthGrid);
    
    /**
     * Find proposed stations by TX channel.
     * 
     * @param txChannel The TX channel
     * @return List of proposed stations with the specified TX channel
     */
    List<Propose> findByTxChannel(int txChannel);
    
    /**
     * Find proposed stations by RX channel.
     * 
     * @param rxChannel The RX channel
     * @return List of proposed stations with the specified RX channel
     */
    List<Propose> findByRxChannel(int rxChannel);
    
    /**
     * Find proposed stations by mode.
     * 
     * @param mode The mode
     * @return List of proposed stations with the specified mode
     */
    List<Propose> findByMode(char mode);
}
