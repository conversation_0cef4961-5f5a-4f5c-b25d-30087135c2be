-------------------------------------------------------------------------------
Test set: com.emc.dao.SubDistrictDaoTest
-------------------------------------------------------------------------------
Tests run: 5, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 0.327 s <<< FAILURE! - in com.emc.dao.SubDistrictDaoTest
testSave  Time elapsed: 0.016 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'update' method:
    jdbcTemplate.update(
    "INSERT INTO SUBDISTRICT_TAB (SUBDISTRICT, NOISE_CODE, DISTRICT_TYPE, LOW_VHF_CULL_REG, LOW_VHF_CULL_HD, VHF_CULL_REG, VHF_CULL_HD, UHF_CULL_REG, UHF_CULL_HD, UHF_800_CULL_REG, UHF_800_CULL_HD) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
    "XYZ",
    3,
    "M",
    10,
    20,
    30,
    40,
    50,
    60,
    70,
    80
);
    -> at com.emc.dao.impl.SubDistrictDaoImpl.save(SubDistrictDaoImpl.java:64)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.update("", null);
      -> at com.emc.dao.SubDistrictDaoTest.testSave(SubDistrictDaoTest.java:88)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.emc.dao.SubDistrictDaoTest.testSave(SubDistrictDaoTest.java:92)

