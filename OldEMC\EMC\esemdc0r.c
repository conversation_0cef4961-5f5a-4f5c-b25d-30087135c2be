/**********************************************************************/
/*                                                                    */
/*    Module Name   :  desensit_interference (esemdc0r.c)             */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  <PERSON><PERSON> <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  desensit_analysis (esemda0r.c)                 */  
/*                                                                    */
/*    Parameters    :  dfp (desensit report file pointer)             */
/*                     line_cnt                                       */
/*                     page_cnt                                       */
/*                                                                    */
/*    Called Modules:  base_to_base (esemin0x.pc)                     */
/*                                                                    */
/*    Purpose       :  Set proposed station and existing station as   */
/*                     victim in turn and perform base-to-base        */
/*                     interference analysis for the 2 cases.         */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>         /*20170706 Cyrus Add */
#include <stdio.h>
#include <string.h>
#include "../include/macros.h"
#include "../include/define.h"
#include "../include/emcext.h"
#include "../include/modeband.h"
#include "../include/propext.h"
#include "../include/existext.h"
#include "../include/desenext.h"
#include "../include/victim.h"
#include "../include/intrfr.h"
#include "../include/presuext.h"
#include "../include/refext.h"

#define DUMMY            -1

#define DESENSIT_LINES   28

void desensit_interference(FILE*, int*, int*);
int set_victim_propose();
int set_victim_exist();
void set_intr_exist(int);
void print_desensit_line(FILE *, int *,int *);
void print_desensit_head(FILE *, int *);

extern char msg[];

int base_to_base(int *,int);

void desensit_interference(dfp, line_cnt, page_cnt)
FILE    *dfp;
int     *line_cnt;
int     *page_cnt;
{
    int    intrfr_flag = FALSE;

	

/*
#ifdef DEBUG
    printf("desensit_interference\n");
#endif
*/

    if (prop->mode != TX_ONLY)
    {
	
        set_victim_propose();
		
/*
printf("VICT PROP rx tx delta_freq CULL: %lf %lf %lf %f\n", 
prop_rx_freq, exist_tx_freq, delta_freq, desensit_cull_freq);
*/
        if ((delta_freq <= desensit_cull_freq)
        &&  (delta_freq > FREQ_EPSILON))
        {
            base_to_base(&intrfr_flag, DESENSIT);
	
            if (intrfr_flag)
			{
				
		print_desensit_line(dfp, line_cnt, page_cnt);

			}
	
        }
    }

	
	
    if (exist[e_idx]->mode != TX_ONLY)
    {

        set_victim_exist();
/*
printf("VICT_EXIST rx tx delta_freq CULL: %lf %lf %lf %f\n", 
exist_rx_freq, prop_tx_freq, delta_freq, desensit_cull_freq);
*/

        if ((delta_freq <= desensit_cull_freq)
        &&  (delta_freq > FREQ_EPSILON))
        {
		
            intrfr_flag = FALSE;
            base_to_base(&intrfr_flag, DESENSIT);

            if (intrfr_flag)
			{
					
		print_desensit_line(dfp, line_cnt, page_cnt);
			}

		
        }
    }
/*
    printf("END desensit_interference\n");
*/
}


/**********************************************************************/
/*    set up proposed station as victim station, existing station as  */
/*    interfering station.                                            */
/**********************************************************************/

int set_victim_propose()
{

/*
#ifdef DEBUG
    printf("set_victim_propose\n");
#endif


    sign_delta_freq = prop_rx_freq - exist_tx_freq;
    delta_freq = abs(sign_delta_freq);
*/
    delta_freq = abs(prop_rx_freq - exist_tx_freq);
    if ((delta_freq > desensit_cull_freq)
    &&  (delta_freq <= FREQ_EPSILON))
        return(0);

    strcpy(victim_is, "PROP");
    strcpy(vict.antenna, prop->antenna);
    strcpy(vict.system_id, PROPSTN_ID);
    vict.base_no     = DUMMY;
    vict.rx_freq     = prop_rx_freq;
    vict.mob_rx_freq = prop_rx_freq;
    vict.grid[0]     = (float)prop->east_grid;
    vict.grid[1]     = (float)prop->north_grid;
/*
printf("%lf %lf %f %f\n", vict.rx_freq,vict.mob_rx_freq,vict.grid[0],vict.grid[1]);
*/
    vict.feed_loss   = prop->feed_loss;
    vict.pw_dbw      = prop->pw_dbw;
/*
printf("**ant_ht asl: %d %f\n", prop->ant_height,prop->height_asl);
*/
    vict.ant_height  = max(prop->ant_height+prop->height_asl,
			   MIN_ANT_HEIGHT);
    vict.az_max_rad   = prop->az_max_rad;
    vict.az_max_rad_r = prop->az_max_rad_r;
    vict.stn_type     = prop->station_type;
    vict.mode         = prop->mode;
    vict.band         = prop->band;
    vict.dist_index   = prop->dist_index;

    vict.sfx_filter[0] = '\0';
    if (prop->sfx_filter[0] != '\0')
        strcpy(vict.sfx_filter, prop->sfx_filter);
/*
printf("%d %f %f %d %c %c %d %s\n", vict.feed_loss,vict.pw_dbw,vict.ant_height,
       vict.az_max_rad,vict.stn_type,vict.mode,vict.dist_index,vict.sfx_filter);
*/

    intr.pw_dbw     = exist[e_idx]->pw_dbw;
    intr.tx_freq    = exist_tx_freq;
    intr.grid[0]    = (float)exist[e_idx]->east_grid;
    intr.grid[1]    = (float)exist[e_idx]->north_grid;
/*
printf("**ant_ht asl: %d %f\n", exist[e_idx]->ant_height,exist[e_idx]->height_asl);
*/
    intr.ant_height = max(exist[e_idx]->ant_height+exist[e_idx]->height_asl,
                          MIN_ANT_HEIGHT);
    intr.stn_type   = exist[e_idx]->station_type;
    intr.mode       = exist[e_idx]->mode;
    intr.dist_index = exist[e_idx]->dist_index;
	
	return(0);
}


/**********************************************************************/
/*    set up existing station as victim station, proposed station as  */
/*    interfering station.                                            */
/**********************************************************************/

int set_victim_exist()
{

/*
#ifdef DEBUG
    printf("set_victim_exist\n");
#endif
*/

/*
    sign_delta_freq = abs(exist_rx_freq - prop_tx_freq);
    delta_freq = abs(sign_delta_freq);
*/
    delta_freq = abs(exist_rx_freq - prop_tx_freq);
    if ((delta_freq > desensit_cull_freq)
    &&  (delta_freq <= FREQ_EPSILON))
        return(0);

    strcpy(victim_is, "EXIST");
    strcpy(vict.antenna, exist[e_idx]->antenna);
    sprintf(vict.system_id, "%c-%s-%s-%s",
            exist[e_idx]->sys_category, exist[e_idx]->sys_type,
            exist[e_idx]->sys_no, exist[e_idx]->sys_suffix);
    vict.base_no      = exist[e_idx]->base_no;
    vict.rx_freq      = exist_rx_freq;
    vict.mob_rx_freq  = exist_rx_freq;
    vict.grid[0]      = (float)exist[e_idx]->east_grid;
    vict.grid[1]      = (float)exist[e_idx]->north_grid;
    vict.feed_loss    = exist[e_idx]->feed_loss;
    vict.pw_dbw       = exist[e_idx]->pw_dbw;
    vict.ant_height   = max(exist[e_idx]->ant_height+exist[e_idx]->height_asl,
		 	   MIN_ANT_HEIGHT);
    vict.az_max_rad   = exist[e_idx]->az_max_rad;
    vict.az_max_rad_r = exist[e_idx]->az_max_rad_r;
    vict.stn_type     = exist[e_idx]->station_type;
    vict.mode         = exist[e_idx]->mode;
    vict.band         = exist[e_idx]->band;
    vict.dist_index   = exist[e_idx]->dist_index;

    vict.sfx_filter[0] = '\0';
    if (exist[e_idx]->sfx_filter[0] != '\0')
        strcpy(vict.sfx_filter, exist[e_idx]->sfx_filter);

    intr.pw_dbw     = prop->pw_dbw;
    intr.tx_freq    = prop_tx_freq;
    intr.grid[0]    = (float)prop->east_grid;
    intr.grid[1]    = (float)prop->north_grid;
    intr.ant_height = max(prop->ant_height+prop->height_asl, MIN_ANT_HEIGHT);
    intr.stn_type   = prop->station_type;
    intr.mode       = prop->mode;
    intr.dist_index = prop->dist_index;
	
	return(0);
}


/**********************************************************************/
/*    set up existing station as interfering station ONLY, leaving    */
/*    victim station information unchanged.                           */
/*    Currently called by 'intermod_2' and 'intermod_3' only          */
/*    Note that 'fq_list' MUST BE USED to determine 'i_idx'           */
/**********************************************************************/

void set_intr_exist(idx)
int    idx;
{
    int    i_idx;

/*
#ifdef DEBUG
    printf("set_intr_exist\n");
#endif
*/
    
    i_idx = fq_list[idx]->stn_node;
    intr.pw_dbw     = exist[i_idx]->pw_dbw;
    intr.tx_freq    = fq_list[idx]->tx_freq;
    intr.grid[0]    = (float)exist[i_idx]->east_grid;
    intr.grid[1]    = (float)exist[i_idx]->north_grid;
/*
printf("**ant_ht asl: %d %f\n", exist[i_idx]->ant_height,exist[i_idx]->height_asl);
*/
    intr.ant_height = max(exist[i_idx]->ant_height+exist[i_idx]->height_asl,
                          MIN_ANT_HEIGHT);
    intr.stn_type   = exist[i_idx]->station_type;
    intr.mode       = exist[i_idx]->mode;
    intr.dist_index = exist[i_idx]->dist_index;

    delta_freq = abs(vict.rx_freq - intr.tx_freq);
}


/**********************************************************************/
/*    print desensitisation report line                               */
/**********************************************************************/

void print_desensit_line(dfp, line_cnt, page_cnt)
FILE    *dfp;
int     *line_cnt;
int     *page_cnt;
{
   char   system_id[15];



/*
#ifdef DEBUG
    printf("print_desensit_line %d %d\n", *line_cnt, *page_cnt);
#endif
*/

   if (*line_cnt == DESENSIT_LINES)
       *line_cnt = 0;

   if (*line_cnt == 0)
   {

       print_desensit_head(dfp, page_cnt);

       (*page_cnt)++;
   }

/*
fprintf(dfp, "%d %d\n", exist[e_idx]->east_grid, exist[e_idx]->north_grid);
*/
   
   sprintf(system_id, "%c%s%s-%s", exist[e_idx]->sys_category, 
           exist[e_idx]->sys_type, exist[e_idx]->sys_no, 
           exist[e_idx]->sys_suffix);
   fprintf(dfp, 
           "%11.5lf%3s%11.5lf%4s%-13s%3s%4d%6s%5.1f%7s%3d%6s%5.2f%7s",
           exist_rx_freq, "", exist_tx_freq, "", system_id, "",
           exist[e_idx]->base_no, "", exist[e_idx]->height_asl, "", 
           exist[e_idx]->ant_height, "", exist[e_idx]->pw_dbw, "");
   fprintf(dfp, "%4.2f%7s%6.4lf%8s%6.2f%7s%-5s\n", (delta_dist/1000), "",
           delta_freq, "", attenuation, "", victim_is);

   max_att_req = max(max_att_req, attenuation);
   min_att_req = min(min_att_req, attenuation);

   desen_tot++;
   (*line_cnt)++;
}


/**********************************************************************/
/*    print desensitisation report heading                            */
/**********************************************************************/

 void print_desensit_head(dfp, page_cnt)
FILE    *dfp;
int     *page_cnt;
{

/*
#ifdef DEBUG
    printf("print_desensit_head\n");
#endif
*/

   if (*page_cnt > 1)
       fprintf(dfp, "\f");

   fprintf(dfp, "RUN DATE: %s%16s", sys_date, "");
   fprintf(dfp,
          "****************************************************************");
   fprintf(dfp, "%15sPAGE   : %-d\n", "", *page_cnt);
   fprintf(dfp, "RUN TIME: %s%16s", sys_time, "");
   fprintf(dfp,
          "*                                                              *");
   fprintf(dfp, "%15sPROGRAM: esemdc0r\n", "");
   fprintf(dfp, "USER ID : %-19s%5s", emc_uid, "");
   fprintf(dfp,
          "*               EMC ANALYSIS - DESENSITISATION                 *\n");
   fprintf(dfp, "%34s", "");
   fprintf(dfp, 
          "*           INTERFERENCE AND POWER ANALYSIS REPORT             *\n");
   fprintf(dfp, "%34s", "");
   fprintf(dfp, 
          "*                PROPOSED STATION INFORMATION                  *\n");
   fprintf(dfp, "%34s", "");
   fprintf(dfp,
          "*                                                              *\n");
   fprintf(dfp, "%34s", "");
   fprintf(dfp, "*  PROP. SYSTEM    : %c%s%s-%s%31s*\n", prop->sys_category,
           prop->sys_type, prop->sys_no, prop->sys_suffix, "");
   fprintf(dfp, "%34s", "");
   fprintf(dfp, 
          "*  RX FREQ (MHz)   :%11.5lf   TX FREQ (MHz)  :%11.5lf  *\n",
          prop_rx_freq, prop_tx_freq);
   fprintf(dfp, "%34s", "");
   fprintf(dfp, 
          "*  GRID EAST       :      %5d   GRID NORTH     :      %5d  *\n",
          prop->east_grid, prop->north_grid);
   fprintf(dfp, "%34s", "");
   fprintf(dfp, 
          "*  SUB-DISTRICT    :        %3s   ERP (DBW)      :     %6.2f  *\n",
          prop->sub_district, prop->pw_dbw);
   fprintf(dfp, "%34s", "");
   fprintf(dfp, 
          "*  ANTENNA HT (M)  :        %3d   TERRAIN HT (M) :      %5.1f  *\n",
          prop->ant_height, prop->height_asl);
   fprintf(dfp, "%34s", "");
   fprintf(dfp, 
          "*  ANTENNA TYPE    :        %3s   AZ OF MAX RAD  :        %3d  *\n",
          prop->antenna, prop->az_max_rad);
   fprintf(dfp, "%34s", "");
   fprintf(dfp, 
          "*  FEED LOSS (DBW) :        %3d   DESEN ADJ (dB) :     %6.1f  *\n",
          prop->feed_loss, prop->desen_att_db);
   fprintf(dfp, "%34s", "");
   if (prop->sfx_filter[0] != '\0')
       fprintf(dfp, 
              "*  STATION TYPE    :          %c   SFX FILTER TYPE  : %10s *\n",
              prop->station_type, prop->sfx_filter);
   else
       fprintf(dfp, "*  STATION TYPE    :          %c%32s*\n",
               prop->station_type, "");
   fprintf(dfp, "%34s", "");
   fprintf(dfp,
          "*                                                              *\n");
   fprintf(dfp, "%34s", "");
   fprintf(dfp, 
          "****************************************************************\n");
   fprintf(dfp, "\n\n\n\n");
   fprintf(dfp, " RX FREQ%7sTX FREQ%23sBASE", "", "");
   fprintf(dfp, "%5sTERRAIN%4sANTENNA%4sERP%6sDELTA DIST", "", "", "", "");
   fprintf(dfp, "%4sDELTA%5sATTENUATION%5sVICTIM\n", "", "", "");
   fprintf(dfp, "%5s(MHz)%8s(MHz)%7sSYSTEM ID.%5sNO. ", "", "", "", "");
   fprintf(dfp, "%5sHT (M)%5sHT (M)%5s(DBW)%7s(KM)", "", "", "", "");
   fprintf(dfp, "%7s(MHz)%5sREQUIRED (DB)%5sIS\n", "", "", "");
   fprintf(dfp, 
           "===========%3s===========%4s=============%3s=====", "", "", "");
   fprintf(dfp, "%4s=======%4s=======%4s=====%4s==========", "", "", "", "");
   fprintf(dfp, "%4s======%4s=============%3s======\n", "", "", "");
}
