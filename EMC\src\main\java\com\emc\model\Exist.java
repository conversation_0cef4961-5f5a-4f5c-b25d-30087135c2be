package com.emc.model;

import lombok.Data;

/**
 * Java equivalent of the EXIST struct from the original C++ code.
 * Represents an existing station for EMC analysis.
 * This matches the structure used in the original ProC programs.
 */
@Data
public class Exist {
    private String emcUid;
    private char sysCategory;
    private String sysType;
    private String sysNo;
    private String sysSuffix;
    private int baseNo;
    private int eastGrid;
    private int northGrid;
    private String subDistrict;
    private char stationType;
    private double desenAttDb;
    private double intmodAttDb;
    private String antenna;
    private int antHeight;
    private double pwDbw;
    private int azMaxRad;
    private double azMaxRadR;
    private int feedLoss;
    private String sfxFilter;
    private double heightAsl;
    private char distType;
    private int noiseCode;
    private int distIndex;
    private int stnNode; // Station node index
    private char mode; // Station mode (TX_ONLY, RX_ONLY, TX_RX)
    private String licType;
    private String licNo;
    private String cancelDate;
}
