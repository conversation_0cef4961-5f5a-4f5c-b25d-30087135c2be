package com.emc.service.impl;

import com.emc.constants.EmcConstants;
import com.emc.model.CullingFrequency;
import com.emc.model.Exist;
import com.emc.model.ExistFreq;
import com.emc.model.Propose;
import com.emc.service.DesensitizationService;
import com.emc.service.ReferenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * Implementation of the DesensitizationService interface.
 * This is a translation of the desensit_analysis function from the original C++ code.
 */
@Service
@Slf4j
public class DesensitizationServiceImpl implements DesensitizationService {

    private static final int DESENSIT_LINES = 28;
    private static final double FREQ_EPSILON = 0.0001;

    @Autowired
    private ReferenceService referenceService;

    @Value("${emc.directory}")
    private String emcDir;

    @Value("${emc.summary.directory}")
    private String summaryDir;

    // These would be injected in a real application
    private Propose prop;
    private List<Exist> exist;
    private List<ExistFreq> fqList;
    private double propTxFreq;
    private double propRxFreq;
    private int cullStnCnt;
    private int desenTot;
    private PrintWriter afp; // Audit file pointer

    @Override
    public int desensitAnalysis() {
        log.info("Performing desensitization analysis");

        try {
            // Create the desensitization analysis file
            String daFname = createDesensitizationFile();

            // Open the file for writing
            try (PrintWriter dfp = new PrintWriter(new FileWriter(daFname))) {
                // Write the header
                writeDesensitizationHeader(dfp);

                // Select stations for analysis
                int status = selectStations();
                if (status == EmcConstants.ERROR) {
                    log.error("Error selecting stations for desensitization analysis");
                    return EmcConstants.ERROR;
                }

                // Perform desensitization interference analysis
                performDesensitizationInterference(dfp);

                // Append the desensitization file to the print file
                appendToAuditFile(daFname);

                log.info("Desensitization analysis completed successfully");
                return EmcConstants.OK;
            }
        } catch (Exception e) {
            log.error("Error performing desensitization analysis", e);
            return EmcConstants.ERROR;
        }
    }

    /**
     * Creates the desensitization analysis file.
     *
     * @return The path to the desensitization file
     * @throws IOException If an I/O error occurs
     */
    private String createDesensitizationFile() throws IOException {
        String daFname = summaryDir + "/desensit_" + propTxFreq + ".txt";
        Path daPath = Paths.get(daFname);
        Files.createDirectories(daPath.getParent());
        return daFname;
    }

    /**
     * Writes the header for the desensitization analysis file.
     *
     * @param dfp The PrintWriter for the desensitization file
     */
    private void writeDesensitizationHeader(PrintWriter dfp) {
        dfp.println("** Interference and Power Analysis Report (Desensitisation)");
        dfp.println("   Proposed Channel (tx/rx): " + propTxFreq + "/" + propRxFreq);
        dfp.println("   Proposed Grid (E/N): " + prop.getEastGrid() + "/" + prop.getNorthGrid());
        dfp.println();
        dfp.println("   SYSTEM ID                EAST  NORTH  DIST   FREQ     POWER   FEED   ANT   CURVE   SFX    PROP    DIFF LOSS  FLAG");
        dfp.println("   ======================== ===== ===== ===== ========= ======= ====== ===== ======= ====== ======= ========== ====");
        dfp.println();
    }

    /**
     * Selects stations for desensitization analysis.
     * In the original C++ code, this was the select_station function.
     *
     * @return OK if successful, ERROR otherwise
     */
    private int selectStations() {
        log.info("Selecting stations for desensitization analysis");

        try {
            // Validate required data
            if (prop == null) {
                log.error("Proposed station data is null");
                return EmcConstants.ERROR;
            }

            // Get the culling frequency from the reference data
            CullingFrequency cullingFreq = referenceService.getCullingFrequency();
            if (cullingFreq == null) {
                log.error("Culling frequency data is null");
                return EmcConstants.ERROR;
            }
            float desenCull = cullingFreq.getDesenCull();

            // Reset the culling station count
            cullStnCnt = 0;

            // In a real application, this would select stations from a database
            // based on the culling frequency and other criteria
            if (fqList != null && !fqList.isEmpty()) {
                int propStnNode = prop.getStnNode();
                log.debug("Proposed station node: {}", propStnNode);

                for (ExistFreq freq : fqList) {
                    if (freq == null) {
                        continue;
                    }

                    // Skip if it's the same station as the proposed station
                    if (freq.getStnNode() == propStnNode) {
                        continue;
                    }

                    // Calculate frequency difference
                    double deltaFreq = Math.abs(freq.getTxFreq() - propRxFreq);

                    // Check if within culling frequency
                    if (deltaFreq <= desenCull && deltaFreq > FREQ_EPSILON) {
                        cullStnCnt++;
                    }
                }
            } else {
                log.debug("Frequency list is null or empty");
            }

            log.info("Selected {} stations for desensitization analysis", cullStnCnt);
            return EmcConstants.OK;
        } catch (Exception e) {
            log.error("Error selecting stations for desensitization analysis", e);
            return EmcConstants.ERROR;
        }
    }

    /**
     * Performs desensitization interference analysis.
     * In the original C++ code, this was the desensit_interference function.
     *
     * @param dfp The PrintWriter for the desensitization file
     */
    private void performDesensitizationInterference(PrintWriter dfp) {
        log.info("Performing desensitization interference analysis");

        // Reset the desensitization total
        desenTot = 0;
        int lineCnt = 0;
        int pageCnt = 1;

        // Get culling frequency for analysis
        CullingFrequency cullingFreq = referenceService.getCullingFrequency();
        float desenCull = cullingFreq.getDesenCull();

        if (fqList != null && !fqList.isEmpty()) {
            // Analyze proposed station as victim
            analyzeProposedAsVictim(dfp, desenCull, lineCnt, pageCnt);

            // Analyze existing stations as victims
            analyzeExistingAsVictims(dfp, desenCull, lineCnt, pageCnt);
        }

        log.info("Desensitization interference analysis completed with {} interferences", desenTot);
    }

    /**
     * Analyzes the proposed station as a victim of desensitization.
     *
     * @param dfp The PrintWriter for the desensitization file
     * @param desenCull The desensitization culling frequency
     * @param lineCnt Current line count
     * @param pageCnt Current page count
     */
    private void analyzeProposedAsVictim(PrintWriter dfp, float desenCull, int lineCnt, int pageCnt) {
        if (prop == null || prop.getMode() == 'T') { // TX_ONLY or null prop
            return;
        }

        int propStnNode = prop.getStnNode();
        for (ExistFreq freq : fqList) {
            if (freq == null) {
                continue;
            }

            // Skip if it's the same station
            if (freq.getStnNode() == propStnNode) {
                continue;
            }

            // Calculate frequency difference
            double deltaFreq = Math.abs(freq.getTxFreq() - propRxFreq);

            // Check if within culling frequency
            if (deltaFreq <= desenCull && deltaFreq > FREQ_EPSILON) {
                // Perform base-to-base interference calculation
                boolean interferenceFlag = calculateBaseToBaseInterference(freq, true);

                if (interferenceFlag) {
                    writeDesensitizationResult(dfp, freq, true, lineCnt, pageCnt);
                    desenTot++;
                }
            }
        }
    }

    /**
     * Analyzes existing stations as victims of desensitization.
     *
     * @param dfp The PrintWriter for the desensitization file
     * @param desenCull The desensitization culling frequency
     * @param lineCnt Current line count
     * @param pageCnt Current page count
     */
    private void analyzeExistingAsVictims(PrintWriter dfp, float desenCull, int lineCnt, int pageCnt) {
        if (prop == null || exist == null) {
            return;
        }

        int propStnNode = prop.getStnNode();
        for (ExistFreq victimFreq : fqList) {
            if (victimFreq == null) {
                continue;
            }

            // Skip if it's the proposed station
            if (victimFreq.getStnNode() == propStnNode) {
                continue;
            }

            // Get the victim station
            int victimStnNode = victimFreq.getStnNode();
            if (victimStnNode >= exist.size() || victimStnNode < 0) {
                log.warn("Invalid station node index: {}", victimStnNode);
                continue;
            }

            Exist victimStation = exist.get(victimStnNode);
            if (victimStation == null || victimStation.getStationType() == 'T') { // TX_ONLY or null
                continue;
            }

            // Calculate frequency difference with proposed station
            double deltaFreq = Math.abs(propTxFreq - victimFreq.getRxFreq());

            // Check if within culling frequency
            if (deltaFreq <= desenCull && deltaFreq > FREQ_EPSILON) {
                // Perform base-to-base interference calculation
                boolean interferenceFlag = calculateBaseToBaseInterference(victimFreq, false);

                if (interferenceFlag) {
                    writeDesensitizationResult(dfp, victimFreq, false, lineCnt, pageCnt);
                    desenTot++;
                }
            }
        }
    }

    /**
     * Calculates base-to-base interference.
     * This is a simplified version of the base_to_base function from the original C++ code.
     *
     * @param freq The frequency information
     * @param isProposedVictim Whether the proposed station is the victim
     * @return true if interference is detected, false otherwise
     */
    private boolean calculateBaseToBaseInterference(ExistFreq freq, boolean isProposedVictim) {
        // This is a simplified interference calculation
        // In a real implementation, this would include:
        // - Path loss calculations
        // - Antenna gain calculations
        // - Power calculations
        // - Desensitization threshold comparisons

        if (freq == null || prop == null || exist == null) {
            return false;
        }

        int stnNode = freq.getStnNode();
        if (stnNode >= exist.size() || stnNode < 0) {
            log.warn("Invalid station node index in interference calculation: {}", stnNode);
            return false;
        }

        // For now, we'll use a simple distance-based calculation
        Exist interferingStation = exist.get(stnNode);
        if (interferingStation == null) {
            return false;
        }

        // Calculate distance between stations (simplified)
        double distance = calculateDistance(
            prop.getEastGrid(), prop.getNorthGrid(),
            interferingStation.getEastGrid(), interferingStation.getNorthGrid()
        );

        // Simple interference threshold based on distance
        // In reality, this would be much more complex
        return distance < 50.0; // 50km threshold for demonstration
    }

    /**
     * Calculates the distance between two grid points.
     *
     * @param east1 First point east coordinate
     * @param north1 First point north coordinate
     * @param east2 Second point east coordinate
     * @param north2 Second point north coordinate
     * @return The distance in kilometers
     */
    private double calculateDistance(int east1, int north1, int east2, int north2) {
        double deltaEast = (east2 - east1) * 0.5; // Approximate conversion to km
        double deltaNorth = (north2 - north1) * 0.5; // Approximate conversion to km
        return Math.sqrt(deltaEast * deltaEast + deltaNorth * deltaNorth);
    }

    /**
     * Writes a desensitization result to the file.
     *
     * @param dfp The PrintWriter for the desensitization file
     * @param freq The frequency information
     * @param isProposedVictim Whether the proposed station is the victim
     * @param lineCnt Current line count
     * @param pageCnt Current page count
     */
    private void writeDesensitizationResult(PrintWriter dfp, ExistFreq freq, boolean isProposedVictim, int lineCnt, int pageCnt) {
        if (dfp == null || freq == null || prop == null || exist == null) {
            return;
        }

        if (lineCnt >= DESENSIT_LINES) {
            lineCnt = 0;
        }

        if (lineCnt == 0) {
            writeDesensitizationHeader(dfp);
            pageCnt++;
        }

        int stnNode = freq.getStnNode();
        if (stnNode >= exist.size() || stnNode < 0) {
            log.warn("Invalid station node index in writeDesensitizationResult: {}", stnNode);
            return;
        }

        // Get station information
        Exist station = exist.get(stnNode);
        if (station == null) {
            return;
        }

        String systemId = String.format("%c%s%s-%s",
            station.getSysCategory(),
            station.getSysType() != null ? station.getSysType() : "",
            station.getSysNo() != null ? station.getSysNo() : "",
            station.getSysSuffix() != null ? station.getSysSuffix() : "");

        // Calculate distance
        double distance = calculateDistance(
            prop.getEastGrid(), prop.getNorthGrid(),
            station.getEastGrid(), station.getNorthGrid()
        );

        // Write desensitization result
        if (isProposedVictim) {
            dfp.printf("   %-24s %05d %05d %5.1f %9.4f %7.1f %6.1f %5.1f %7.1f %6.1f %7.1f %10.1f    %c%n",
                systemId, station.getEastGrid(), station.getNorthGrid(), distance,
                freq.getTxFreq(), station.getPwDbw(), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 'Y');
        } else {
            dfp.printf("   %-24s %05d %05d %5.1f %9.4f %7.1f %6.1f %5.1f %7.1f %6.1f %7.1f %10.1f    %c%n",
                "(PROPOSED VICTIM)", prop.getEastGrid(), prop.getNorthGrid(), distance,
                propTxFreq, prop.getPwDbw(), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 'Y');
        }

        lineCnt++;
    }

    /**
     * Appends the desensitization file to the audit file.
     *
     * @param daFname The path to the desensitization file
     * @throws IOException If an I/O error occurs
     */
    private void appendToAuditFile(String daFname) throws IOException {
        // In a real application, this would append the desensitization file to the audit file
        // For now, we'll just log that it would be done
        log.info("Appending desensitization file to audit file: {}", daFname);

        // In a real application, this would be something like:
        // Files.write(Paths.get(auditFile), Files.readAllBytes(Paths.get(daFname)), StandardOpenOption.APPEND);
    }

    // Setter methods for dependencies that would be injected in a real application

    public void setProp(Propose prop) {
        this.prop = prop;
    }

    public void setExist(List<Exist> exist) {
        this.exist = exist;
    }

    public void setFqList(List<ExistFreq> fqList) {
        this.fqList = fqList;
    }

    public void setPropTxFreq(double propTxFreq) {
        this.propTxFreq = propTxFreq;
    }

    public void setPropRxFreq(double propRxFreq) {
        this.propRxFreq = propRxFreq;
    }

    public void setCullStnCnt(int cullStnCnt) {
        this.cullStnCnt = cullStnCnt;
    }

    public void setDesenTot(int desenTot) {
        this.desenTot = desenTot;
    }

    public void setAfp(PrintWriter afp) {
        this.afp = afp;
    }

    // Getter methods for values that would be accessed by other services

    public int getCullStnCnt() {
        return cullStnCnt;
    }

    public int getDesenTot() {
        return desenTot;
    }
}
