package com.emc.dao.impl;

import com.emc.dao.StationDao;
import com.emc.dao.mapper.StationRowMapper;
import com.emc.model.Station;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * JDBC implementation of StationDao.
 * This matches the original ProC STATION table queries.
 */
@Repository
@Slf4j
public class StationDaoImpl implements StationDao {
    
    private final JdbcTemplate jdbcTemplate;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final StationRowMapper rowMapper;
    
    // SQL queries matching original ProC queries
    private static final String SELECT_ALL = 
        "SELECT SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO, " +
        "EAST, NORTH, LIC_TYPE, LIC_NO, STATION_TYPE, ANT_TYPE, " +
        "HALT, AZ_MAX_RAD, PW_SIGN, PW_DBW, SUBDISTRICT, " +
        "NVL(TO_CHAR(CANCEL_DATE), '-') AS CANCEL_DATE " +
        "FROM STATION";
    
    private static final String SELECT_IN_BOUNDING_BOX = SELECT_ALL +
        " WHERE (NORTH BETWEEN ? AND ?) AND (EAST BETWEEN ? AND ?) " +
        "ORDER BY SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO";
    
    private static final String SELECT_BY_SYSTEM_ID = SELECT_ALL +
        " WHERE SYS_CATEGORY = ? AND SYS_TYPE = ? AND SYS_NO = ? AND SYS_SUFFIX = ? AND BASE_NO = ?";
    
    private static final String SELECT_BY_SYS_TYPE = SELECT_ALL +
        " WHERE SYS_TYPE = ? ORDER BY SYS_CATEGORY, SYS_NO, SYS_SUFFIX, BASE_NO";
    
    private static final String SELECT_BY_SUB_DISTRICT = SELECT_ALL +
        " WHERE SUBDISTRICT = ? ORDER BY SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO";
    
    @Autowired
    public StationDaoImpl(JdbcTemplate jdbcTemplate, NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.rowMapper = new StationRowMapper();
    }
    
    @Override
    public List<Station> findStationsInBoundingBox(int southGrid, int northGrid, int westGrid, int eastGrid) {
        log.debug("Finding stations in bounding box: S={}, N={}, W={}, E={}", 
                 southGrid, northGrid, westGrid, eastGrid);
        return jdbcTemplate.query(SELECT_IN_BOUNDING_BOX, rowMapper, 
                                 southGrid, northGrid, westGrid, eastGrid);
    }
    
    @Override
    public Optional<Station> findBySystemId(char sysCategory, String sysType, String sysNo, String sysSuffix, int baseNo) {
        log.debug("Finding station by system ID: {}{}{}-{} base {}", 
                 sysCategory, sysType, sysNo, sysSuffix, baseNo);
        try {
            Station station = jdbcTemplate.queryForObject(SELECT_BY_SYSTEM_ID, rowMapper,
                                                         sysCategory, sysType, sysNo, sysSuffix, baseNo);
            return Optional.ofNullable(station);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    @Override
    public List<Station> findBySysType(String sysType) {
        log.debug("Finding stations by system type: {}", sysType);
        return jdbcTemplate.query(SELECT_BY_SYS_TYPE, rowMapper, sysType);
    }
    
    @Override
    public List<Station> findBySubDistrict(String subDistrict) {
        log.debug("Finding stations by sub-district: {}", subDistrict);
        return jdbcTemplate.query(SELECT_BY_SUB_DISTRICT, rowMapper, subDistrict);
    }
    
    // BaseDao implementation methods
    @Override
    public Station save(Station entity) {
        throw new UnsupportedOperationException("Station save not implemented");
    }
    
    @Override
    public Station update(Station entity) {
        throw new UnsupportedOperationException("Station update not implemented");
    }
    
    @Override
    public Optional<Station> findById(String id) {
        throw new UnsupportedOperationException("Station findById not implemented - use findBySystemId instead");
    }
    
    @Override
    public List<Station> findAll() {
        log.debug("Finding all stations");
        return jdbcTemplate.query(SELECT_ALL + " ORDER BY SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO", rowMapper);
    }
    
    @Override
    public boolean deleteById(String id) {
        throw new UnsupportedOperationException("Station delete not implemented");
    }
    
    @Override
    public boolean existsById(String id) {
        throw new UnsupportedOperationException("Station existsById not implemented");
    }
    
    @Override
    public long count() {
        Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM STATION", Integer.class);
        return count != null ? count : 0;
    }
}
