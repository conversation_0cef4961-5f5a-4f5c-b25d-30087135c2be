package com.emc.dao;

import com.emc.model.CullingFrequency;

/**
 * DAO interface for CullingFrequency entity operations.
 */
public interface CullingFrequencyDao extends BaseDao<CullingFrequency, Long> {
    
    /**
     * Get the culling frequency configuration.
     * Since there's typically only one configuration record,
     * this method returns the current active configuration.
     * 
     * @return The culling frequency configuration
     */
    CullingFrequency getConfiguration();
    
    /**
     * Update the culling frequency configuration.
     * 
     * @param cullingFrequency The new configuration
     * @return The updated configuration
     */
    CullingFrequency updateConfiguration(CullingFrequency cullingFrequency);
}
