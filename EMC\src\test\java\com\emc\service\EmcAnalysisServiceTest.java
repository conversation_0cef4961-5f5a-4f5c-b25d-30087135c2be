package com.emc.service;

import com.emc.constants.EmcConstants;
import com.emc.model.EmcAnalysisRequest;
import com.emc.model.EmcAnalysisResult;
import com.emc.service.impl.EmcAnalysisServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class EmcAnalysisServiceTest {

    @Mock
    private ReferenceService referenceService;

    @Mock
    private TerrainService terrainService;

    @Mock
    private DesensitizationService desensitizationService;

    @Mock
    private IntermodulationService intermodulationService;

    @Mock
    private CochannelService cochannelService;

    @InjectMocks
    private EmcAnalysisServiceImpl emcAnalysisService;

    private EmcAnalysisRequest request;
    private String tempDir;

    @BeforeEach
    void setUp() throws IOException {
        // Create temporary directories for testing
        tempDir = System.getProperty("java.io.tmpdir") + "/emc_test";
        Path tempPath = Paths.get(tempDir);
        Files.createDirectories(tempPath);

        // Set up directories in the service
        ReflectionTestUtils.setField(emcAnalysisService, "emcDir", tempDir);
        ReflectionTestUtils.setField(emcAnalysisService, "batchDir", tempDir + "/batch");
        ReflectionTestUtils.setField(emcAnalysisService, "auditDir", tempDir + "/audit");
        ReflectionTestUtils.setField(emcAnalysisService, "summaryDir", tempDir + "/summary");
        ReflectionTestUtils.setField(emcAnalysisService, "interactiveDir", tempDir + "/interactive");

        // Create the directories
        Files.createDirectories(Paths.get(tempDir + "/batch"));
        Files.createDirectories(Paths.get(tempDir + "/audit"));
        Files.createDirectories(Paths.get(tempDir + "/summary"));
        Files.createDirectories(Paths.get(tempDir + "/interactive"));

        // Set up a sample request
        request = new EmcAnalysisRequest();
        request.setPrinterId("printer1");
        request.setPrintFile("test_print");
        request.setEmcUid("TEST_EMC_UID");
        request.setInteractive(false);

        // Sample batch content with one station
        String batchContent =
            "TEST_EMC_UID       12345678901234567890" +
            "12345" + // East grid
            "67890" + // North grid
            "ABC" +   // Sub-district
            "X" +     // Station type
            "YZ" +    // Antenna
            "123" +   // Azimuth
            "045" +   // Antenna height
            "+1234" + // Power in dBW
            "123" +   // Feed loss
            "+1234" + // Desensitization attenuation
            "+1234" + // Intermodulation attenuation
            "FILTER123" + // SFX filter
            "12345678901" + // TX frequency
            "12345678901";  // RX frequency

        request.setBatchContent(batchContent);

        // Mock service responses
        when(terrainService.getLocalHeight(anyFloat(), anyFloat())).thenReturn(100.0f);
        when(desensitizationService.desensitAnalysis()).thenReturn(EmcConstants.OK);
        when(cochannelService.cochaninf()).thenReturn(EmcConstants.OK);
        when(referenceService.getDistrict(anyString(), any(), anyInt(), anyInt())).thenReturn(EmcConstants.OK);
    }

    @Test
    void testPerformAnalysis_Success() {
        // Execute
        EmcAnalysisResult result = emcAnalysisService.performAnalysis(request);

        // Verify
        assertNotNull(result);
        assertEquals("COMPLETED", result.getStatus());

        // Verify service calls
        verify(referenceService, times(1)).loadReference();
        verify(terrainService, atLeastOnce()).getLocalHeight(anyFloat(), anyFloat());
        verify(desensitizationService, atLeastOnce()).desensitAnalysis();
        verify(intermodulationService, atLeastOnce()).intermod2();
        verify(intermodulationService, atLeastOnce()).intermod3();
        verify(cochannelService, atLeastOnce()).cochaninf();
    }



    @Test
    void testPerformAnalysis_DesensitizationFailure() {
        // Setup
        when(desensitizationService.desensitAnalysis()).thenReturn(EmcConstants.ERROR);

        // Execute
        EmcAnalysisResult result = emcAnalysisService.performAnalysis(request);

        // Verify
        assertNotNull(result);
        assertEquals("COMPLETED", result.getStatus());

        // Verify service calls
        verify(referenceService, times(1)).loadReference();
        verify(terrainService, atLeastOnce()).getLocalHeight(anyFloat(), anyFloat());
        verify(desensitizationService, atLeastOnce()).desensitAnalysis();
        // Intermodulation should not be called if desensitization fails
        verify(intermodulationService, never()).intermod2();
        verify(intermodulationService, never()).intermod3();
        verify(cochannelService, atLeastOnce()).cochaninf();
    }

    @Test
    void testPerformAnalysis_InteractiveWithDefaultEmcUid() {
        // Setup
        EmcAnalysisRequest interactiveRequest = new EmcAnalysisRequest();
        interactiveRequest.setPrinterId("printer1");
        interactiveRequest.setPrintFile("test_print");
        interactiveRequest.setInteractive(true);
        // Deliberately not setting emcUid to test default value

        // Sample batch content with one station
        String batchContent =
            "ELSO_WEB.ANALYSIS   12345678901234567890" +
            "12345" + // East grid
            "67890" + // North grid
            "ABC" +   // Sub-district
            "X" +     // Station type
            "YZ" +    // Antenna
            "123" +   // Azimuth
            "045" +   // Antenna height
            "+1234" + // Power in dBW
            "123" +   // Feed loss
            "+1234" + // Desensitization attenuation
            "+1234" + // Intermodulation attenuation
            "FILTER123" + // SFX filter
            "12345678901" + // TX frequency
            "12345678901";  // RX frequency

        interactiveRequest.setBatchContent(batchContent);

        // Execute
        EmcAnalysisResult result = emcAnalysisService.performAnalysis(interactiveRequest);

        // Verify
        assertNotNull(result);
        assertEquals("COMPLETED", result.getStatus());

        // Verify service calls
        verify(referenceService, times(1)).loadReference();
        verify(terrainService, atLeastOnce()).getLocalHeight(anyFloat(), anyFloat());
        verify(desensitizationService, atLeastOnce()).desensitAnalysis();
        verify(intermodulationService, atLeastOnce()).intermod2();
        verify(intermodulationService, atLeastOnce()).intermod3();
        verify(cochannelService, atLeastOnce()).cochaninf();
    }
}
