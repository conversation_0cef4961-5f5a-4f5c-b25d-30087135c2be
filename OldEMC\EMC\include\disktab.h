/*
 * 	@(#)disktab.h	6.1	(ULTRIX)	11/19/91
 */

/**************************************************************************
 *
 *	Modification History
 *
 * 13-Sep-89	<PERSON>
 *	Added "struct  disktab *creatediskbyname();"
 */

/*
 * Disk description table, see disktab(5)
 */
#define	DISKTAB		"/etc/disktab"

struct	disktab {
	char	*d_name;		/* drive name */
	char	*d_type;		/* drive type */
	int	d_secsize;		/* sector size in bytes */
	int	d_ntracks;		/* # tracks/cylinder */
	int	d_nsectors;		/* # sectors/track */
	int	d_ncylinders;		/* # cylinders */
	int	d_rpm;			/* revolutions/minute */
	struct	partition {
		int	p_size;		/* #sectors in partition */
		short	p_bsize;	/* block size in bytes */
		short	p_fsize;	/* frag size in bytes */
	} d_partitions[8];
};

struct	disktab *getdiskbyname();
struct  disktab *creatediskbyname();
