#ifndef  DUMMY
#define  DUMMY        -1
#endif

/************************************************************/
/* The following north grids are searched when current grid */
/* cannot be found in TERRAIN table                         */
/************************************************************/
int inorth[] =
{  465, 480, 495, 511, 526, 541, 556, 571, 587, 602,
   617, 632, 648, 663, 678, 693, 709, 724, 739, 754,
   770, 785, 800, 815, 831, 846, 861, 876, 892, 907,
   922, 937, 952, 968, 983, 998,1013,1029,1044,1059,
  1074,1090,1105,1120,1135,1151,1166,1181,1196,1212,
  1227,1242,1257,1273,1288,1303,1318,1333,1349,1364,
  1379,1394,1410,1425,1440,1455,1471,1486,1501,1516,
  1532,1547,1562,1577,1593,1608,1623,1638,1654,1669,
  1684,1699,1714,1730,1745,1760,1775,1791,1806,1821,
  1836,1852,1867,1882,1897,1913,1928,1943,1958,1974,
  1989,2004,2019,2035,2050,2065,2080,2095,2111,2126,
  2141,2156,2172,2187,2202,2217,2233,2248,2263,2278,
  2294,2309,2324,2339,2355,2370,2385,2400,2416,2431,
  2446,2461,2476,2492,2507,2522,2537,2553,2568,2583,
  2598,2614,2629,2644,2659,2675,2690,2705,2720,2736,
  2751,2766,2781,2797,2812,2827,2842,2857,2873,2888,
  2903,2918,2934,2949,2964,2979,2995,3010,3025,3040,
  3056,3071,3086,3101,3117,3132,3147,3162,3178,3193,
  3208,3223,3238,3254,3269,3284,3299,3315,3330,3345,
  3360,3376,3391,3406,3421,3437,3452,3467,3482,3498,
  3513,3528,3543,3559,3574,3589,3604,3619,3635,3650,
  3665,3680,3696,3711,3726,3741,3757,3772,3787,3802,
  3818,3833,3848,3863,3879,3894,3909,3924,3940,3955,
  3970,3985,4000,4016,4031,4046,4061,4077,4092,4107,
  4122,4138,4153,4168,4183,4199,4214,4229,4244,4260,
  4275,4290,4305,4321,4336,4351,4366,4381,4397,4412,
  4427,4442,4458,4473,4488,4503,4519,4534,4549,4564,
  4580,4595,4610,4625,4641,4656,4671,4686,4702,4717,
  4732,4747,4762,4778,4793,4808,4823,4839,4854,4869, DUMMY };
