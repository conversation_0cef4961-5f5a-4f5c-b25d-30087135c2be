/**********************************************************************/
/*                                                                    */
/*    Module Name   :  main (esemcn0f.c)                              */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  <PERSON><PERSON> <PERSON>                                      */
/*                                                                    */
/*    Callers       :                                                 */  
/*                                                                    */
/*    Parameters    :                                                 */
/*                                                                    */
/*    Called Modules:  esemns0f.pc                                    */
/*                     esemcs0f.pc                                    */
/*                     freqsel_main_menu (esemfp0f.c)                 */
/*                     esembe0f.pc                                    */
/*                     esemer0f.c                                     */
/*                     emc_tables_maint (esemet0f.c)                  */
/*                     esemep0f.c                                     */
/*                                                                    */
/*    Purpose       :  This is the main menu for all EMC functions    */
/*                     which include :                                */
/*                         near-site analysis (esemns0f.pc),          */
/*                         co-site analysis (esemcs0f.pc),            */
/*                         frequency pre-selection analysis           */
/*                         (freqsel_main_menu of esemfp0f.c),         */
/*                         EMC batch entry (esembe0f.pc),             */
/*                         EMC analysis report reprint (esemer0f.c),  */
/*                         EMC tables maintenance menu                */
/*                         (emc_tables_maint of esemet0f.c),          */
/*                         EMC tables report menu                     */
/*                         (esemep0f.c)                               */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <curses.h>
#include <time.h>
#include "../include/emc.h"
#include "../include/global.h"
#include <unistd.h>


#ifndef  DUMMY
#define  DUMMY        -1
#endif

#define  TRAIL          0
#define  LEADING        1

#define  LOGIN_OK       0
#define  NOT_FOUND      1403

#define  ORDINARY_USER       'O'
#define  MAINTENANCE_USER    'M'
#define  SUPERVISOR          'S'

#define  DEFAULT_PRINTER     "printer1"

int get_sys_date_time(char *,char *,char *,char *);
int user_login    (char *,char *,char *);
void disp_err(char *);	
int disp_heading (char *,char *,char *);							/*20170707 Cyrus Add */
void disp_space(int,int,int);										/*20170707 Cyrus Add */
int read_str(char *, char *, int, int, int, int *, int);			/*20170707 Cyrus Add */
void str_toupper(char *);											/*20170707 Cyrus Add */
int emc_login(char *, char *, char *, char *);                      /*20170707 Cyrus Add */
int strip_blank   (char *,char *);									/*20170707 Cyrus Add */
void freqsel_main_menu( char *, char *);							/*20170707 Cyrus Add */
int emc_tables_maint(char *,char);									/*20170707 Cyrus Add */
int user_logout();													/*20170707 Cyrus Add */

char       *src_dir;

char       *getenv();


int main(argc, argv)
int    argc;
char   **argv;
{
    char    err_msg[80];
    char    cmdline[150];
    char    option[3], option_hi;
    char    passwd[20];
    char    emc_uid[21];
    char    emc_pwd[21];
    char    user_type;
    int     i = 0;
    int     cnt = 1;
    int     field_no;
    int     status;
    int     term_code;
    int     y_pos[2], x_pos[2];

    struct tm  *tt;

	option_hi=0; 													/*20170707 Cyrus [uninitialized in this function] */
    if (argc != 3)
    {
        printf("Usage: esemcn0f user_id password\n");
        exit(1);
    }

    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);
    emc_dir = getenv("EMC");
    src_dir = getenv("SRC");

    initscr();
    raw();

/* added by Chen Yung */    
    noecho();
/* -- */    
    
    strcpy(user_id, argv[1]);
    strcpy(passwd, argv[2]);
    if (user_login(user_id, passwd, err_msg) != LOGIN_OK)
    {
        disp_err(err_msg);
        goto force_exit;
    }

    disp_heading("emcn0f_01", "LOGIN  SCREEN", sys_date);
    mvaddstr(8, 14, "USER NAME  : ");
    getyx(stdscr, y_pos[i], x_pos[i++]);
    mvaddstr(10, 14, "PASSWORD   : ");
    refresh();
    getyx(stdscr, y_pos[i], x_pos[i]);
    field_no = i;

    status = !(LOGIN_OK);

    do
    {
        attrset(A_REVERSE);
        disp_space(y_pos[0], x_pos[0], 20);
        disp_space(y_pos[1], x_pos[1], 20);
/*
        disp_space(y_pos[0], x_pos[0], 9);
        disp_space(y_pos[1], x_pos[1], 9);
*/

        move(y_pos[0], x_pos[0]);
        refresh();
	    nodelay(stdscr, TRUE);
/*
	    read_str(user_id, "", 19, 0, 10, &term_code, A_REVERSE);
*/
	    read_str(emc_uid, "", 20, 0, 10, &term_code, A_REVERSE);
        str_toupper(emc_uid);
	    nodelay(stdscr, FALSE);

	    if (term_code == TIME_OUT)
        {
            disp_err("Login time out");
            goto force_exit;
        }

        attroff(A_REVERSE);

	    if (cnt > 0)	/* just failed last time */
	    {
	        disp_space(23, 0, 80);
	        refresh();
	    }

/* commented out by Chen Yung
	    noecho();
*/
        move(y_pos[1], x_pos[1]);
	    nodelay(stdscr, TRUE);
/*
	    read_str(passwd, "", 19, TRM_NOECHO, 10, &term_code, A_REVERSE);
*/
	    read_str(emc_pwd, "", 20, TRM_NOECHO, 10, &term_code, A_REVERSE);
        str_toupper(emc_pwd);
	    nodelay(stdscr, FALSE);
/* commented out by Chen Yung
	    echo();
*/

	    if (term_code == TIME_OUT)
        {
            disp_err("Login time out");
            goto force_exit;
        }

        status = emc_login(emc_uid, emc_pwd, &user_type, err_msg);
        if (status != LOGIN_OK)
		{
            if (status != NOT_FOUND)
            {
                disp_err(err_msg);
                goto force_exit;
            }
            else
            {
                if (cnt == 3)
                {
                    disp_err("Login fails for 3 times");
		            goto force_exit;
                }
                else
	            {
                    strip_blank(err_msg, TRAIL);
                    disp_err(err_msg);
	            }

                cnt++;
            }
		}
    }
    while (status != LOGIN_OK);

    switch (user_type)
    {
        case ORDINARY_USER:
            option_hi = '6';
            break;

        case MAINTENANCE_USER:
        case SUPERVISOR:
            option_hi = '8';
            break;
    }

    mvaddstr(12, 14, "PRINTER ID : ");
    refresh();
    read_str(printer_id, DEFAULT_PRINTER, 15, 0, 0, &term_code, A_REVERSE);

    do
    {
        raw();
        strcpy(option, "-");
        attrset(A_NORMAL);
        clear();
        disp_heading("emcn0f_02", "FREQUENCY  ASSIGNMENT  MENU", sys_date);
        mvaddstr(6,  15, "0.   Exit");
        mvaddstr(8,  15, "1.   NEAR SITE ANALYSIS");
        mvaddstr(9,  15, "2.   CO-SITE ANALYSIS FOR HILL-TOP SITES");
        mvaddstr(10, 15, "3.   FREQUENCY PRE-SELECTION ANALYSIS");
        mvaddstr(11, 15, "4.   EMC BATCH ENTRY");
        mvaddstr(12, 15, "5.   INTERACTIVE EMC ANALYSIS");
        mvaddstr(13, 15, "6.   EMC ANALYSIS REPORTS REPRINTING");
        switch (user_type)
        {
            case ORDINARY_USER:
                break;

            case MAINTENANCE_USER:
            case SUPERVISOR:
                mvaddstr(14, 15, "7.   EMC TABLES MAINTENANCE");
                mvaddstr(15, 15, "8.   EMC TABLES PRINTING");
                break;
        }

        mvaddstr(18, 15, "YOUR SELECTION : ");
        refresh();
        getyx(stdscr, y_pos[0], x_pos[0]);
        attrset(A_REVERSE);
        disp_space(y_pos[0], x_pos[0], 1);
        move(y_pos[0], x_pos[0]);
        refresh();

	    read_str(option, "", 2, 0, 10, &term_code, A_REVERSE);

        while ((option[0] < '0') || (option[0] > option_hi))
        {
            disp_err("Invalid option");
            attrset(A_REVERSE);
            move(y_pos[0], x_pos[0]);
            refresh();
	        read_str(option, "", 2, 0, 10, &term_code, A_REVERSE);
        }

        switch (option[0])
        {
            case '0' : 
                break;
            case '1' : 
                sprintf(cmdline, "%s/esemns0f %s %s %s -P %s", src_dir, user_id,
                        passwd, emc_uid, printer_id);
                system(cmdline); 
                break;
            case '2' : 
                sprintf(cmdline, "%s/esemcs0f %s %s %s -P %s", src_dir, user_id,
                        passwd, emc_uid, printer_id);
                system(cmdline); 
                break;
            case '3' : 
                freqsel_main_menu(emc_uid, passwd);
                break;
            case '4' : 
                sprintf(cmdline, "%s/esembe0f %s %s %s", src_dir, user_id,
                        passwd, emc_uid);
                system(cmdline); 
                break;
            case '5' : 
                sprintf(cmdline, "%s/esembe0f %s %s %s -i -P %s",
                        src_dir, user_id, passwd, emc_uid, printer_id);
                system(cmdline); 
                break;
            case '6' : 
                sprintf(cmdline, "%s/esemer0f -P %s", src_dir, printer_id);
                system(cmdline); 
                break;
            case '7' : 
                emc_tables_maint(passwd, user_type);
                break;
            case '8' : 
                sprintf(cmdline, "%s/esemep0f %s %s -P %s", src_dir, user_id,
                        passwd, printer_id);
                system(cmdline); 
                break;
        }

    } while (option[0] != '0');

    endwin();
    if (user_logout() == OK)
    {
        disp_err(err_msg);
        goto force_exit;
    }

    exit(0);


force_exit:
    sleep(1); 
    clear();
    refresh();
    endwin();
    exit(1);
}
