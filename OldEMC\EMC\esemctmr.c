
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,1,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[12];
};
static const struct sqlcxp sqlfpn =
{
    11,
    "esemctmr.pc"
};


static unsigned int sqlctx = 149947;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[3];
   unsigned long  sqhstl[3];
            int   sqhsts[3];
            short *sqindv[3];
            int   sqinds[3];
   unsigned long  sqharm[3];
   unsigned long  *sqharc[3];
   unsigned short  sqadto[3];
   unsigned short  sqtdso[3];
} sqlstm = {13,3};

/* SQLLIB Prototypes */
extern void sqlcxt (void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlcx2t(void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlbuft(void **, char *);
extern void sqlgs2t(void **, char *);
extern void sqlorat(void **, unsigned int *, void *);

/* Forms Interface */
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern void sqliem(unsigned char *, signed int *);

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4130,1,0,0,
5,0,0,1,0,0,17,184,0,0,1,1,0,1,0,1,97,0,0,
24,0,0,1,0,0,45,187,0,0,0,0,0,1,0,
39,0,0,1,0,0,13,189,0,0,3,0,0,1,0,2,4,0,0,2,4,0,0,2,9,0,0,
66,0,0,1,0,0,15,193,0,0,0,0,0,1,0,
81,0,0,1,0,0,13,228,0,0,3,0,0,1,0,2,4,0,0,2,4,0,0,2,9,0,0,
108,0,0,1,0,0,13,286,0,0,3,0,0,1,0,2,4,0,0,2,4,0,0,2,9,0,0,
135,0,0,1,0,0,15,297,0,0,0,0,0,1,0,
};


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  main (esemctmr.pc)                             */
/*    Date Written  :  July, 1993                                     */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  main (esemep0f.c)                              */  
/*                                                                    */
/*    Parameters    :  error message                                  */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Prints traffic loading readings for all MRS    */
/*                     channels in the order of measurement date or   */
/*                     frequency depending on whether user supplies   */
/*                     '-d' or '-f' flag tp the program.              */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Jul-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>

//20180627 Cyrus add
#include <time.h>
#include <unistd.h>
#include <curses.h>
#include "../include/define.h"
#include "../include/emc.h"







#ifndef ERROR
#define ERROR    1
#endif

#ifndef OK
#define OK       0
#endif

#ifndef LOGIN_OK
#define LOGIN_OK    0
#endif

#ifndef NOT_FOUND
#define NOT_FOUND   1403
#endif

#define  MAX_RPT_LINES       57
#define  HEADLINES           4
#define  COLUMN_HEADLINES    6

/* report sorting order */
#define  BY_FREQ        0
#define  BY_DATE        1

/*   strip blank mode   */
#define  TRAIL          0
#define  LEADING        1


int get_sys_date_time(char *,char *,char *,char *);	                /*20180627 Cyrus Add */
int user_login    (char *,char *,char *);                           /*20180627 Cyrus Add */
int strip_blank   (char *,char *);									/*20180627 Cyrus Add */
void print_rpt_head (FILE *, int, int);                               /*20180627 Cyrus Add */
void print_by_date_rpt_col (FILE *);                                 /*20180627 Cyrus Add */


char    user_id[20];        /* user login name       */
char	sys_date[9];        /* system date           */
char	sys_time[9];        /* system time           */
char	yymmdd[7];          /* system date in yymmdd */
char	hhmmss[7];          /* system time in hhmmss */
char	printer_id[15];     /* destinated printer id */
char    err_msg[133];

int     items_per_line;

char       *getenv();
struct tm  *get_date_time();


/* EXEC SQL BEGIN DECLARE SECTION; */ 


    double   o_freq;
    float    o_traffic;
    /* VARCHAR  o_date[9]; */ 
struct { unsigned short len; unsigned char arr[9]; } o_date;

    char     select1[256];

/* EXEC SQL END DECLARE SECTION; */ 


/* EXEC SQL INCLUDE SQLCA;
 */ 
/*
 * $Header: sqlca.h 24-apr-2003.12:50:58 mkandarp Exp $ sqlca.h 
 */

/* Copyright (c) 1985, 2003, Oracle Corporation.  All rights reserved.  */
 
/*
NAME
  SQLCA : SQL Communications Area.
FUNCTION
  Contains no code. Oracle fills in the SQLCA with status info
  during the execution of a SQL stmt.
NOTES
  **************************************************************
  ***                                                        ***
  *** This file is SOSD.  Porters must change the data types ***
  *** appropriately on their platform.  See notes/pcport.doc ***
  *** for more information.                                  ***
  ***                                                        ***
  **************************************************************

  If the symbol SQLCA_STORAGE_CLASS is defined, then the SQLCA
  will be defined to have this storage class. For example:
 
    #define SQLCA_STORAGE_CLASS extern
 
  will define the SQLCA as an extern.
 
  If the symbol SQLCA_INIT is defined, then the SQLCA will be
  statically initialized. Although this is not necessary in order
  to use the SQLCA, it is a good pgming practice not to have
  unitialized variables. However, some C compilers/OS's don't
  allow automatic variables to be init'd in this manner. Therefore,
  if you are INCLUDE'ing the SQLCA in a place where it would be
  an automatic AND your C compiler/OS doesn't allow this style
  of initialization, then SQLCA_INIT should be left undefined --
  all others can define SQLCA_INIT if they wish.

  If the symbol SQLCA_NONE is defined, then the SQLCA variable will
  not be defined at all.  The symbol SQLCA_NONE should not be defined
  in source modules that have embedded SQL.  However, source modules
  that have no embedded SQL, but need to manipulate a sqlca struct
  passed in as a parameter, can set the SQLCA_NONE symbol to avoid
  creation of an extraneous sqlca variable.
 
MODIFIED
    lvbcheng   07/31/98 -  long to int
    jbasu      12/12/94 -  Bug 217878: note this is an SOSD file
    losborne   08/11/92 -  No sqlca var if SQLCA_NONE macro set 
  Clare      12/06/84 - Ch SQLCA to not be an extern.
  Clare      10/21/85 - Add initialization.
  Bradbury   01/05/86 - Only initialize when SQLCA_INIT set
  Clare      06/12/86 - Add SQLCA_STORAGE_CLASS option.
*/
 
#ifndef SQLCA
#define SQLCA 1
 
struct   sqlca
         {
         /* ub1 */ char    sqlcaid[8];
         /* b4  */ int     sqlabc;
         /* b4  */ int     sqlcode;
         struct
           {
           /* ub2 */ unsigned short sqlerrml;
           /* ub1 */ char           sqlerrmc[70];
           } sqlerrm;
         /* ub1 */ char    sqlerrp[8];
         /* b4  */ int     sqlerrd[6];
         /* ub1 */ char    sqlwarn[8];
         /* ub1 */ char    sqlext[8];
         };

#ifndef SQLCA_NONE 
#ifdef   SQLCA_STORAGE_CLASS
SQLCA_STORAGE_CLASS struct sqlca sqlca
#else
         struct sqlca sqlca
#endif
 
#ifdef  SQLCA_INIT
         = {
         {'S', 'Q', 'L', 'C', 'A', ' ', ' ', ' '},
         sizeof(struct sqlca),
         0,
         { 0, {0}},
         {'N', 'O', 'T', ' ', 'S', 'E', 'T', ' '},
         {0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0}
         }
#endif
         ;
#endif
 
#endif
 
/* end SQLCA */



int main(argc, argv)
int    argc;
char   **argv;
{
    char   *rpt_dir;
    char   rpt_line[133];
    char   sub_rpt_line[45];
    char   fname[120];
    char   order_by_str[35];
    char   cmdline[100];
    char   prev_date[3];
    FILE   *rfp;
    int    sort_order;
    int    line_cnt = 0;
    int    page_cnt = 1;
    int    item_cnt = 0;


    /* EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn; */ 


    if (argc != 6)
    {
        printf("Fatal error: invalid no. of arguments passed to %s\n", argv[0]);
        printf("Usage: esemctmr username password [-f or -d] -P printer_id\n");
        exit(1);
    }

    if (strcmp(argv[3], "-f") && strcmp(argv[3], "-d"))
    {
        printf("Fatal error: invalid no. of arguments passed to %s\n", argv[0]);
        printf("Usage: esemctmr username password [-f or -d] -P printer_id\n");
        exit(1);
    }

    if (strcmp(argv[4], "-P"))
    {
        printf("Fatal error: invalid no. of arguments passed to %s\n", argv[0]);
        printf("Usage: esemctmr username password [-f or -d] -P printer_id\n");
        exit(1);
    }

    strcpy(user_id, argv[1]);
    rpt_dir = getenv("REPORT_DIR");
    sprintf(fname, "%s/%s.%s", rpt_dir, argv[0], user_id);
    if((rfp = fopen(fname, "w")) == (FILE *) NULL)
    {
        printf("Fail to open %s, press any key to exit", fname);
        getchar();
        goto force_exit;
    }

    if (user_login(user_id, argv[2], err_msg) != LOGIN_OK)
    {
        strip_blank(err_msg, TRAIL);
        strcat(err_msg, "  ");
        strcat(err_msg, argv[1]);
        strcat(err_msg, argv[2]);
        fprintf(rfp, "%s\n", err_msg);
        fclose(rfp);
        goto force_exit;
    }

    if (argv[3][1] == 'f')
    {
        strcpy(order_by_str, "TX_FREQ");
        sort_order = BY_FREQ;
        items_per_line = 3;
    }
    else
    {
        strcpy(order_by_str, "LAST_UPDATE, TX_FREQ");
        sort_order = BY_DATE;
        items_per_line = 5;
    }

    strcpy(printer_id, argv[5]);

    sprintf(select1, 
    "SELECT TX_FREQ, LOAD_PER_MOBILE, TO_CHAR(LAST_UPDATE, 'DD/MM/YY') FROM CHANNEL_TRAFFIC ORDER BY %s", order_by_str);

    /* EXEC SQL PREPARE S1 FROM :select1; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 1;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )5;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)select1;
    sqlstm.sqhstl[0] = (unsigned long )256;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


    /* EXEC SQL DECLARE C1 CURSOR FOR S1; */ 


    /* EXEC SQL OPEN C1; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 1;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )24;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqcmod = (unsigned int )0;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    /* EXEC SQL FETCH C1 INTO :o_freq, :o_traffic, :o_date; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 3;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )39;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqfoff = (         int )0;
    sqlstm.sqfmod = (unsigned int )2;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_freq;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(double);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_traffic;
    sqlstm.sqhstl[1] = (unsigned long )sizeof(float);
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&o_date;
    sqlstm.sqhstl[2] = (unsigned long )11;
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


    if (sqlca.sqlcode == NOT_FOUND)
    {
        fprintf(rfp, "Fatal error: no record found in CHANNEL_TRAFFIC");
        /* EXEC SQL CLOSE C1; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 3;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )66;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


        fclose(rfp);
        goto force_exit;
    }

    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);

    print_rpt_head(rfp, sort_order, page_cnt); 
    page_cnt++;
    line_cnt = HEADLINES + COLUMN_HEADLINES;
    rpt_line[0] = prev_date[0] = '\0';

    if (sort_order == BY_FREQ)
        for (; ;)
        {
            o_date.arr[o_date.len] = '\0';

            sprintf(sub_rpt_line, "%11.5lf   %6.4f    %s       ",
                    o_freq, o_traffic, o_date.arr);
            strcat(rpt_line, sub_rpt_line);

            item_cnt = (item_cnt + 1) % items_per_line;
            if (item_cnt == 0)
            {
                if (line_cnt == MAX_RPT_LINES)
                {
                    print_rpt_head(rfp, sort_order, page_cnt); 
                    page_cnt++;
                    line_cnt = HEADLINES + COLUMN_HEADLINES;
                }
                fprintf(rfp, "%10s%s\n", "", rpt_line);
                rpt_line[0] = '\0';
                line_cnt++;
            }

            /* EXEC SQL FETCH C1 INTO :o_freq, :o_traffic, :o_date; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 3;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )81;
            sqlstm.selerr = (unsigned short)1;
            sqlstm.sqlpfmem = (unsigned int  )0;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlstm.sqfoff = (         int )0;
            sqlstm.sqfmod = (unsigned int )2;
            sqlstm.sqhstv[0] = (unsigned char  *)&o_freq;
            sqlstm.sqhstl[0] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[0] = (         int  )0;
            sqlstm.sqindv[0] = (         short *)0;
            sqlstm.sqinds[0] = (         int  )0;
            sqlstm.sqharm[0] = (unsigned long )0;
            sqlstm.sqadto[0] = (unsigned short )0;
            sqlstm.sqtdso[0] = (unsigned short )0;
            sqlstm.sqhstv[1] = (unsigned char  *)&o_traffic;
            sqlstm.sqhstl[1] = (unsigned long )sizeof(float);
            sqlstm.sqhsts[1] = (         int  )0;
            sqlstm.sqindv[1] = (         short *)0;
            sqlstm.sqinds[1] = (         int  )0;
            sqlstm.sqharm[1] = (unsigned long )0;
            sqlstm.sqadto[1] = (unsigned short )0;
            sqlstm.sqtdso[1] = (unsigned short )0;
            sqlstm.sqhstv[2] = (unsigned char  *)&o_date;
            sqlstm.sqhstl[2] = (unsigned long )11;
            sqlstm.sqhsts[2] = (         int  )0;
            sqlstm.sqindv[2] = (         short *)0;
            sqlstm.sqinds[2] = (         int  )0;
            sqlstm.sqharm[2] = (unsigned long )0;
            sqlstm.sqadto[2] = (unsigned short )0;
            sqlstm.sqtdso[2] = (unsigned short )0;
            sqlstm.sqphsv = sqlstm.sqhstv;
            sqlstm.sqphsl = sqlstm.sqhstl;
            sqlstm.sqphss = sqlstm.sqhsts;
            sqlstm.sqpind = sqlstm.sqindv;
            sqlstm.sqpins = sqlstm.sqinds;
            sqlstm.sqparm = sqlstm.sqharm;
            sqlstm.sqparc = sqlstm.sqharc;
            sqlstm.sqpadto = sqlstm.sqadto;
            sqlstm.sqptdso = sqlstm.sqtdso;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



            if (sqlca.sqlcode == NOT_FOUND)
            {
                if (item_cnt != 0)
                    fprintf(rfp, "%10s%s\n", "", rpt_line);

                break;
            }
        }
    else
        for (; ;)
        {
            o_date.arr[o_date.len] = '\0';

/*            if (strcmp(o_date.arr, prev_date)) */
            if (strcmp((char *)o_date.arr, prev_date))
            {
                if (prev_date[0] != '\0')
                {
                    fprintf(rfp, "%s\n", rpt_line);
                    fprintf(rfp, "%10s", "");
                    line_cnt++;
                    rpt_line[0] = '\0';
                    item_cnt = 0;
                    if ((line_cnt + COLUMN_HEADLINES) >= MAX_RPT_LINES)
                    {
                        print_rpt_head(rfp, sort_order, page_cnt); 
                        page_cnt++;
                        line_cnt = HEADLINES + COLUMN_HEADLINES;
                    }
                    else
                    {
                        print_by_date_rpt_col(rfp); 
                        line_cnt += COLUMN_HEADLINES;
                    }
                }
/*                strcpy(prev_date, o_date.arr); */
                strcpy(prev_date, (char *)o_date.arr);                
            }

            sprintf(sub_rpt_line, "%11.5lf  %6.4f      ", o_freq, o_traffic);
            strcat(rpt_line, sub_rpt_line);

            item_cnt = (item_cnt + 1) % items_per_line;
            if (item_cnt == 0)
            {
                if (line_cnt == MAX_RPT_LINES)
                {
                    print_rpt_head(rfp, sort_order, page_cnt); 
                    page_cnt++;
                    line_cnt = HEADLINES + COLUMN_HEADLINES;
                }
                fprintf(rfp, "%s\n", rpt_line);
                rpt_line[0] = '\0';
                line_cnt++;
            }

            /* EXEC SQL FETCH C1 INTO :o_freq, :o_traffic, :o_date; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 3;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )108;
            sqlstm.selerr = (unsigned short)1;
            sqlstm.sqlpfmem = (unsigned int  )0;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlstm.sqfoff = (         int )0;
            sqlstm.sqfmod = (unsigned int )2;
            sqlstm.sqhstv[0] = (unsigned char  *)&o_freq;
            sqlstm.sqhstl[0] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[0] = (         int  )0;
            sqlstm.sqindv[0] = (         short *)0;
            sqlstm.sqinds[0] = (         int  )0;
            sqlstm.sqharm[0] = (unsigned long )0;
            sqlstm.sqadto[0] = (unsigned short )0;
            sqlstm.sqtdso[0] = (unsigned short )0;
            sqlstm.sqhstv[1] = (unsigned char  *)&o_traffic;
            sqlstm.sqhstl[1] = (unsigned long )sizeof(float);
            sqlstm.sqhsts[1] = (         int  )0;
            sqlstm.sqindv[1] = (         short *)0;
            sqlstm.sqinds[1] = (         int  )0;
            sqlstm.sqharm[1] = (unsigned long )0;
            sqlstm.sqadto[1] = (unsigned short )0;
            sqlstm.sqtdso[1] = (unsigned short )0;
            sqlstm.sqhstv[2] = (unsigned char  *)&o_date;
            sqlstm.sqhstl[2] = (unsigned long )11;
            sqlstm.sqhsts[2] = (         int  )0;
            sqlstm.sqindv[2] = (         short *)0;
            sqlstm.sqinds[2] = (         int  )0;
            sqlstm.sqharm[2] = (unsigned long )0;
            sqlstm.sqadto[2] = (unsigned short )0;
            sqlstm.sqtdso[2] = (unsigned short )0;
            sqlstm.sqphsv = sqlstm.sqhstv;
            sqlstm.sqphsl = sqlstm.sqhstl;
            sqlstm.sqphss = sqlstm.sqhsts;
            sqlstm.sqpind = sqlstm.sqindv;
            sqlstm.sqpins = sqlstm.sqinds;
            sqlstm.sqparm = sqlstm.sqharm;
            sqlstm.sqparc = sqlstm.sqharc;
            sqlstm.sqpadto = sqlstm.sqadto;
            sqlstm.sqptdso = sqlstm.sqtdso;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



            if (sqlca.sqlcode == NOT_FOUND)
            {
                if (item_cnt != 0)
                    fprintf(rfp, "%s\n", rpt_line);

                break;
            }
        }
    
    /* EXEC SQL CLOSE C1; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 3;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )135;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    fprintf(rfp, "\n\n%57s** END OF REPORT **", "");
    fclose(rfp);

    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", fname);
    system(cmdline);
/*
    unlink(fname);
*/

    exit(0);

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    fprintf(rfp, "%s\n", err_msg);
    fclose(rfp);

force_exit:
    exit(1);

}


/**********************************************************************/
/*  print channel traffic report column heading                       */
/**********************************************************************/

void print_by_date_rpt_col(rfp)
FILE   *rfp;
{
    int    i;

    fprintf(rfp, "\n\nMEASUREMENT DATE : %s\n\n", o_date.arr);
    for (i = 0; i < items_per_line - 1; i++)
        fprintf(rfp, "FREQUENCY    TRAFFIC     ");
    fprintf(rfp, "FREQUENCY    TRAFFIC\n");
    for (i = 0; i < items_per_line - 1; i++)
        fprintf(rfp, "===========  =======     ");
    fprintf(rfp, "===========  =======\n");
}


/**********************************************************************/
/*  print channel traffic report heading                              */
/**********************************************************************/

void print_rpt_head(rfp, sort_order, page_cnt)
FILE    *rfp;
int     sort_order;
int     page_cnt;
{
   if (page_cnt > 1)
       fprintf(rfp, "\f");
   fprintf(rfp, "USER ID  : %-15s%26s", user_id, "");
   fprintf(rfp, "ENHANCED SPECTRUM MANAGEMENT SYSTEM");
   fprintf(rfp, "%27sPAGE    : %-d\n", "", page_cnt);
   fprintf(rfp, "RUN DATE : %s", sys_date);
   fprintf(rfp, "%35sMRS CHANNEL MAINTENANCE REPORT", "");
   fprintf(rfp, "%30sPROGRAM : esemctmr\n", "");
   if (sort_order == BY_FREQ)
   {
       fprintf(rfp, "%58s(SORTED BY FREQUENCY)\n\n\n","");     //20180627 Cyrus Add ,"" at the end , cause  ‘%s’ expects a matching ‘char *’ argument
       fprintf(rfp, "                                  MEASURED");
       fprintf(rfp, "                               MEASURED                               MEASURED\n"); 
       fprintf(rfp, "%10sFREQUENCY     TRAFFIC     DATE         ", "");
       fprintf(rfp, "FREQUENCY     TRAFFIC     DATE         FREQUENCY     TRAFFIC     DATE\n");
       fprintf(rfp, "%10s===========   =======   ========       ", "");
       fprintf(rfp, "===========   =======   ========       ===========   =======   ========\n");
   }
   else
   {
	   
       fprintf(rfp, "%55s(SORTED BY MEASUREMENT DATE)\n\n",""); //20180627 Cyrus Add ,"" at the end , cause  ‘%s’ expects a matching ‘char *’ argument
       print_by_date_rpt_col(rfp);
   }
}
