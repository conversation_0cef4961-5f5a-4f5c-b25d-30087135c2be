package com.emc.dao.mapper;

import com.emc.model.Station;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for Station objects.
 */
public class StationRowMapper implements RowMapper<Station> {
    
    @Override
    public Station mapRow(ResultSet rs, int rowNum) throws SQLException {
        Station station = new Station();
        
        station.setSysCategory(rs.getString("SYS_CATEGORY").charAt(0));
        station.setSysType(rs.getString("SYS_TYPE"));
        station.setSysNo(rs.getString("SYS_NO"));
        station.setSysSuffix(rs.getString("SYS_SUFFIX"));
        station.setBaseNo(rs.getInt("BASE_NO"));
        station.setEast(rs.getInt("EAST"));
        station.setNorth(rs.getInt("NORTH"));
        station.setLicType(rs.getString("LIC_TYPE"));
        station.setLicNo(rs.getString("LIC_NO"));
        station.setStationType(rs.getString("STATION_TYPE").charAt(0));
        station.setAntType(rs.getString("ANT_TYPE"));
        station.setHalt(rs.getInt("HALT"));
        station.setAzMaxRad(rs.getInt("AZ_MAX_RAD"));
        station.setPwSign(rs.getString("PW_SIGN").charAt(0));
        station.setPwDbw(rs.getFloat("PW_DBW"));
        station.setSubDistrict(rs.getString("SUBDISTRICT"));
        station.setCancelDate(rs.getString("CANCEL_DATE"));
        
        return station;
    }
}
