/**********************************************************************/
/*                                                                    */
/*    Module Name   :  screen.c                                       */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  <PERSON><PERSON> <PERSON><PERSON>                                      */
/*                                                                    */
/*    Callers       :                                                 */  
/*                                                                    */
/*    Parameters    :                                                 */
/*                                                                    */
/*    Called Modules:  cursesX screen handling functions              */
/*                                                                    */
/*    Purpose       :  provides a number of useful screen functions   */
/*                     which in turns call cursesX screen functions.  */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. <PERSON><PERSON>k      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <curses.h>
#include <time.h>
#include "../include/global.h"

#define  SYSTEM_TITLE    "ENHANCED SPECTRUM MANAGEMENT SYSTEM"

#ifndef SELECT_ERROR
#define SELECT_ERROR    -2
#endif

#ifndef OK
#define OK       0
#endif

#ifndef ERROR
#define ERROR    -1
#endif

#ifndef EXIT
#define EXIT     -1
#endif

#ifndef DUMMY
#define DUMMY    -1
#endif

#ifndef QUIT
#define  QUIT    1
#endif

#define  NEWLINE      '\n'

#ifndef BLANK
#define  BLANK        ' '
#endif

void disp_heading(char*,char*,char*);
void centre_msg(int, char *, int, int);
void disp_err(char *);
void clear_err();
void clear_msg();
void disp_space(int, int, int);

 /*void read_str(char *, char *, int, int, int, int *, int); */
int toupper();
int disp_win_heading();

void disp_win_lines(WINDOW *, char **, int, int, int);
void disp_line_with_attr(WINDOW *, int, char *, int, int);

/*******************************************************************/
/* displays screen heading with string constant 'SYSTEM_TITLE' as  */
/* the 1st title line and 'sub_title' as the 2nd title line        */
/*******************************************************************/

void disp_heading(screen_id, sub_title, system_date)
char    *screen_id;
char    *sub_title;
char    *system_date;
{
    char   disp_str[20];

    sprintf(disp_str, "(%s)", screen_id);
    centre_msg(0, SYSTEM_TITLE, A_NORMAL, A_NORMAL);
    mvaddstr(0, 0, disp_str);
    mvaddstr(0, 72, system_date);
    centre_msg(1, sub_title, A_NORMAL, A_NORMAL);
}


/*******************************************************************/
/* set display attribute to 'disp_attr', then display 'str' at     */
/* the centre of 'row', then set display attribute to 'orig_attr'  */
/*******************************************************************/

void centre_msg(row, str, disp_attr, orig_attr)
int     row;
char    *str;
int     disp_attr, orig_attr;
{
    int x_coord;

    x_coord = (80 - strlen(str)) / 2 - 1;
    if ((disp_attr == DUMMY) || (orig_attr == DUMMY))
    {
        mvaddstr(row, x_coord, str);
        refresh();
    }
    else
    {
        attrset(disp_attr);
        mvaddstr(row, x_coord, str);
        refresh();
        if (disp_attr != orig_attr)
            attrset(orig_attr);
    }
}


/*******************************************************************/
/* displays error message 'str' at line 23 and 'beep' the user     */
/*******************************************************************/

void disp_err(str)
char    *str;
{
    clear_err();
    attrset(A_REVERSE); 
    centre_msg(23, str, DUMMY, DUMMY);
    refresh();
    attroff(A_REVERSE);
    beep();
}


/*******************************************************************/
/* clear error message at line 23                                  */
/*******************************************************************/

void clear_err()
{
    clear_msg(23);
}


/*******************************************************************/
/* clear message at line 'ypos'                                    */
/*******************************************************************/

void clear_msg(ypos)
{
    attrset(A_NORMAL);
    disp_space(ypos, 0, 80);
} 


/*******************************************************************/
/* display spaces starting at 'y_coord' and 'x_coord' with a       */
/* length of 'len'                                                 */
/*******************************************************************/

void disp_space(y_coord, x_coord, len)
int	y_coord, x_coord, len;
{
    char    space[len+1];
    register int    i;

    for (i = 0; i < len; i++)
        space[i] = BLANK;

    space[len] = '\0';

	//printf( "y_coord%d,%d,%s\n\r",y_coord,x_coord,space); /*Cyrus Debug*/	

    mvaddstr(y_coord, x_coord, space);
    refresh();
}


/*******************************************************************/
/* gets a user-input string into 'r_str', if 'timeout' > 0, use    */
/* 'timeout' as the user response time limit.                      */
/* 'msg' is the default value of 'r_str'.                          */
/*******************************************************************/

void read_str(r_str, msg, max_len, modifier, timeout, term_code, attr)
char *r_str, *msg;
int max_len;
unsigned int modifier;
int timeout, *term_code, attr;
{ int i,row,col;
  int len;
  int modified = FALSE;
  time_t time1;

  attron(attr);
  getyx(stdscr,row,col);

  if (*msg!='\0')
  {
    addstr(msg); 
    strcpy(r_str,msg);
    len=strlen(msg);
  }
  else
    len=0;

  refresh();

  for (i=len;i<max_len;i++) addch(inch()|attr);
  move(row,col+len); 
  refresh();
  keypad(stdscr,TRUE); 
  for (i=len,time1=time((long *)0);i<max_len;)
  {
    if (timeout>0) if (time(0)>(time1+timeout)) 
    {
      *term_code = TIME_OUT;
      break;
    }
    if ((*term_code=getch())==ERR) continue;
    if (modifier&TRM_CVTLOW) *term_code = toupper(*term_code);
    if (*term_code=='\n') break;
/*
    if (modified == FALSE) modified = TRUE;
      if (*term_code==KEY_F(1)) break;
      if (*term_code==KEY_F(4)) break;
*/
    if ((i>0)&&((*term_code==127)||(*term_code=='\b')||
        (*term_code==KEY_LEFT)||(*term_code==KEY_BACKSPACE)))
    { i--; mvaddch(row,col+i,' '); 
      move(row,col+i); refresh(); continue;
    }
    if ((*term_code<32)||(*term_code>=127)) { beep(); continue; }
    r_str[i++] = *term_code; 
    if (modifier&TRM_NOECHO) addch(' '); else addch(*term_code); refresh();
    
    
  }

  if (max_len > 1) 
      r_str[i]='\0'; 

/*
  if ((len!=0) && (modified == FALSE))
      strcpy(r_str, msg);
*/

  attroff(attr); refresh();
}


/*******************************************************************/
/* draws a vertical or horizontal line depending on whether        */
/* 'row1' = 'row2' or 'col1' = 'col2'                              */
/*******************************************************************/

int draw_line(row1, col1, row2, col2, attr1, attr2)
int row1, col1, row2, col2, attr1, attr2;
{ 
    register int row, col;

    if ((row2 < row1) || (col2 < col1))
        return ERROR;

    printf("%c)0%c~", ESCCHAR, ESCCHAR); 
    attrset(attr1);
    for (row = row1; row <= row2; row++) 
          for (col = col1; col <= col2; col++) 
              mvaddch(row, col, (col1 == col2 ? 248 : 241)); 
    attrset(attr2); 
	
	return (0);
}


/*******************************************************************/
/* move spaces to an area defined by (row1, col1), (row2, col2)    */
/*******************************************************************/

int erase_disp(row1, col1, row2, col2)
int row1, col1, row2, col2;
{ 
    register int    row, col;

    if ((row2 < row1) || (col2 < col1))
        return ERROR;

    for (row = row1; row <= row2; row++) 
        for (col = col1; col <= col2; col++) 
            mvaddch(row, col, ' ');
    refresh();
	return (0);
}


/*******************************************************************/
/* clear to end of line starting from (row, col)                   */
/*******************************************************************/

void erase_line(row, col)
int row, col;
{ 
  move(row,col);
  clrtoeol(); 
  refresh(); 
}


/*******************************************************************/
/* this subroutine hides the cursor which can then be made visible */
/* again by subroutine show_cursor.  applicable in vt_340.         */
/*******************************************************************/

void hide_cursor()
{
  printf("%c[?25l",ESCCHAR);
}


/*******************************************************************/
/* this subroutine makes the cursor visible again                  */
/*******************************************************************/

void show_cursor()
{
  printf("%c[?25h",ESCCHAR);
}


/*******************************************************************/
/* This subroutine enables the user to choose option within 'wp'   */
/* He can travel around the options by <KEY_UP> and <KEY_DOWN>     */
/* , confirm selection by pressing <Enter> (and quit the window),  */
/* and quit the window by pressing 'Q'.                            */
/* 'wp' is displayed with single-lined borders if 'border' is set  */
/*  to TRUE.                                                       */
/* 'heading' is an array to store the column headings within 'wp'  */
/*******************************************************************/

#define  PROMPT              "<Enter> - select"
#define  PROMPT_WITH_QUIT    "<Enter> - select   'Q' - quit"

int select_entry(wp,border,y1,x1,y2,x2,array,heading,max_len,with_quit,curr_size,err_msg)
WINDOW  *wp;
int     border;
int     y1, x1, y2, x2;
char    *array[];
char    *heading[];
int     max_len;

int     curr_size;
char    *err_msg;
{
    int    row1, col1, row2, col2;    /* diagonal corners of window 'wp' */
    int    win_size;
    int    start_entry;
    int    action;
    int    array_cnt, win_cnt;
    int    status = (!QUIT);
    int    skip_lines = 0;
    int    disp_area;
    int    prompt_len;
    char   s[80];


    if ((y2 < y1) || (x2 < x1))
    {
        sprintf(err_msg, "Invalid coordinates (y1, x1), (y2, x2): %d %d %d %d",
                y1, x1, y2, x2);
        return SELECT_ERROR;
    }

    prompt_len = (with_quit)? strlen(PROMPT_WITH_QUIT) : strlen(PROMPT);


    /************************/
    /* draw border for 'wp' */
    /************************/

    if (border)
    {
        int    col3;

        row1 = y1 - 1; col1 = x1 - 1;
        row2 = y2 + 1; col2 = x2 + 1;
        draw_line(row1, col1, row1, col2, A_BOLD, A_BOLD);
        draw_line(row1, col2, row2, col2, A_BOLD, A_BOLD);
        draw_line(row1, col1, row2, col1, A_BOLD, A_BOLD);
        draw_line(row2, col1, row2, col2, A_BOLD, A_BOLD);

        if (col2 - col1 <= prompt_len)
        {
            sprintf(err_msg,
                    "Border too narrow for prompt, Press any key to quit");
            return SELECT_ERROR;
        }

        col3 = col1 + (col2 - col1 - prompt_len) / 2;

        if (with_quit)
            mvaddstr(row2, col3, PROMPT_WITH_QUIT);
        else
            mvaddstr(row2, col3, PROMPT);

        /* draw the 4 corners of 'wp' */
        mvaddch(row1, col1, 236);
        mvaddch(row1, col2, 235);
        mvaddch(row2, col1, 237);
        mvaddch(row2, col2, 234);

        attrset(A_NORMAL);
        refresh();
    }
    
/*
sprintf(s,"%d %d %d %d %d", curr_size, y1, x1, y2, x2);
mvaddstr(23, 0, s);
*/
    if (heading != (char **)NULL)
        skip_lines = disp_win_heading(wp, heading);

    win_size = y2 - y1 + 1;
    if ((disp_area = y2 - y1 - skip_lines + 1) < curr_size)
/*
        disp_win_lines(wp, array, 0, skip_lines, win_size);
*/
        disp_win_lines(wp, array, 0, skip_lines, curr_size);
    else
/*
        disp_win_lines(wp, array, 0, skip_lines, curr_size + skip_lines);
*/
        disp_win_lines(wp, array, 0, skip_lines, win_size);

    disp_line_with_attr(wp, skip_lines, array[0], A_REVERSE, A_NORMAL);

    action = toupper(wgetch(wp));

    if ((with_quit != TRUE) || (action != 'Q'))
        for (array_cnt = 0, win_cnt = skip_lines; 
             (action != NEWLINE) && (status != QUIT); )
        {
            switch (action)
            {
                case KEY_UP:
                    if (win_cnt == skip_lines)
                        if (array_cnt == 0)
                        {
                            beep();
                            break;
                        }
                        else
                        {
                            /* scroll up 1 line */
                            array_cnt--;
                            disp_win_lines(wp, array, array_cnt,
                                           skip_lines, win_size);
                            disp_line_with_attr(wp,
                                                skip_lines,
                                                array[array_cnt],
                                                A_REVERSE,
                                                A_NORMAL);
                            break;
                        }
                    else
                    {
                        disp_line_with_attr(wp,
                                            win_cnt,
                                            array[array_cnt],
                                            A_NORMAL,
                                            A_REVERSE);
                        array_cnt--;
                        win_cnt--;
                        disp_line_with_attr(wp,
                                            win_cnt,
                                            array[array_cnt],
                                            A_REVERSE,
                                            A_NORMAL);
                    }
                    break;

                case KEY_DOWN:
                    if (array_cnt == curr_size - 1)
                    {
                        beep();
                        break;
                    }
                    if (win_cnt == win_size - 1)
                    {
                        /* scroll down 1 line */
                        array_cnt++;
                        disp_win_lines(wp, array, array_cnt-win_cnt+skip_lines,
                                       skip_lines, win_size);
                        disp_line_with_attr(wp,
                                            win_cnt,
                                            array[array_cnt],
                                            A_REVERSE,
                                            A_NORMAL);
                        break;
                    }
                    else
                    {
                        disp_line_with_attr(wp,
                                            win_cnt,
                                            array[array_cnt],
                                            A_NORMAL,
                                            A_REVERSE);
                        array_cnt++;
                        win_cnt++;
                        disp_line_with_attr(wp,
                                            win_cnt,
                                            array[array_cnt],
                                            A_REVERSE,
                                            A_NORMAL);
                    }
                    break;

                default:
                    beep();
                    break;
            }

            action = toupper(wgetch(wp));
            if (action == 'Q')
			{
                if (with_quit == FALSE)
                    beep();
                else
                    status = QUIT;
			}
        }

    return((action == 'Q') ? EXIT : array_cnt);
}


/*******************************************************************/
/* displays column headings within 'wp'                            */
/*******************************************************************/

int disp_win_heading(wp, heading)
WINDOW *wp;
char   **heading;
{
    register int    i;

    for (i = 0; heading[i] != (char *)NULL; i++)
        mvwaddstr(wp, i, 0, heading[i]);
    wrefresh(wp);

    return(i);
}


/*******************************************************************/
/* displays detail lines within 'wp'                               */
/*******************************************************************/

void disp_win_lines(wp, array, start_entry, skip_lines, disp_size)
WINDOW  *wp;
char    *array[];
int     start_entry;
int     disp_size;
{
    register int    i, j;

    for (i = skip_lines, j = start_entry; i < disp_size; i++, j++)
        mvwaddstr(wp, i, 0, array[j]);
    wrefresh(wp);
}


/*******************************************************************/
/* displays 'disp_line' at ypos with attribute 'attr'              */
/*******************************************************************/

void disp_line_with_attr(wp, ypos, disp_line, attr, restore_attr)
WINDOW  *wp;
int     ypos;
char    *disp_line;
int     attr;
int     restore_attr;
{
    wattrset(wp, attr);
    mvwaddstr(wp, ypos, 0, disp_line);
    wrefresh(wp);
    wattrset(wp, restore_attr);
}


/*******************************************************************/
/* clear border lines                                              */
/*******************************************************************/

void clear_border(y1, x1, y2, x2, err_msg)
int     y1, x1, y2, x2;
char    *err_msg;
{
    int    row1, col1, row2, col2;
    int    win_size;
    int    array_cnt, win_cnt;

    row1 = y1 - 1; col1 = x1 - 1;
    row2 = y2 + 1; col2 = x2 + 1;
    erase_disp(row1, col1, row2, col2);
}
