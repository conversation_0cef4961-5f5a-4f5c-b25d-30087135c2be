#define MAX_SEP         15
#define MAX_BAND        6
#define MAX_NOISE       6
#define MAX_OFF_CHAN    15
#define MAX_SUBDIST     75

#define FINE_REF_DIST   7.0

struct  off_chan_tag
{
    float   ch_sep;              /* channel separation          */
    float   rej_db[4];           /* off-channel rejection in dB */
};
typedef struct off_chan_tag  OFF_CHANNEL;

struct  subdist_tag
{
    char    sub_district[4];     /* sub-district code           */
    char    dist_type;           /* district type               */
    int     noise_code;
    int     cull_dist[4][2];     /* culling distance of LOWVHF, */
                                 /* HIGHVHF, UHF and UHF800     */
                                 /* of heavy duty station and   */
                                 /* regular duty station  and   */
};
typedef struct subdist_tag  SUBDIST;
