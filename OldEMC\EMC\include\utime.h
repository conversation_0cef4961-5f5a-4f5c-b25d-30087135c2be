/*
 *		@(#)utime.h	6.1	(ULTRIX)	11/19/91
 */

/************************************************************************
 *									*
 *			Copyright (c) 1987, 1989 by			*
 *		Digital Equipment Corporation, Maynard, MA		*
 *			All rights reserved.				*
 *									*
 *   This software is furnished under a license and may be used and	*
 *   copied  only  in accordance with the terms of such license and	*
 *   with the  inclusion  of  the  above  copyright  notice.   This	*
 *   software  or  any  other copies thereof may not be provided or	*
 *   otherwise made available to any other person.  No title to and	*
 *   ownership of the software is hereby transferred.			*
 *									*
 *   This software is  derived  from  software  received  from  the	*
 *   University    of   California,   Berkeley,   and   from   Bell	*
 *   Laboratories.  Use, duplication, or disclosure is  subject  to	*
 *   restrictions  under  license  agreements  with  University  of	*
 *   California and with AT&T.						*
 *									*
 *   The information in this software is subject to change  without	*
 *   notice  and should not be construed as a commitment by Digital	*
 *   Equipment Corporation.						*
 *									*
 *   Digital assumes no responsibility for the use  or  reliability	*
 *   of its software on equipment which is not supplied by Digital.	*
 *									*
 ************************************************************************/
/************************************************************************
 *			Modification History				*
 *									*
 *	Mark A. Parenti, 04-Sep-1987					*
 * 		Moved structure definition from utime.c			*
 *	Jon Reeves, 18-Jul-1989
 *		Added function declaration for X/Open
 *									*
 ************************************************************************/


#ifndef __XUTIME__
#define __XUTIME__

/*	structure used to pass info to utime call
 */
struct utimbuf
	{
	time_t	actime;
	time_t	modtime;
};

extern int	utime();

#endif /* __XUTIME__ */
