package com.emc.dao;

import com.emc.model.AddEq;

import java.util.List;
import java.util.Optional;

/**
 * DAO interface for AddEq operations.
 * This matches the original ProC ADD_EQ table queries.
 */
public interface AddEqDao extends BaseDao<AddEq, String> {
    
    /**
     * Finds additional equipment loss by system identification and equipment type.
     * This matches the original ProC feeder loss lookup.
     *
     * @param sysCategory System category
     * @param sysType System type
     * @param sysNo System number
     * @param sysSuffix System suffix
     * @param baseNo Base number
     * @param addEqType Additional equipment type ('L' for loss)
     * @return Optional containing the equipment record if found
     */
    Optional<AddEq> findBySystemIdAndType(char sysCategory, String sysType, String sysNo, 
                                         String sysSuffix, int baseNo, char addEqType);
    
    /**
     * Finds feeder loss for a specific station.
     * This is a convenience method for the common case of finding loss type 'L'.
     *
     * @param sysCategory System category
     * @param sysType System type
     * @param sysNo System number
     * @param sysSuffix System suffix
     * @param baseNo Base number
     * @return The feeder loss value, or 0.0 if not found
     */
    float findFeederLoss(char sysCategory, String sysType, String sysNo, String sysSuffix, int baseNo);
}
