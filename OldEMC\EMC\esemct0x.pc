/**********************************************************************/
/*                                                                    */
/*    Module Name   :  batch_traffic_update (esemct0x.pc)             */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  esemfp0f.c through 'system' statement          */  
/*                                                                    */
/*    Parameters    :  user id.                                       */
/*                  :  user password                                  */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Read tx frequency and channel traffic from     */
/*                     'traffic.dat" to update the ORACLE table       */
/*                     CHANNEL_TRAFFIC.                               */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/


#include <stdlib.h>
#include <curses.h>
#include <string.h>
#include <stdio.h>
#include "../include/define.h"
#include "../include/emc.h"

#ifndef ERROR
#define ERROR    1
#endif

#ifndef OK
#define OK       0
#endif

#ifndef  DUMMY
#define  DUMMY        -1
#endif

#define  TRAIL            0
#define  LEADING          1

#define  TRAFFIC_FILE     "traffic.dat"

#define  LOGIN_OK       0

#define  CTRL_Z         26
#define  DATE_LEN       8      /* date format in dd/mm/yy */


char            *getenv();


EXEC SQL BEGIN DECLARE SECTION;

    char    o_dummy;
    double  o_tx_freq;
    double  o_tx_freq_lo;
    double  o_tx_freq_hi;
    float   o_load_per_mobile;
    int     o_cnt;
    VARCHAR o_date[9];

EXEC SQL END DECLARE SECTION;

EXEC SQL INCLUDE SQLCA;


main(argc, argv)
int     argc;
char    **argv;
{
    char       instr[30];
    char       str[80];
    char       tf_name[150];
    char       err_msg[80];
    char       audit[120];
    char       s[80];
    char       passwd[20];
    FILE       *afp, *tfp;
    char       s_tx_freq[20], s_load_per_mobile[20], s_date[20];
    int        err_cnt = 0;
    int        upd_cnt = 0;
    int        ins_cnt = 0;
    int        cnt = 1;


    EXEC SQL WHENEVER NOT FOUND CONTINUE;

    strcpy(user_id, argv[1]);
    strcpy(passwd, argv[2]);

    emc_dir = getenv("EMC");
    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);

    sprintf(audit, "%s/log/traffic.%s", emc_dir, yymmdd);
    if ((afp = fopen(audit, "w")) == (FILE *)NULL)
    {
        printf("\nFail to open error log");
        exit(1);
    }

    if (user_login(user_id, passwd, err_msg) != LOGIN_OK)
    {
        strip_blank(err_msg, TRAIL);
        strcat(err_msg, "  ");
        strcat(err_msg, user_id);
        strcat(err_msg, passwd);
        goto force_exit;
    }

    EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn;

    sprintf(tf_name, "%s/%s", emc_dir, TRAFFIC_FILE);
    if((tfp = fopen(tf_name, "r")) == (FILE *) NULL)
    {
        printf("Fail to open traffic data file");
        fclose(afp);
        exit(1);
    }

    if (fgets(instr, sizeof(instr), tfp) == (char *) NULL)
    {
        fprintf(afp, "Empty traffic data file");
        fclose(afp); fclose(tfp);
        exit(1);
    }

    if (instr[0] == CTRL_Z)
    {
        fprintf(afp, "Empty traffic data file");
        fclose(afp); fclose(tfp);
        exit(1);
    }

    o_date.len = DATE_LEN;

    do
    {
        if (instr[0] == CTRL_Z)    /* This is to cater for the ^Z at the */
            break;                 /* end of the uploaded file           */

        sscanf(instr, "%s %s %s", s_tx_freq, s_load_per_mobile, s_date);
        if (!isnumber(s_tx_freq))
        {
             fprintf(afp, "Line %d: Non-numeric tx frequency\f", cnt);
             EXEC SQL ROLLBACK RELEASE;
             fclose(afp); fclose(tfp);
             exit(1);
        }

        if (!isnumber(s_load_per_mobile))
        {
             fprintf(afp, "Line %d: Non-numeric traffic loading\f", cnt);
             EXEC SQL ROLLBACK RELEASE;
             fclose(afp); fclose(tfp);
             exit(1);
        }

        if (strlen(s_date) != DATE_LEN)
        {
             fprintf(afp, "Line %d: Invalid date format\f", cnt);
             EXEC SQL ROLLBACK RELEASE;
             fclose(afp); fclose(tfp);
             exit(1);
        }

        sscanf(instr, "%lf %f %s", &o_tx_freq, &o_load_per_mobile, o_date.arr);
       
        o_tx_freq_lo = o_tx_freq - FREQ_EPSILON;
        o_tx_freq_hi = o_tx_freq + FREQ_EPSILON;

        EXEC SQL 
             UPDATE CHANNEL_TRAFFIC
             SET    LOAD_PER_MOBILE = :o_load_per_mobile,
                    LAST_UPDATE = TO_DATE(:o_date, 'DD/MM/YY')
             WHERE  TX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi;
/*
             WHERE  TX_FREQ = :o_tx_freq;
*/

  
        /********************************************************/
        /* If CHANNEL_TRAFFIC record does not exist for current */
        /* channel, then insert it into CHANNEL_TRAFFIC         */
        /********************************************************/
        if (sqlca.sqlcode == NOT_FOUND)
        {
/*
            EXEC SQL
                 SELECT 'X'
                 INTO   :o_dummy
                 FROM   MOBILE_CH
                 WHERE  RX_FREQ = :o_tx_freq;

            if (sqlca.sqlcode == NOT_FOUND)
            {
                fprintf(afp, "Line %d: %lf not found in mobile channel\n", 
                        cnt, o_tx_freq);
                err_cnt++;
                continue;
            }
*/

            EXEC SQL INSERT INTO CHANNEL_TRAFFIC
                 VALUES 
                 (:o_tx_freq, :o_load_per_mobile, TO_DATE(:o_date, 'DD/MM/YY'));
            ins_cnt++;
        }
        else
            upd_cnt++;

        cnt++;
    }
    while (fgets(instr, sizeof(instr), tfp) != (char *) NULL);

    fprintf(afp, "\nNo. of traffic records updated: %d\n", upd_cnt);
    fprintf(afp, "No. of new traffic records added: %d\f", ins_cnt);
    fclose(afp);
    EXEC SQL COMMIT WORK RELEASE;
    exit(0);


sqlerr_rtn:
    EXEC SQL ROLLBACK WORK RELEASE;

force_exit:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    fprintf(afp, "%s\n", err_msg);
    fclose(afp);
    exit(1);

}
