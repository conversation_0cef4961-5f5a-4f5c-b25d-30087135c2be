package com.emc.service;

import com.emc.model.Exist;
import com.emc.model.ExistFreq;
import com.emc.model.Propose;

import java.io.PrintWriter;
import java.util.List;

/**
 * Service for intermodulation analysis.
 */
public interface IntermodulationService {

    /**
     * Performs 2-signal intermodulation analysis.
     * Equivalent to the intermod_2 function in the original C++ code.
     */
    void intermod2();

    /**
     * Performs 3-signal intermodulation analysis.
     * Equivalent to the intermod_3 function in the original C++ code.
     */
    void intermod3();

    // Setter methods for data injection
    void setProp(Propose prop);
    void setExist(List<Exist> exist);
    void setFqList(List<ExistFreq> fqList);
    void setPropTxFreq(double propTxFreq);
    void setPropRxFreq(double propRxFreq);
    void setAfp(PrintWriter afp);
    void setFqCnt(int fqCnt);

    // Getter methods for results
    int getIntmod2VictTot();
    int getIntmod2Tx1Tot();
    int getIntmod2Tx2Tot();
    int getIntmod3VictTot();
    int getIntmod3Tx1Tot();
    int getIntmod3Tx3Tot();
}
