/**********************************************************************/
/*                                                                    */
/*    Module Name   :  cochaninf (esemco0r.pc)                        */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  <PERSON><PERSON> <PERSON><PERSON>                                      */
/*                                                                    */
/*    Callers       :  main (esemba0x.c)                              */  
/*                                                                    */
/*    Parameters    :                                                 */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Print all stations that share the same channel */
/*                     as the proposed station.                       */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. <PERSON>. Mak      Apr-93      Initial version                       */
/**********************************************************************/
/*   C. K. Wong     Dec-95                                            */
/*   Modify to screen the pager cochannel report because the pager    */
/*   channel is used by one system only.                              */
/**********************************************************************/

#include <stdio.h>
#include <string.h>
#include "../include/define.h"
#include "../include/emcext.h"
#include "../include/propext.h"
/* this is the new code */
#include "../include/existext.h"

#define  COCHANINF_LINES    35

#define  TX_RX      0
#define  RX_ONLY    1
#define  TX_ONLY    2

extern char msg[];

EXEC SQL BEGIN DECLARE SECTION;

    double  o_tx_freq;
    double  o_rx_freq;
    double  o_tone_freq;
    double  o_tx_freq_lo;
    double  o_rx_freq_lo;
    double  o_tx_freq_hi;
    double  o_rx_freq_hi;
    char    o_sys_category;
    VARCHAR o_sys_type[3];
    VARCHAR o_sys_no[8];
    VARCHAR o_sys_suffix[4];
    char    o_station_type;
    int     o_base_no;
    int     o_mobile_cnt;
    VARCHAR o_sub_district[4];
    VARCHAR o_cancel_date[10];
    int     o_grid_east;
    int     o_grid_north;
    VARCHAR o_client_name[49];
    VARCHAR o_buss_code[5];
    VARCHAR o_buss_desc[31];

EXEC SQL END DECLARE SECTION;

EXEC SQL INCLUDE SQLCA;


cochaninf()
{
    char   co_fname[120];
    char   cmdline[140];
    char   curr_sys[20], prev_sys[20];
    double prev_tx_freq = -1.0;
    double prev_rx_freq = -1.0;
    double prev_tone_freq = -1.0;
    FILE   *cfp;
    int    prev_base_no = -1;
    int    line_cnt = 0;
    int    page_cnt = 1;
    int    skip_lines;
    int    tx_mode;
    int    i;
    int    tot_mobile_cnt = 0;


    EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn;
    EXEC SQL WHENEVER NOT FOUND CONTINUE;

/*
#ifdef DEBUG
    printf("cochaninf\n");
fflush(stdout);
#endif
*/

    prev_sys[0] = '\0';
    o_tx_freq = prop_tx_freq; 
    o_rx_freq = prop_rx_freq; 

    fprintf(afp, "\n** Co-channel Stations Report\n");

    if ((o_tx_freq > FREQ_EPSILON) && (o_rx_freq > FREQ_EPSILON))
    {
        tx_mode = TX_RX;

        o_tx_freq_lo = o_tx_freq - FREQ_EPSILON;
        o_tx_freq_hi = o_tx_freq + FREQ_EPSILON;
        o_rx_freq_lo = o_rx_freq - FREQ_EPSILON;
        o_rx_freq_hi = o_rx_freq + FREQ_EPSILON;

        EXEC SQL DECLARE CO01 CURSOR FOR
             SELECT SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO,
                    TX_FREQ, RX_FREQ, NVL(TONE_FREQ, 0.0)
             FROM   BASE_CH
             WHERE  TX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
             OR     RX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
             ORDER  BY
                    SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, TX_FREQ,
                    RX_FREQ;
    }
    else
    {
        if (o_tx_freq <= FREQ_EPSILON)    /* Rx-only channel */
        {
            tx_mode = RX_ONLY;

            o_rx_freq_lo = o_rx_freq - FREQ_EPSILON;
            o_rx_freq_hi = o_rx_freq + FREQ_EPSILON;

            EXEC SQL DECLARE CO02 CURSOR FOR
                 SELECT SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO,
                        TX_FREQ, RX_FREQ, NVL(TONE_FREQ, 0.0)
                 FROM   BASE_CH
                 WHERE  RX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
                 ORDER  BY
                        SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO, 
                        CHANNEL_NO;
        }
        else    /* Tx-only channel */
        {
            tx_mode = TX_ONLY;

            o_tx_freq_lo = o_tx_freq - FREQ_EPSILON;
            o_tx_freq_hi = o_tx_freq + FREQ_EPSILON;

            EXEC SQL DECLARE CO03 CURSOR FOR
                 SELECT SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO,
                        TX_FREQ, RX_FREQ, NVL(TONE_FREQ, 0.0)
                 FROM   BASE_CH
                 WHERE  TX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
                 ORDER  BY
                        SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO, 
                        CHANNEL_NO;
        }
    }

    switch (tx_mode)
    {
        case TX_RX:
            EXEC SQL OPEN CO01;

            EXEC SQL
                 FETCH CO01
                 INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                       :o_sys_suffix, :o_base_no, :o_tx_freq, :o_rx_freq,
                       :o_tone_freq;
            break;

        case RX_ONLY:
            EXEC SQL OPEN CO02;

            EXEC SQL
                 FETCH CO02
                 INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                       :o_sys_suffix, :o_base_no, :o_tx_freq, :o_rx_freq,
                       :o_tone_freq;
            break;

        case TX_ONLY:
            EXEC SQL OPEN CO03;

            EXEC SQL
                 FETCH CO03
                 INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                       :o_sys_suffix, :o_base_no, :o_tx_freq, :o_rx_freq,
                       :o_tone_freq;
            break;
    }

    if (sqlca.sqlcode == NOT_FOUND)
    {
        fprintf(afp, "No co-channel stations found\n");
        goto close_cursor;
    }

    sprintf(co_fname, "%s/cochannel/%.5lf.%s", emc_dir, prop_tx_freq, 
            hhmmss);
    if ((cfp = fopen(co_fname, "w")) == (FILE *) NULL)
    {
        printf("Fatal error: fail to open co-channel station report : %s\n",
               co_fname);
        exit(1);
    }
    
    for ( ; ; )
    {
        o_sys_type.arr[o_sys_type.len] = '\0';
        o_sys_no.arr[o_sys_no.len] = '\0';
        o_sys_suffix.arr[o_sys_suffix.len] = '\0';

        sprintf(curr_sys, "%c%s%s-%s", o_sys_category, o_sys_type.arr,
                o_sys_no.arr, o_sys_suffix.arr);

        if ((abs(o_tx_freq - prev_tx_freq) <= FREQ_EPSILON)
        &&  (abs(o_rx_freq - prev_rx_freq) <= FREQ_EPSILON)
        &&  (abs(o_tone_freq - prev_tone_freq) <= FREQ_EPSILON))
            if (!strcmp(curr_sys, prev_sys) && (o_base_no == prev_base_no))
            {
                switch (tx_mode)
                {
                    case TX_RX:
                        EXEC SQL
                             FETCH CO01
                             INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                                   :o_sys_suffix, :o_base_no, :o_tx_freq,
                                   :o_rx_freq, :o_tone_freq;
                        break;

                    case RX_ONLY:
                        EXEC SQL
                             FETCH CO02
                             INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                                   :o_sys_suffix, :o_base_no, :o_tx_freq,
                                   :o_rx_freq, :o_tone_freq;
                        break;

                    case TX_ONLY:
                        EXEC SQL
                             FETCH CO03
                             INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                                   :o_sys_suffix, :o_base_no, :o_tx_freq,
                                   :o_rx_freq, :o_tone_freq;
                        break;
                }
                if (sqlca.sqlcode == NOT_FOUND)
                    break;

                continue;
            }

/*
system("echo \"esemco0r 1\" >> /tmp/debug");
sprintf(msg, "echo \"%c %s %s %s %d\" >> /tmp/debug",
o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr, o_base_no );
system(msg);
*/

        EXEC SQL
             SELECT SUBDISTRICT, EAST, NORTH, STATION_TYPE,
                    NVL(TO_CHAR(CANCEL_DATE), '-')
             INTO   :o_sub_district, :o_grid_east, :o_grid_north,
                    :o_station_type, :o_cancel_date
             FROM   STATION
             WHERE  SYS_CATEGORY = :o_sys_category
             AND    SYS_TYPE = :o_sys_type
             AND    SYS_NO = :o_sys_no
             AND    SYS_SUFFIX = :o_sys_suffix
             AND    BASE_NO = :o_base_no

             AND    STATION_FLAG IN ('P', 'A');
/* Alex Yeung 1999-08-05 */


        /*************************************************************/
        /*  if this is a cancelled station (CANCEL_DATE is not NULL) */
        /*  skip it                                                  */
        /*************************************************************/

/*
        if ((o_grid_east == 0) || (o_grid_north == 0))
*/

        if (o_cancel_date.arr[0] != '-')
        {
            switch (tx_mode)
            {
                case TX_RX:
                    EXEC SQL
                         FETCH CO01
                         INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                               :o_sys_suffix, :o_base_no, :o_tx_freq,
                               :o_rx_freq, :o_tone_freq;
                    break;

                case RX_ONLY:
                    EXEC SQL
                         FETCH CO02
                         INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                               :o_sys_suffix, :o_base_no, :o_tx_freq,
                               :o_rx_freq, :o_tone_freq;
                    break;

                case TX_ONLY:
                    EXEC SQL
                         FETCH CO03
                         INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                               :o_sys_suffix, :o_base_no, :o_tx_freq,
                               :o_rx_freq, :o_tone_freq;
                    break;
            }
            if (sqlca.sqlcode == NOT_FOUND)
                break;

            continue;
        }

        o_sub_district.arr[o_sub_district.len] = '\0';
/*
printf("sub east north: %s %d %d\n", o_sub_district.arr, o_grid_east, o_grid_north);
fflush(stdout);
*/
        if (sqlca.sqlcode == NOT_FOUND)
        {
            fprintf(afp, "STATION record not found for ");
            fprintf(afp, "%c%s%s-%s base no. : %d\n",
                    o_sys_category, o_sys_type.arr, o_sys_no.arr,
                    o_sys_suffix.arr, o_base_no);
            continue;
        }
/*
printf("curr: %s\n", curr_sys);
fflush(stdout);
*/
        if (strcmp(curr_sys, prev_sys))
        {
/*
printf("prev: %s\n", prev_sys);
fflush(stdout);
*/
            strcpy(prev_sys, curr_sys);
            o_tx_freq_lo = prop_tx_freq - FREQ_EPSILON;
            o_tx_freq_hi = prop_tx_freq + FREQ_EPSILON;
            o_rx_freq_lo = prop_rx_freq - FREQ_EPSILON;
            o_rx_freq_hi = prop_rx_freq + FREQ_EPSILON;

            switch (tx_mode)
            {
                case TX_RX:

/* Alex Yeung 1999-08-05
                    EXEC SQL
                         SELECT COUNT(UNIQUE MOBILE_NO)
*/
/* comment by Chen Yung
                    EXEC SQL
                         SELECT COUNT(DISTINCT MOBILE_NO)

                         INTO   :o_mobile_cnt
                         FROM   MOBILE_CH
                         WHERE  TX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
                         AND    RX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
                         AND    SYS_CATEGORY = :o_sys_category
                         AND    SYS_TYPE = :o_sys_type
                         AND    SYS_NO = :o_sys_no
                         AND    SYS_SUFFIX = :o_sys_suffix
                         AND    CANCEL_DATE IS NULL;
*/
                    EXEC SQL
                         SELECT COUNT(DISTINCT b.MOBILE_NO)
                         INTO   :o_mobile_cnt
                         FROM   MOBILE a, MOBILE_CH b
                         WHERE  b.TX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
                         AND    b.RX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
                         AND    b.SYS_CATEGORY = :o_sys_category
                         AND    b.SYS_TYPE = :o_sys_type
                         AND    b.SYS_NO = :o_sys_no
                         AND    b.SYS_SUFFIX = :o_sys_suffix
                         AND    b.CANCEL_DATE IS NULL
                         AND    a.SYS_CATEGORY = b.SYS_CATEGORY
                         AND    a.SYS_TYPE = b.SYS_TYPE
                         AND    a.SYS_NO = b.SYS_NO
                         AND    a.SYS_SUFFIX = b.SYS_SUFFIX
                         AND    a.MOBILE_NO = b.MOBILE_NO
                         AND    a.CANCEL_DATE IS NULL;
                         
                         break;

                case TX_ONLY :

/* Alex Yeung 1999-08-05
                    EXEC SQL
                         SELECT COUNT(UNIQUE MOBILE_NO)
*/
/* comment by Chen Yung
                    EXEC SQL
                         SELECT COUNT(DISTINCT MOBILE_NO)

                         INTO   :o_mobile_cnt
                         FROM   MOBILE_CH
                         WHERE  TX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
                         AND    RX_FREQ IS NULL
                         AND    SYS_CATEGORY = :o_sys_category
                         AND    SYS_TYPE = :o_sys_type
                         AND    SYS_NO = :o_sys_no
                         AND    SYS_SUFFIX = :o_sys_suffix
                         AND    CANCEL_DATE IS NULL;
*/
                    EXEC SQL
                         SELECT COUNT(DISTINCT b.MOBILE_NO)
                         INTO   :o_mobile_cnt
                         FROM   MOBILE a, MOBILE_CH b
                         WHERE  b.TX_FREQ between :o_rx_freq_lo and :o_rx_freq_hi
                         AND    b.RX_FREQ IS NULL
                         AND    b.SYS_CATEGORY = :o_sys_category
                         AND    b.SYS_TYPE = :o_sys_type
                         AND    b.SYS_NO = :o_sys_no
                         AND    b.SYS_SUFFIX = :o_sys_suffix
                         AND    b.CANCEL_DATE IS NULL
                         AND    a.SYS_CATEGORY = b.SYS_CATEGORY
                         AND    a.SYS_TYPE = b.SYS_TYPE
                         AND    a.SYS_NO = b.SYS_NO
                         AND    a.SYS_SUFFIX = b.SYS_SUFFIX
                         AND    a.MOBILE_NO = b.MOBILE_NO
                         AND    a.CANCEL_DATE IS NULL;

                    break;

                case RX_ONLY:

/* Alex Yeung 1999-08-05
                    EXEC SQL
                         SELECT COUNT(UNIQUE MOBILE_NO)
*/
/* comment by Chen Yung
                    EXEC SQL
                         SELECT COUNT(DISTINCT MOBILE_NO)

                         INTO   :o_mobile_cnt
                         FROM   MOBILE_CH
                         WHERE  RX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
                         AND    TX_FREQ IS NULL
                         AND    SYS_CATEGORY = :o_sys_category
                         AND    SYS_TYPE = :o_sys_type
                         AND    SYS_NO = :o_sys_no
                         AND    SYS_SUFFIX = :o_sys_suffix
                         AND    CANCEL_DATE IS NULL;
*/
                    EXEC SQL
                         SELECT COUNT(DISTINCT b.MOBILE_NO)
                         INTO   :o_mobile_cnt
                         FROM   MOBILE a, MOBILE_CH b
                         WHERE  b.RX_FREQ between :o_tx_freq_lo and :o_tx_freq_hi
                         AND    b.TX_FREQ IS NULL
                         AND    b.SYS_CATEGORY = :o_sys_category
                         AND    b.SYS_TYPE = :o_sys_type
                         AND    b.SYS_NO = :o_sys_no
                         AND    b.SYS_SUFFIX = :o_sys_suffix
                         AND    b.CANCEL_DATE IS NULL
                         AND    a.SYS_CATEGORY = b.SYS_CATEGORY
                         AND    a.SYS_TYPE = b.SYS_TYPE
                         AND    a.SYS_NO = b.SYS_NO
                         AND    a.SYS_SUFFIX = b.SYS_SUFFIX
                         AND    a.MOBILE_NO = b.MOBILE_NO
                         AND    a.CANCEL_DATE IS NULL;
                         
                    break;
            }
    
            tot_mobile_cnt += o_mobile_cnt;

/*
system("echo \"esemco0r 2\" >> /tmp/debug");
*/

/*
sprintf(msg, "echo \"%c %s %s %s %d\" >> /tmp/debug",
o_sys_category, o_sys_type.arr, o_sys_no.arr, o_sys_suffix.arr );
system(msg);
*/
            EXEC SQL
                 SELECT E_NAME, BUSS_CODE
                 INTO   :o_client_name, :o_buss_code
                 FROM   SYSTEM
                 WHERE  SYS_CATEGORY = :o_sys_category
                 AND    SYS_TYPE = :o_sys_type
                 AND    SYS_NO = :o_sys_no
                 AND    SYS_SUFFIX = :o_sys_suffix;

            if (sqlca.sqlcode == NOT_FOUND)
            {
                fprintf(afp, "SYSTEM record not found for %c-%s-%s-%s\n",
                        o_sys_category, o_sys_type.arr, o_sys_no.arr, 
                        o_sys_suffix.arr);
                continue;
            }

            o_client_name.arr[o_client_name.len] = '\0';
            o_buss_code.arr[o_buss_code.len] = '\0';
/*
printf("client buss_code: %s %s\n", o_client_name.arr, o_buss_code.arr);
fflush(stdout);
*/

/*
system("echo \"esemco0r 3\" >> /tmp/debug");
*/

/*
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_buss_code.arr);
system(msg);
*/
            EXEC SQL
                 SELECT BUSS_E_DESC
                 INTO   :o_buss_desc
                 FROM   BUSINESS
                 WHERE  BUSS_CODE = :o_buss_code;

            if (sqlca.sqlcode == NOT_FOUND)
            {
                fprintf(afp, "BUSINESS record not found for code : %s\n",
                       o_buss_code.arr);
                continue;
            }

            o_buss_desc.arr[o_buss_desc.len] = '\0';
/*
printf("buss: %s %d\n", o_buss_desc.arr, o_buss_desc.len);
fflush(stdin);
*/
        }

/* if statment added by CK Wong to screen the report of pager cochannel */
/* because the pager channel is used exclusively by one system only !   */

/*        if(strcmp(o_sys_type.arr,"01")!=0 && strcmp(o_sys_type.arr,"42")!=0) */
        if(strcmp((char *)o_sys_type.arr,"01")!=0 && strcmp((char *)o_sys_type.arr,"42")!=0)
        {
          print_cochaninf_line(cfp, curr_sys, &line_cnt, &page_cnt);
          cochan_tot++;
        }

        prev_tx_freq = o_tx_freq;
        prev_rx_freq = o_rx_freq;
        prev_tone_freq = o_tone_freq;
        prev_base_no = o_base_no;

        switch (tx_mode)
        {
            case TX_RX:
                EXEC SQL
                     FETCH CO01
                     INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                           :o_sys_suffix, :o_base_no, :o_tx_freq, 
                           :o_rx_freq, :o_tone_freq;
                break;

            case RX_ONLY:
                EXEC SQL
                     FETCH CO02
                     INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                           :o_sys_suffix, :o_base_no, :o_tx_freq,
                           :o_rx_freq, :o_tone_freq;
                break;

            case TX_ONLY:
                EXEC SQL
                     FETCH CO03
                     INTO  :o_sys_category, :o_sys_type, :o_sys_no, 
                           :o_sys_suffix, :o_base_no, :o_tx_freq,
                           :o_rx_freq, :o_tone_freq;
                break;
        }

        if (sqlca.sqlcode == NOT_FOUND)
            break;
    }

/* this is the new code */
    for (i = 0; i < prop_fq_cnt; i++)
        if ((prop->tx_channel == fq_list[i]->tx_channel)
        &&  (prop->rx_channel == fq_list[i]->rx_channel))
            if (prop->stn_node != fq_list[i]->stn_node)
            {
                int    j = fq_list[i]->stn_node;
/*
printf("i sysid node: %d %c%s%s%s %d\n", i,
exist[fq_list[i]->stn_node]->sys_category,
exist[fq_list[i]->stn_node]->sys_type,
exist[fq_list[i]->stn_node]->sys_no,
exist[fq_list[i]->stn_node]->sys_suffix,
fq_list[i]->stn_node);
*/
               if (line_cnt == COCHANINF_LINES)
                   line_cnt = 0;

               if (line_cnt == 0)
               {
                   print_cochaninf_head(cfp, &page_cnt);
                   page_cnt++;
               }

               sprintf(curr_sys, "%c%s%s-%s", exist[j]->sys_category,
                       exist[j]->sys_type, exist[j]->sys_no,
                       exist[j]->sys_suffix);
               fprintf(cfp, "   %-48s %-14s%2s%c%6s-%6s%-3s%4s%05d %05d%11s-\n",
                       "(PROPOSED STATION)", curr_sys, "",
                       exist[j]->station_type, "", "", exist[j]->sub_district, 
                       "", exist[j]->east_grid, exist[j]->north_grid, "");
               line_cnt++; 
               cochan_tot++; 
           }

    if (cochan_tot == 0)
    {
        fprintf(afp, "No co-channel stations found\n");
        fclose(cfp);
        unlink(co_fname);
    }
    else
    {
        skip_lines = 4 + (COCHANINF_LINES - line_cnt);
        for (i = 0; i < skip_lines; i++)
            fprintf(cfp, "\n");
        fprintf(cfp, "%47sTOTAL NO. OF MOBILES             :  %-4d\n", "",
                    tot_mobile_cnt);
        fprintf(cfp, "%47sTOTAL NO. OF CO-CHANNEL STATIONS :  %-3d\f", "",
                    cochan_tot);
        fclose(cfp);

/*
            sprintf(cmdline, "lp -dprinter1 %s > /dev/null", co_fname);
*/
            sprintf(cmdline, "cat %s >> %s", co_fname, print_file);
            system(cmdline);
    }


close_cursor:

    switch (tx_mode)
    {
        case TX_RX:
            EXEC SQL CLOSE CO01;
            break;

        case RX_ONLY:
            EXEC SQL CLOSE CO02;
            break;

        case TX_ONLY:
            EXEC SQL CLOSE CO03;
            break;
    }

/* commented out by Chen Yung
    return;
*/
/* changed by Chen Yung */
/* original: */
/*    return; */
    return(0);


sqlerr_rtn:
/* changed by Chen Yung */
/* original: */
/*    printf("%s\c", sqlca.sqlerrm.sqlerrmc); */
    printf("%s\n", sqlca.sqlerrm.sqlerrmc);
    fflush(afp);
    fclose(afp);
    exit(1);

}


/**********************************************************************/
/*  print co-channel details to co-channel report                     */
/**********************************************************************/

print_cochaninf_line(cfp, curr_sys, line_cnt, page_cnt)
FILE    *cfp;
char    *curr_sys;
int     *line_cnt, *page_cnt;
{

/*
#ifdef DEBUG
    printf("print_cochaninf_line\n");
#endif
*/

   if (*line_cnt == COCHANINF_LINES)
       *line_cnt = 0;

   if (*line_cnt == 0)
   {
       print_cochaninf_head(cfp, page_cnt);
       (*page_cnt)++;
   }

   fprintf(cfp, "   %-48s %-14s  %c   %4d%6s%-3s%4s%05d %05d %-30s\n",
           o_client_name.arr, curr_sys, o_station_type, o_mobile_cnt, "",
           o_sub_district.arr, "", o_grid_east, o_grid_north, o_buss_desc.arr);
   (*line_cnt)++; 
}


/**********************************************************************/
/*  print co-channel report heading                                   */
/**********************************************************************/

print_cochaninf_head(cfp, page_cnt)
FILE    *cfp;
int     *page_cnt;
{

/*
#ifdef DEBUG
    printf("print_cochaninf_head\n");
#endif
*/

   if (*page_cnt > 1)
       fprintf(cfp, "\f");

   fprintf(cfp, "RUN DATE: %s%23s", sys_date, "");
   fprintf(cfp, "***************************************************");
   fprintf(cfp, "%23sPAGE   : %-d\n", "", *page_cnt);
   fprintf(cfp, "RUN TIME: %s%23s", sys_time, "");
   fprintf(cfp, "*                                                 *");
   fprintf(cfp, "%23sPROGRAM: esemco0r\n", "");
   fprintf(cfp, "USER ID : %-19s%12s", emc_uid, "");
   fprintf(cfp, "*                 E M C   ANALYSIS                *\n");
   fprintf(cfp, "%41s", "");
   fprintf(cfp, "*              CO-CHANNEL INFORMATION             *\n");
   fprintf(cfp, "%41s", "");
   fprintf(cfp, "*                                                 *\n");
   fprintf(cfp, "%41s", "");
   fprintf(cfp, "*            PROP. SYSTEM : %c%s%s-%s%11s*\n",
           prop->sys_category, prop->sys_type, prop->sys_no, 
           prop->sys_suffix, "");
   fprintf(cfp, "%41s", "");
   fprintf(cfp, "*             TX FREQ : %11.5lf MHz           *\n",
           prop_tx_freq);
   fprintf(cfp, "%41s", "");
   fprintf(cfp, "*             RX FREQ : %11.5lf MHz           *\n",
           prop_rx_freq);
   fprintf(cfp, "%41s", "");
   fprintf(cfp, "*                                                 *\n");
   fprintf(cfp, "%41s", "");
   fprintf(cfp, "***************************************************");
   fprintf(cfp, "\n\n\n\n");
   fprintf(cfp, "%67sSTN  NO. OF  SUB-\n", "");
   fprintf(cfp, "%3sLICENCE NAME", "");
   fprintf(cfp, "%37sSYSTEM-ID%6sTYPE MOBILES DISTRICT EAST ", "", "");
   fprintf(cfp, " NORTH BUSINESS CLASSIFICATION\n");
   fprintf(cfp, "%3s================================================", "");
   fprintf(cfp, " ============== ==== ======= ========");
   fprintf(cfp, " ===== ===== ==============================\n\n");
}
