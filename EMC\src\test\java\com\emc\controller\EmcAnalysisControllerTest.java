package com.emc.controller;

import com.emc.model.EmcAnalysisRequest;
import com.emc.model.EmcAnalysisResult;
import com.emc.service.EmcAnalysisService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class EmcAnalysisControllerTest {

    @Mock
    private EmcAnalysisService emcAnalysisService;

    @InjectMocks
    private EmcAnalysisController emcAnalysisController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(emcAnalysisController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void testAnalyzeEmc_Success() throws Exception {
        // Setup
        EmcAnalysisRequest request = new EmcAnalysisRequest();
        request.setPrinterId("printer1");
        request.setPrintFile("test_print");
        request.setEmcUid("TEST_EMC_UID");
        request.setInteractive(false);

        EmcAnalysisResult result = new EmcAnalysisResult();
        result.setEmcUid("TEST_EMC_UID");
        result.setStatus("COMPLETED");
        result.setPrintFile("test_print.TEST_EMC_UID");
        
        when(emcAnalysisService.performAnalysis(any(EmcAnalysisRequest.class))).thenReturn(result);
        
        // Execute and Verify
        mockMvc.perform(post("/api/emc/analyze")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.emcUid").value("TEST_EMC_UID"))
                .andExpect(jsonPath("$.status").value("COMPLETED"))
                .andExpect(jsonPath("$.printFile").value("test_print.TEST_EMC_UID"));
    }

    @Test
    void testAnalyzeEmc_Error() throws Exception {
        // Setup
        EmcAnalysisRequest request = new EmcAnalysisRequest();
        request.setPrinterId("printer1");
        request.setPrintFile("test_print");
        request.setEmcUid("TEST_EMC_UID");
        request.setInteractive(false);
        
        EmcAnalysisResult result = new EmcAnalysisResult();
        result.setEmcUid("TEST_EMC_UID");
        result.setStatus("ERROR");
        result.setMessage("Analysis failed due to invalid input");
        
        when(emcAnalysisService.performAnalysis(any(EmcAnalysisRequest.class))).thenReturn(result);
        
        // Execute and Verify
        mockMvc.perform(post("/api/emc/analyze")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.emcUid").value("TEST_EMC_UID"))
                .andExpect(jsonPath("$.status").value("ERROR"))
                .andExpect(jsonPath("$.message").value("Analysis failed due to invalid input"));
    }

    @Test
    void testGetEmcStatus() throws Exception {
        // Setup
        String emcUid = "TEST_EMC_UID";
        
        EmcAnalysisResult result = new EmcAnalysisResult();
        result.setEmcUid(emcUid);
        result.setStatus("UNKNOWN");
        result.setMessage("Status retrieval not implemented");
        
        // Execute and Verify
        mockMvc.perform(get("/api/emc/status/{emcUid}", emcUid))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.emcUid").value(emcUid))
                .andExpect(jsonPath("$.status").value("UNKNOWN"))
                .andExpect(jsonPath("$.message").value("Status retrieval not implemented"));
    }
}
