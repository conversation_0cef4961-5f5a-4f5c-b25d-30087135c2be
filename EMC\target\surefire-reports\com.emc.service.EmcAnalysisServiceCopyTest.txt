-------------------------------------------------------------------------------
Test set: com.emc.service.EmcAnalysisServiceCopyTest
-------------------------------------------------------------------------------
Tests run: 1, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 0.026 s <<< FAILURE! - in com.emc.service.EmcAnalysisServiceCopyTest
testCopyExistToProp  Time elapsed: 0.025 s  <<< ERROR!
org.mockito.exceptions.base.MockitoException: 

Cannot instantiate @InjectMocks field named 'emcAnalysisService'! Cause: the type 'EmcAnalysisService' is an interface.
You haven't provided the instance at field declaration so I tried to construct the instance.
Examples of correct usage of @InjectMocks:
   @InjectMocks Service service = new Service();
   @InjectMocks Service service;
   //and... don't forget about some @Mocks for injection :)


