struct  propose_tag
{
        char    sys_category;
        char    sys_type[3];
        char    sys_no[8];
        char    sys_suffix[4];
        int     stn_node;               /* position in 'exist' array     */
        int     east_grid;
        int     north_grid;
        char    sub_district[4];        /* sub-district code             */
        char    station_type;
        float   desen_att_db;           /* desensit attenuation level    */
        float   intmod_att_db;          /* intermod attenuation level    */
        char    antenna[3];             /* antenna id.                   */
        int     ant_height;	        /* height above local terrain    */
				        /* (height of antenna inclusive) */
        float   pw_dbw;                 /* effective rated power         */
        int     az_max_rad;             /* azimuth of max. radiation     */
        float   az_max_rad_r;           /* azimuth of max. radiation     */                                             /* in radian                     */
        int     feed_loss;              /* obtained from table 'add_eq'  */
	char    sfx_filter[11];         /* SFX filter type               */
        double  tx_freq[10];
        double  rx_freq[10];
        int     tx_channel;
        int     rx_channel;
	/*****************************************************************/
	/*     the above declarations are the record specification of    */
	/*     the EMC batch file                                        */
	/*****************************************************************/
	float   height_asl; 	/* height above see level of local       */
				/* terrain                               */
        char    mode;           /* mode of transmission                  */
        int     band;           /* band category, eg., VHF, UHF          */
        char    dist_type;      /* sub-district type code 'B', 'H', 'O'  */
        int     noise_code;     /* 1 : busy,  2 : average,  3 : low      */
        int     dist_index;     /* pointer to subdist array              */
};

typedef struct propose_tag  PROPOSE;
