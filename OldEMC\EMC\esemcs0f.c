
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,0,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[12];
};
static const struct sqlcxp sqlfpn =
{
    11,
    "esemcs0f.pc"
};


static unsigned int sqlctx = 149355;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[13];
   unsigned long  sqhstl[13];
            int   sqhsts[13];
            short *sqindv[13];
            int   sqinds[13];
   unsigned long  sqharm[13];
   unsigned long  *sqharc[13];
   unsigned short  sqadto[13];
   unsigned short  sqtdso[13];
} sqlstm = {13,13};

/* SQLLIB Prototypes */
extern void sqlcxt (void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlcx2t(void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlbuft(void **, char *);
extern void sqlgs2t(void **, char *);
extern void sqlorat(void **, unsigned int *, void *);

/* Forms Interface */
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern void sqliem(unsigned char *, signed int *);

 static const char *sq0002 = 
"select SYS_CATEGORY ,SYS_TYPE ,SYS_NO ,SYS_SUFFIX ,BASE_NO ,STATION_TYPE ,EA\
ST ,NORTH ,NVL(PW_SIGN,' ') ,NVL(PW_DBW,0.0) ,NVL(TO_CHAR(CANCEL_DATE),'-') ,G\
AIN ,HALT  from STATION where SUBDISTRICT=:b0 order by LIC_TYPE,LIC_NO        \
    ";

 static const char *sq0003 = 
"select NVL(TX_FREQ,0.0) ,NVL(RX_FREQ,0.0) ,UPPER(ACT_FLAG)  from BASE_CH whe\
re ((((SYS_CATEGORY=:b0 and SYS_TYPE=:b1) and SYS_NO=:b2) and SYS_SUFFIX=:b3) \
and BASE_NO=:b4) order by TX_FREQ            ";

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4242,1,0,0,
5,0,0,1,73,0,4,997,0,0,2,1,0,1,0,2,1,0,0,1,9,0,0,
28,0,0,2,236,0,9,1071,0,0,1,1,0,1,0,1,9,0,0,
47,0,0,2,0,0,13,1073,0,0,13,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,
1,0,0,2,3,0,0,2,3,0,0,2,1,0,0,2,4,0,0,2,9,0,0,2,3,0,0,2,3,0,0,
114,0,0,2,0,0,15,1084,0,0,0,0,0,1,0,
129,0,0,2,0,0,13,1101,0,0,13,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,
1,0,0,2,3,0,0,2,3,0,0,2,1,0,0,2,4,0,0,2,9,0,0,2,3,0,0,2,3,0,0,
196,0,0,2,0,0,15,1110,0,0,0,0,0,1,0,
211,0,0,3,199,0,9,1138,0,0,5,5,0,1,0,1,1,0,0,1,9,0,0,1,9,0,0,1,9,0,0,1,3,0,0,
246,0,0,3,0,0,13,1140,0,0,3,0,0,1,0,2,4,0,0,2,4,0,0,2,1,0,0,
273,0,0,3,0,0,15,1150,0,0,0,0,0,1,0,
288,0,0,2,0,0,15,1151,0,0,0,0,0,1,0,
303,0,0,2,0,0,15,1185,0,0,0,0,0,1,0,
318,0,0,3,0,0,15,1186,0,0,0,0,0,1,0,
333,0,0,3,0,0,13,1190,0,0,2,0,0,1,0,2,4,0,0,2,4,0,0,
356,0,0,3,0,0,15,1196,0,0,0,0,0,1,0,
371,0,0,2,0,0,13,1223,0,0,13,0,0,1,0,2,1,0,0,2,9,0,0,2,9,0,0,2,9,0,0,2,3,0,0,2,
1,0,0,2,3,0,0,2,3,0,0,2,1,0,0,2,4,0,0,2,9,0,0,2,3,0,0,2,3,0,0,
438,0,0,2,0,0,15,1234,0,0,0,0,0,1,0,
};


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  main (esemcs0f.pc)                             */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  esemcn0f.c through 'system' statement          */  
/*                                                                    */
/*    Parameters    :  user id.                                       */
/*                     user password                                  */
/*                     EMC user id.                                   */
/*                     printer id.                                    */
/*                                                                    */
/*    Called Modules:  screen functions from 'screen.c'               */
/*                     (centre_msg, disp_err, disp_space)             */
/*                     screen function from 'cursesX'                 */
/*                     user_login and user_logout (login.pc)          */
/*                     intermod_2 (esemim0r.c)                        */
/*                     intermod_3 (esemim0r.c)                        */
/*                     sub_district_stn (esemsl0r.pc)                 */
/*                                                                    */
/*    Purpose       :  Accept user input co-site analysis data,       */
/*                     validate the input data, then perform the      */
/*                     user-chosen analysis                           */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <time.h>
#include <curses.h>
#include "../include/define.h"
#include "../include/global.h"
#include "../include/emc.h"
#include "../include/exist.h"
#include "../include/ref.h"
#include "../include/propose.h"
#include <unistd.h>
#include <ctype.h>

#define  COSITE 1

#define  MAX_FLD_LEN  12              /* max. field length of input field */

#define  LOGIN_OK     0

#define  NEXT_START_PT    5          /* field index at which user starts his */
                                     /* input for next co-site analysis      */

#define  COSITE_OPTION    6

#define  HILL_TOP_SITE    'H'

#define  FREQ_MULTIPLE    10000
#define  CHANNEL_SEP      125          /* in the order of 100Hz */
#define  MAX_FREQ         99999.9875   /* in the order of MHz   */
#define  MAX_CULL_FREQ    99.9875      /* in the order of MHz   */

#define  REPRINT_ERROR    "Report not found, press any key to continue"

#include "../include/screen.h"




int user_login    (char *,char *,char *);     						/*20170704 Cyrus [Add] */
int strip_blank   (char *,char *); 									/*20170704 Cyrus [Add] */
int get_sys_date_time(char *,char *,char *,char *); 				/*20170704 Cyrus [Add] */
int disp_heading (char *,char *,char *);							/*20170707 Cyrus Add */
void disp_entry_scn(int,int,int);									/*20170707 Cyrus Add */
int clear_err();													/*20170707 Cyrus Add */
void refresh_screen(int,int);										/*20170707 Cyrus Add */
void init_field(FIELD *, int *);									/*20170707 Cyrus Add */
void disp_err(char *);												/*20170707 Cyrus Add */
void disp_space(int,int,int);										/*20170707 Cyrus Add */
void hide_cursor();													/*20170707 Cyrus Add */
int get_subdist_stn(char *);



/* field validation function declarations */
int    chk_freq();
int    chk_cull_freq();
int    chk_sub_dist();
int    chk_cs_option();

/* For field definitions, see ../include/screen.h */
FIELD item[] = 
{
	{5, 23,FREQ,       TRUE, 11,5, NEW, "00000.00000","",0,FALSE,chk_freq},
	{6, 23,FREQ,       TRUE, 11,5, NEW, "00000.00000","",0,FALSE,chk_freq},
	{7, 23,STRING,     TRUE, 3, 0, NEW, "   ",        "",0,FALSE,chk_sub_dist},
	{6, 68,FREQ,       FALSE,8, 5, NEW, "3.0     ",   "3.0",0,FALSE,chk_cull_freq},
	{7, 68,FREQ,       FALSE,8, 5, NEW, "3.0     ",   "3.0",0,FALSE,chk_cull_freq},
	{19,32,INTEGER,    TRUE, 1, 0, NEW, " ",          "",0,FALSE,chk_cs_option},
	{-1,-1,DUMMY,      TRUE, 0, 0, NEW, "",           "",0,FALSE,(int(*)())NULL}
};

/*
char   *item_err_msg[] = 
{
   "Freq. not in multiple of min. channel separation",
   "Freq. not in multiple of min. channel separation",
   "Invalid sub-district code or not hill-top site",
   "Invalid option",
   ""
};
*/
     
char   *prog_id = "emcs0f_01";
char   *screen_head = "CO-SITE ANALYSIS FOR HILL-TOP SITE";

char   passwd[20];
char   emc_uid[20];
char   sub_district[4];

double prop_tx_freq, prop_rx_freq;
int    tx_mode, freq_band;

/*****************************************************/
/*           Variables for summary totals            */
/*****************************************************/
int    intmod2_vict_tot;
int    intmod2_tx1_tot;
int    intmod2_tx2_tot;
int    intmod3_vict_tot;
int    intmod3_tx1_tot;
int    intmod3_tx3_tot;

char            *getenv();
extern double   atof();

extern char msg[];

/* EXEC SQL BEGIN DECLARE SECTION; */ 


    char     o_dist_type;
    char     o_station_type;
    char     o_act_flag;
    double   o_tx_freq;
    double   o_rx_freq;
    char     o_pw_sign;
    float    o_pw_dbw;
    int      o_base_no;
    int      o_east;
    int      o_north;
    int      o_ant_gain;
    int      o_ant_height;
    char     o_sys_category;
    /* VARCHAR  o_sys_type[3]; */ 
struct { unsigned short len; unsigned char arr[3]; } o_sys_type;

    /* VARCHAR  o_sys_no[8]; */ 
struct { unsigned short len; unsigned char arr[8]; } o_sys_no;

    /* VARCHAR  o_sys_suffix[4]; */ 
struct { unsigned short len; unsigned char arr[4]; } o_sys_suffix;

    /* VARCHAR  o_subdistrict[4]; */ 
struct { unsigned short len; unsigned char arr[4]; } o_subdistrict;

    /* VARCHAR  o_cancel_date[10]; */ 
struct { unsigned short len; unsigned char arr[10]; } o_cancel_date;


/* EXEC SQL END DECLARE SECTION; */ 


/* EXEC SQL INCLUDE SQLCA;
 */ 
/*
 * $Header: sqlca.h 24-apr-2003.12:50:58 mkandarp Exp $ sqlca.h 
 */

/* Copyright (c) 1985, 2003, Oracle Corporation.  All rights reserved.  */
 
/*
NAME
  SQLCA : SQL Communications Area.
FUNCTION
  Contains no code. Oracle fills in the SQLCA with status info
  during the execution of a SQL stmt.
NOTES
  **************************************************************
  ***                                                        ***
  *** This file is SOSD.  Porters must change the data types ***
  *** appropriately on their platform.  See notes/pcport.doc ***
  *** for more information.                                  ***
  ***                                                        ***
  **************************************************************

  If the symbol SQLCA_STORAGE_CLASS is defined, then the SQLCA
  will be defined to have this storage class. For example:
 
    #define SQLCA_STORAGE_CLASS extern
 
  will define the SQLCA as an extern.
 
  If the symbol SQLCA_INIT is defined, then the SQLCA will be
  statically initialized. Although this is not necessary in order
  to use the SQLCA, it is a good pgming practice not to have
  unitialized variables. However, some C compilers/OS's don't
  allow automatic variables to be init'd in this manner. Therefore,
  if you are INCLUDE'ing the SQLCA in a place where it would be
  an automatic AND your C compiler/OS doesn't allow this style
  of initialization, then SQLCA_INIT should be left undefined --
  all others can define SQLCA_INIT if they wish.

  If the symbol SQLCA_NONE is defined, then the SQLCA variable will
  not be defined at all.  The symbol SQLCA_NONE should not be defined
  in source modules that have embedded SQL.  However, source modules
  that have no embedded SQL, but need to manipulate a sqlca struct
  passed in as a parameter, can set the SQLCA_NONE symbol to avoid
  creation of an extraneous sqlca variable.
 
MODIFIED
    lvbcheng   07/31/98 -  long to int
    jbasu      12/12/94 -  Bug 217878: note this is an SOSD file
    losborne   08/11/92 -  No sqlca var if SQLCA_NONE macro set 
  Clare      12/06/84 - Ch SQLCA to not be an extern.
  Clare      10/21/85 - Add initialization.
  Bradbury   01/05/86 - Only initialize when SQLCA_INIT set
  Clare      06/12/86 - Add SQLCA_STORAGE_CLASS option.
*/
 
#ifndef SQLCA
#define SQLCA 1
 
struct   sqlca
         {
         /* ub1 */ char    sqlcaid[8];
         /* b4  */ int     sqlabc;
         /* b4  */ int     sqlcode;
         struct
           {
           /* ub2 */ unsigned short sqlerrml;
           /* ub1 */ char           sqlerrmc[70];
           } sqlerrm;
         /* ub1 */ char    sqlerrp[8];
         /* b4  */ int     sqlerrd[6];
         /* ub1 */ char    sqlwarn[8];
         /* ub1 */ char    sqlext[8];
         };

#ifndef SQLCA_NONE 
#ifdef   SQLCA_STORAGE_CLASS
SQLCA_STORAGE_CLASS struct sqlca sqlca
#else
         struct sqlca sqlca
#endif
 
#ifdef  SQLCA_INIT
         = {
         {'S', 'Q', 'L', 'C', 'A', ' ', ' ', ' '},
         sizeof(struct sqlca),
         0,
         { 0, {0}},
         {'N', 'O', 'T', ' ', 'S', 'E', 'T', ' '},
         {0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0}
         }
#endif
         ;
#endif
 
#endif
 
/* end SQLCA */



int main(argc, argv)
int    argc;
char   **argv;
{
    char    cs_fname[150];
    char    err_msg[80];
    int     term_code;
    int     item_cnt;                   /* total no. of items               */
    int     last_item;                  /* index of last filled-up item     */
    int     x_pos, y_pos;               /* current cursor position          */
    int     curr_pos;                   /* current position of input string */
    int     loop;                       /* TRUE if entry continue on        */
                                        /* current item                     */
    int     modified = FALSE;           /* TRUE when current item has been  */
                                        /* modified                         */
    int     token;                      /* user-input character             */
    int     status = !(QUIT);
    int     direction;                  /* field shuttle direction          */
    int     err_flag = FALSE;
    int     first_select = TRUE;        /* TRUE when cosite data is freshly */
                                        /* input                            */
    int     option;
    int     i;
    register int     j;
    struct tm  *tt;
char s[80];


    /* EXEC SQL WHENEVER NOT FOUND CONTINUE; */ 


    initscr();
    raw();
    noecho();
    clear();
    keypad(stdscr, TRUE); 

    if (argc != 6)
    {
       sprintf(err_msg, "Usage: esemcs0f user_id passwd emc_uid -P printer_id");
       goto force_exit;
    }

    if (strcmp(argv[4], "-P"))
    {
       sprintf(err_msg, "Usage: esemcs0f user_id passwd emc_uid -P printer_id");
       goto force_exit;
    }
    
    strcpy(user_id, argv[1]);
    strcpy(passwd, argv[2]);

    if (user_login(user_id, passwd, err_msg) != LOGIN_OK)
    {
        strip_blank(err_msg, TRAIL);
        strcat(err_msg, "  ");
        strcat(err_msg, user_id);
        strcat(err_msg, passwd);
        goto force_exit;
    }

    /* EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn; */ 


    strcpy(emc_uid, argv[3]);
    strcpy(printer_id, argv[5]);
    get_sys_date_time(sys_date, sys_time, yymmdd, hhmmss);

    for (i = 0; item[i].xpos != -1; i++)
        ;

    item_cnt = i;

    emc_dir = getenv("EMC");
    
    disp_heading(prog_id, screen_head, sys_date);
    disp_entry_scn(item_cnt, item_cnt, NEW_SCREEN);
 
    for (i = 0; status != QUIT; )
    {
        x_pos = item[i].xpos; 
        y_pos = item[i].ypos;

        /**********************************************************/
        /* if data type of current field is sign field, then skip */
        /* the 1st position of current string and increment       */
        /* screen x-coordinate by 1, else keep 1st position       */
        /**********************************************************/
        if (is_sign(&item[i]))
        {
            curr_pos = 1;
            x_pos++;
        }
        else
            curr_pos = 0;
        move(y_pos, x_pos);
        loop = TRUE;

        for (; loop == TRUE;)
        {
            refresh();
            token = getch();

            if (err_flag)
            {
                err_flag = FALSE;
                attrset(A_NORMAL);
                getyx(stdscr, y_pos, x_pos);
                clear_err();
                move(y_pos, x_pos);
                attrset(A_REVERSE);
            }

            /**************************************/
            /* Function key definitions:          */
            /*    KEY_F(1)  - quit session        */
            /*    KEY_F(3)  - refresh screen      */
            /*    KEY_F(4)  - clear field         */
            /*    TAB,                            */
            /*    NEWLINE,                        */
            /*    KEY_DOWN  - next field          */
            /*    CTRL_B,                         */
            /*    KEY_UP    - next field          */
            /*    KEY_LEFT  - next char position  */
            /*    KEY_RIGHT - next char position  */
            /*    DELETE,                         */
            /*    BACKSPACE - delete current char */
            /*                and back 1 position */
            /**************************************/

            switch (token)
            {
                case KEY_F(1):
                    status = QUIT;
                    loop = FALSE;
                    break;
/*
                case KEY_F(2):
                    confirm = TRUE;
                    loop = FALSE;
                    break;
*/
                case KEY_F(3):
                    attroff(A_REVERSE);
                    refresh_screen(item_cnt, i);
                    move(y_pos, x_pos);
                    break; 

                case KEY_F(4):
                    init_field(&item[i], &curr_pos);
                    loop = FALSE; 
                    direction = RESTART; 
                    break; 

                case KEY_UP:
                case CTRL_B:
                    if (i == 0)
                        beep();
                    else
                    {
                        loop = FALSE;
                        direction = BACKWARD;
                    }
                    break;

                case KEY_DOWN:
                case NEWLINE:
                case TAB:
/*
sprintf(s," i len req: %d %d %d", i, item[i].curr_len, item[i].required);
mvaddstr(23,0,s);
                    if ((item[i].state == NEW) && (item[i].required))
                    if ((is_sign(&item[i]) && (item[i].curr_len == 1))
                     || (!is_sign(&item[i]) && empty(&item[i]))
                    && (item[i].required))
*/
                    /*********************************************************/
                    /* beep user if he wants to skip an input-required field */
                    /*********************************************************/
                    if (is_sign(&item[i]))
                    {
                        if ((item[i].curr_len == 1) && (item[i].required))
                        {
                            err_flag = TRUE;
                            disp_err(INPUT_REQ_MSG);
                            attron(A_REVERSE);
                            break;
                        }
                    }
                    else
                        if (empty(&item[i]) && (item[i].required))
                        {
                            err_flag = TRUE;
                            disp_err(INPUT_REQ_MSG);
                            attron(A_REVERSE);
                            break;
                        }

                    if (empty(&item[i]) 
                    ||  (is_sign(&item[i]) && (item[i].curr_len == 1)))
                        strcpy(item[i].curr_str, item[i].init_str);

                    loop = FALSE;
                    direction = FORWARD;
                    break;

                case KEY_LEFT:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                    }
                    break;

                case KEY_RIGHT:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* right boundary of current field  */
                    /************************************/
                    if (x_pos - item[i].xpos == item[i].curr_len)
                        beep();
                    else
                    {
                        x_pos++; curr_pos++;
                    }
                    break;

                case BACKSPACE:
                case DELETE:

                    /************************************/
                    /* beep user if he has reached the  */
                    /* left-most position of a field    */
                    /************************************/
                    if (is_sign(&item[i]))
                        if (x_pos == item[i].xpos + 1)
                        {
                            beep();
                            break;
                        }

                    if (x_pos == item[i].xpos)
                        beep();
                    else
                    {
                        x_pos--; curr_pos--;
                        if (is_float(&item[i]) 
                        && (item[i].curr_str[curr_pos] == DOT))
                            item[i].has_dot = FALSE;
                        move(y_pos, x_pos);
                        addch(BLANK);
                        modified = TRUE;
                    }
                    break;

                case DOT:

                    /******************************************************/
                    /* beep user if data type of current field is integer */
                    /******************************************************/
                    if (is_int(&item[i]))
                    {
                        beep();
                        break;
                    }


                    /********************************************************/
                    /* beep user if data type of current field is float and */
                    /* user has already input one dot                       */
                    /********************************************************/
                    if (is_float(&item[i]))
                        if (item[i].has_dot)
                        {
                            beep();
                            break;
                        }
                        else
                            item[i].has_dot = TRUE;

                    if (item[i].state == NEW)
                        if (is_sign(&item[i]))
                        {
                            if (curr_pos == 1)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len - 1);
                                move(y_pos, x_pos);
                            }
                        }
                        else
                            if (curr_pos == 0)
                            {
                                disp_space(y_pos, x_pos, item[i].fld_len);
                                move(y_pos, x_pos);
                            }

                    addch(token);
                    item[i].curr_str[curr_pos] = token;
                    x_pos++; curr_pos++;
                    modified = TRUE;
                    item[i].state = MODIFIED;

                    if (x_pos - item[i].xpos == item[i].fld_len)
                    {
                        item[i].curr_str[item[i].fld_len] = '\0';
                        item[i].curr_len = item[i].fld_len;
                        loop = FALSE;
                        direction = FORWARD;
                    }

                    break;

                default:
                    if ((!isalnum(token)) && (token != MINUS) 
                    &&  (token != PLUS))
                    {
                        beep();
                        break;
                    }

                    if (!isdigit(token))
                    {
/*
mvaddstr(23, 0, "not digit");
*/
                        if (is_sign(&item[i]))
                        {
                            if ((token != MINUS) && (token != PLUS))
                            {
                                beep();
                                break;
                            }
                            else
                                if (curr_pos > 1)
                                {
                                    beep();
                                    break;
                                }
                        }
                        else
/*
sprintf(s, "should not be integer: %d %d %s", i, item[i].type, item[i].init_str);
mvaddstr(22, 0, s);
*/
                            if ((item[i].type != CHAR) 
                            &&  (item[i].type != STRING))
                            {
                                beep();
                                break;
                            }
                            else
                                if (isalpha(token))
                                    token = toupper(token); 
                    }
                    else
                        if (item[i].state == NEW)
                            if (is_sign(&item[i]))
                            {
                                if (curr_pos == 1)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len-1);
                                    move(y_pos, x_pos);
                                }
                            }
                            else
                                if (curr_pos == 0)
                                {
                                    disp_space(y_pos, x_pos, item[i].fld_len);
                                    move(y_pos, x_pos);
                                }

                    if (((token == MINUS) || (token == PLUS)) 
                    &&  is_sign(&item[i]))
                    {
                        x_pos--; curr_pos--;  /* because we don't want to  */
                                              /* move cursor to 1 position */
                                              /* this statement is used    */
                                              /* to complement the         */
                                              /* 'x_pos++; curr_pos++'     */
                                              /* a few lines below         */
                        if (token == MINUS)
                        {
                            item[i].curr_str[0] = MINUS;
                            move(y_pos, x_pos);
                            addch(MINUS);
                        }
                            
                    }
                    else
                    {
                        item[i].curr_str[curr_pos] = token;
                        addch(token);
                    }

/*
sprintf(s, "modified");
mvaddstr(22, 0, s);
*/
                    if ((token != MINUS) && (token != PLUS))
                        item[i].state = MODIFIED;

                    modified = TRUE;
                    x_pos++; curr_pos++;

                    if (x_pos - item[i].xpos == item[i].fld_len)
                    {
                        item[i].curr_str[item[i].fld_len] = '\0';
                        item[i].curr_len = item[i].fld_len;
                        loop = FALSE;
                        direction = FORWARD;
                    }
                
                    break;
            }

            refresh();
            if (loop == FALSE)
            {
                int    (*check_item)();

                check_item = item[i].validate;
                if (check_item != (int(*)())NULL)
                {
                    if ((direction != BACKWARD) && (!empty(&item[i])))
                        if ((*check_item)(&i, err_msg) == ERROR)
                        {
                            err_flag = TRUE;
                            disp_err(err_msg);
                            attron(A_REVERSE);
                            loop = FALSE;
                            direction = RESTART;
                        }
                }

                switch (direction)
                {
                    case FORWARD:
                        i++;
                        break;
                    case BACKWARD:
                        i--;
                        break;
                    case RESTART:
                        break;
                }

                continue;
            }
            
            if (modified && (!err_flag))
            {
                int    len = x_pos - item[i].xpos;
   
                if ((item[i].curr_len < len) || is_delete(token))
                {
                    item[i].curr_len = len;
                    item[i].curr_str[item[i].curr_len] = '\0';
                }
/*
disp_space(22,0,80);
sprintf(s,"i mode: %d %d", i, tx_mode);
mvaddstr(22,0,s);
*/

                modified = FALSE;
            }
  
            move(y_pos, x_pos);
        }

        if ((i == item_cnt) && (!err_flag))
        {
            option = atoi(item[i-1].curr_str);
            if (option == 0)
            {
                i = 0;
                for (j = 0; j < item_cnt; j++)
                    init_field(&item[j], &curr_pos);
                first_select = TRUE;
            }
            else
            {
                i = NEXT_START_PT;
                hide_cursor();
                if (first_select)
                {
                    prop_tx_freq = atof(item[0].curr_str);
                    prop_rx_freq = atof(item[1].curr_str);
                    strcpy(sub_district,item[2].curr_str);
                }
                switch (option)
                {
                    case 1 :
                    case 2 :

                        /**********************************************/
                        /* select sub-district stations only when the */
                        /* co-site data is freshly input              */
                        /**********************************************/
                        if (first_select)
                        {
                            /*if (get_subdist_stn(item[2].curr_str, err_msg)   20170711 Cyrus remove  item[2].curr_str*/
							if (get_subdist_stn(err_msg)								
                                == ERROR)
                                goto force_exit;
                            first_select = FALSE;
                        }

                        if (option == 1)
                        {
                            intermod2_cull_freq = atof(item[3].curr_str);
                            intermod3_cull_freq = atof(item[4].curr_str);
                            intmod2_vict_tot = 0;
                            intmod2_tx1_tot = 0;
                            intmod2_tx2_tot = 0;
                            intmod3_vict_tot = 0;
                            intmod3_tx1_tot = 0;
                            intmod3_tx3_tot = 0;
                            set_band_mode2(&tx_mode,
                                           &freq_band,
                                           prop_tx_freq,
                                           prop_rx_freq);
                            centre_msg(23, "2-signal intermod ...  ",
                                       A_BLINK, A_REVERSE);
                            intermod_2();
                            centre_msg(23, "3-signal intermod ...  ",
                                       A_BLINK, A_REVERSE);
                            intermod_3();
                            if (print_intermod_summary(err_msg) == ERROR)
                                goto force_exit;
                        }
                        else
                        {
                            int    status;

                            centre_msg(23, " Generating report ... ",
                                       A_BLINK, A_REVERSE);
                            status = sub_district_stn(err_msg);
                            if (status == ERROR)
                                goto force_exit;
                            if (status == NOT_FOUND)
                            {
                                disp_err(err_msg);
                                getch();
                            }
                        }
                        break;

                    case 3 :
                        if (reprint_substn_rpt() == ERROR)
                        {
                            disp_err(REPRINT_ERROR);
                            getch();
                        }
                        break;

                    case 4 :
                        if (reprint_intermod2_rpt() == ERROR)
                        {
                            disp_err(REPRINT_ERROR);
                            getch();
                        }
                        break;

                    case 5 :
                        if (reprint_intermod3_rpt() == ERROR)
                        {
                            disp_err(REPRINT_ERROR);
                            getch();
                        }
                        break;

                    case 6 :
                        if (reprint_intermod_summary() == ERROR)
                        {
                            disp_err(REPRINT_ERROR);
                            getch();
                        }
                        break;

                }

                show_cursor();
                attrset(A_NORMAL);
                disp_space(23, 0, 80);
                refresh();
                attrset(A_REVERSE);
                
                for (j = NEXT_START_PT; j < item_cnt; j++)
                    init_field(&item[j], &curr_pos);
                beep();
            }
        }
    }

    attroff(A_BOLD);
    clear();
    endwin();
    user_logout();
    exit(0);


sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);

force_exit:
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    getch(); clear(); show_cursor(); refresh();
    endwin();
    exit(1);
}


/**************************************************************/
/* Display new entry screen or refresh current screen to user */
/**************************************************************/

disp_entry_scn(item_cnt, curr_cnt, mode)
int    item_cnt;
int    curr_cnt;
int    mode;
{
    char    tmp_str[80];
    register int    i;

    attrset(A_NORMAL);
    mvaddstr(5,  7,  "TX FREQ (MHz) :");
    mvaddstr(6,  7,  "RX FREQ (MHz) :");
    mvaddstr(7,  7,  "HILL-TOP CODE :");
    attrset(A_UNDERLINE);
    mvaddstr(4,  42, "CULLING FREQUENCY LIMITS");
    attrset(A_NORMAL);
    mvaddstr(6,  42, "2-SIGNAL INTERMOD (MHz) :");
    mvaddstr(7,  42, "3-SIGNAL INTERMOD (MHz) :");
    mvaddstr(10, 15, "0.   ANOTHER SET OF CO-SITE DATA");
    mvaddstr(12, 15, "1.   COMBINED SIGNALS INTERMODULATION ANALYSIS");
    mvaddstr(13, 15, "2.   SUB-DISTRICT STATION REPORTING"); 
    mvaddstr(14, 15, "3.   SUB-DISTRICT STATION REPORT REPRINTING"); 
    mvaddstr(15, 15, "4.   2-SIGNAL INTERMODULATION REPORT REPRINTING");
    mvaddstr(16, 15, "5.   3-SIGNAL INTERMODULATION REPORT REPRINTING");
    mvaddstr(17, 15, "6.   COMBINED SIGNALS INTERMODULATION SUMMARY REPRINTING");
    mvaddstr(19, 15, "YOUR SELECTION : ");
 
    sprintf(tmp_str, "%s",
"F1-Quit   F3-Refresh   F4-Clear field   UP-Prev   DOWN/<Tab>-next");
 /*
    sprintf(tmp_str, "%s",
"F1-Quit   F2-Confirm   F3-Refresh   F4-Clear field   UP-Prev   DOWN/<Tab>-next");
*/
    centre_msg(21, tmp_str, A_BOLD, A_REVERSE);

    if (mode == REFRESH)
    {
        for (i = 0; i < curr_cnt; i++)
        {
            disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
            mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
        }
        for (i = curr_cnt; i < item_cnt; i++)
            if (item[i].state == NEW)
                mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);
            else
            {
                disp_space(item[i].ypos, item[i].xpos, item[i].fld_len);
                mvaddstr(item[i].ypos, item[i].xpos, item[i].curr_str);
            }
    }
    else
        for (i = 0; i < item_cnt; i++)
            mvaddstr(item[i].ypos, item[i].xpos, item[i].init_str);

    refresh();

}


/**************************************************************/
/* call 'disp_entry_scn' to refresh current screen to user    */
/**************************************************************/

refresh_screen(item_cnt, curr_cnt)
int item_cnt;
int curr_cnt;
{
    clear();
    disp_heading(prog_id, screen_head, sys_date);
    disp_entry_scn(item_cnt, curr_cnt, REFRESH);
}


/*************************************************/
/* initialise current field to its initial value */
/*************************************************/

init_field(p_item, curr_pos)
FIELD *p_item;
int   *curr_pos;
{
    mvaddstr(p_item->ypos, p_item->xpos, p_item->init_str);
    if (is_sign(p_item))
    {
        p_item->curr_len = *curr_pos = 1;
        p_item->curr_str[1] = '\0';
    }
    else
    {
        p_item->curr_len = *curr_pos = 0;
        p_item->curr_str[0] = '\0';
    }

    if (!disp_only(p_item))
    {
        p_item->state = NEW;
        if (is_float(p_item))
            p_item->has_dot = FALSE;
    }
}


/**************************************************************/
/* check input frequency                                      */
/**************************************************************/

int    chk_freq(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
    double  freq;
    long    i_freq;

    freq = atof(item[*curr_cnt].curr_str);
    if ((freq - MAX_FREQ) > FREQ_EPSILON)
    {
        sprintf(err_msg, "Frequency must be between 0 and %-11.5lf", MAX_FREQ);
        return ERROR;
    }

    freq = freq * FREQ_MULTIPLE;
    i_freq = (long)(freq + .5);

    if (abs(freq - i_freq) > FREQ_EPSILON)
    {
        sprintf(err_msg, 
                "Frequency not in multiple of min. channel separation");
        return ERROR;
    }

    if (i_freq % CHANNEL_SEP)
    {
        sprintf(err_msg, 
                "Frequency not in multiple of min. channel separation");
        return ERROR;
    }

    return OK;
}


/**************************************************************/
/* check input culling frequency                              */
/**************************************************************/

int    chk_cull_freq(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
    double  freq;
    long    i_freq;

    freq = atof(item[*curr_cnt].curr_str);
    if ((freq - MAX_CULL_FREQ) > FREQ_EPSILON)
    {
        sprintf(err_msg, "Frequency must be between 0 and %-8.5lf",
                MAX_CULL_FREQ);
        return ERROR;
    }

    freq = freq * FREQ_MULTIPLE;
    i_freq = (long)(freq + .5);

    if (abs(freq - i_freq) > FREQ_EPSILON)
    {
        sprintf(err_msg, 
                "Frequency not in multiple of min. channel separation");
        return ERROR;
    }

    if (i_freq % CHANNEL_SEP)
    {
        sprintf(err_msg, 
                "Frequency not in multiple of min. channel separation");
        return ERROR;
    }

    return OK;
}


/**********************************************************************/
/* check that input sub-district code can be found in SUBDISTRICT_TAB */
/**********************************************************************/

int    chk_sub_dist(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
/*    strcpy(o_subdistrict.arr, item[*curr_cnt].curr_str);
    o_subdistrict.len = strlen(o_subdistrict.arr); */
    strcpy((char *)o_subdistrict.arr, item[*curr_cnt].curr_str);
    o_subdistrict.len = strlen((char *)o_subdistrict.arr);
    
/*
system("echo \"esemcs0f 1\" >> /tmp/debug");
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_subdistrict.arr );
system(msg);
*/

    /* EXEC SQL
         SELECT DISTRICT_TYPE
         INTO   :o_dist_type
         FROM   SUBDISTRICT_TAB
         WHERE  SUBDISTRICT = :o_subdistrict; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 2;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select DISTRICT_TYPE into :b0  from SUBDISTRICT_TAB where\
 SUBDISTRICT=:b1";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )5;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_dist_type;
    sqlstm.sqhstl[0] = (unsigned long )1;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_subdistrict;
    sqlstm.sqhstl[1] = (unsigned long )6;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "Invalid sub-district code or not hill-top site");
        return ERROR;
    }

    if (o_dist_type != HILL_TOP_SITE)
    {
        sprintf(err_msg, "Invalid sub-district code or not hill-top site");
        return ERROR;
    }

    return OK;

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh(); beep();
    sleep(1); clear(); refresh();
    endwin();
    exit(1);
}


/********************************************************************/
/* check that input co-site analysis option must be < COSITE_OPTION */
/********************************************************************/

int    chk_cs_option(curr_cnt, err_msg)
int    *curr_cnt;
char   *err_msg;
{
    int    option;

    option = atoi(item[*curr_cnt].curr_str);
    if (option > COSITE_OPTION)
    {
        sprintf(err_msg, "Invalid option");
        return ERROR;
    }

    return OK;
}

    
/****************************************************************/
/* select from STATION stations that have the same sub-district */
/* code as the user-input sub-district code                     */
/****************************************************************/

int get_subdist_stn(err_msg)
char   *err_msg;
{
    register int   i, j, gap;
    EXIST_FREQ     *tmp_list;

char s[80];

    centre_msg(23, "Selecting stations ...", A_BLINK, A_REVERSE);
    /* EXEC SQL DECLARE C02 CURSOR FOR
         SELECT SYS_CATEGORY, SYS_TYPE, SYS_NO, SYS_SUFFIX, BASE_NO,
                STATION_TYPE, EAST, NORTH, NVL(PW_SIGN, ' '), NVL(PW_DBW, 0.0),
                NVL(TO_CHAR(CANCEL_DATE), '-'), GAIN, HALT
         FROM   STATION
         WHERE  SUBDISTRICT = :o_subdistrict
         ORDER  BY LIC_TYPE, LIC_NO; */ 


    /* EXEC SQL OPEN C02; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 2;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = sq0002;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )28;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqcmod = (unsigned int )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_subdistrict;
    sqlstm.sqhstl[0] = (unsigned long )6;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    /* EXEC SQL
         FETCH C02
         INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
               :o_base_no, :o_station_type, :o_east, :o_north,
               :o_pw_sign, :o_pw_dbw, :o_cancel_date, :o_ant_gain,
               :o_ant_height; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 13;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )47;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqfoff = (         int )0;
    sqlstm.sqfmod = (unsigned int )2;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
    sqlstm.sqhstl[0] = (unsigned long )1;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
    sqlstm.sqhstl[1] = (unsigned long )5;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
    sqlstm.sqhstl[2] = (unsigned long )10;
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
    sqlstm.sqhstl[3] = (unsigned long )6;
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
    sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[4] = (         int  )0;
    sqlstm.sqindv[4] = (         short *)0;
    sqlstm.sqinds[4] = (         int  )0;
    sqlstm.sqharm[4] = (unsigned long )0;
    sqlstm.sqadto[4] = (unsigned short )0;
    sqlstm.sqtdso[4] = (unsigned short )0;
    sqlstm.sqhstv[5] = (unsigned char  *)&o_station_type;
    sqlstm.sqhstl[5] = (unsigned long )1;
    sqlstm.sqhsts[5] = (         int  )0;
    sqlstm.sqindv[5] = (         short *)0;
    sqlstm.sqinds[5] = (         int  )0;
    sqlstm.sqharm[5] = (unsigned long )0;
    sqlstm.sqadto[5] = (unsigned short )0;
    sqlstm.sqtdso[5] = (unsigned short )0;
    sqlstm.sqhstv[6] = (unsigned char  *)&o_east;
    sqlstm.sqhstl[6] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[6] = (         int  )0;
    sqlstm.sqindv[6] = (         short *)0;
    sqlstm.sqinds[6] = (         int  )0;
    sqlstm.sqharm[6] = (unsigned long )0;
    sqlstm.sqadto[6] = (unsigned short )0;
    sqlstm.sqtdso[6] = (unsigned short )0;
    sqlstm.sqhstv[7] = (unsigned char  *)&o_north;
    sqlstm.sqhstl[7] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[7] = (         int  )0;
    sqlstm.sqindv[7] = (         short *)0;
    sqlstm.sqinds[7] = (         int  )0;
    sqlstm.sqharm[7] = (unsigned long )0;
    sqlstm.sqadto[7] = (unsigned short )0;
    sqlstm.sqtdso[7] = (unsigned short )0;
    sqlstm.sqhstv[8] = (unsigned char  *)&o_pw_sign;
    sqlstm.sqhstl[8] = (unsigned long )1;
    sqlstm.sqhsts[8] = (         int  )0;
    sqlstm.sqindv[8] = (         short *)0;
    sqlstm.sqinds[8] = (         int  )0;
    sqlstm.sqharm[8] = (unsigned long )0;
    sqlstm.sqadto[8] = (unsigned short )0;
    sqlstm.sqtdso[8] = (unsigned short )0;
    sqlstm.sqhstv[9] = (unsigned char  *)&o_pw_dbw;
    sqlstm.sqhstl[9] = (unsigned long )sizeof(float);
    sqlstm.sqhsts[9] = (         int  )0;
    sqlstm.sqindv[9] = (         short *)0;
    sqlstm.sqinds[9] = (         int  )0;
    sqlstm.sqharm[9] = (unsigned long )0;
    sqlstm.sqadto[9] = (unsigned short )0;
    sqlstm.sqtdso[9] = (unsigned short )0;
    sqlstm.sqhstv[10] = (unsigned char  *)&o_cancel_date;
    sqlstm.sqhstl[10] = (unsigned long )12;
    sqlstm.sqhsts[10] = (         int  )0;
    sqlstm.sqindv[10] = (         short *)0;
    sqlstm.sqinds[10] = (         int  )0;
    sqlstm.sqharm[10] = (unsigned long )0;
    sqlstm.sqadto[10] = (unsigned short )0;
    sqlstm.sqtdso[10] = (unsigned short )0;
    sqlstm.sqhstv[11] = (unsigned char  *)&o_ant_gain;
    sqlstm.sqhstl[11] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[11] = (         int  )0;
    sqlstm.sqindv[11] = (         short *)0;
    sqlstm.sqinds[11] = (         int  )0;
    sqlstm.sqharm[11] = (unsigned long )0;
    sqlstm.sqadto[11] = (unsigned short )0;
    sqlstm.sqtdso[11] = (unsigned short )0;
    sqlstm.sqhstv[12] = (unsigned char  *)&o_ant_height;
    sqlstm.sqhstl[12] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[12] = (         int  )0;
    sqlstm.sqindv[12] = (         short *)0;
    sqlstm.sqinds[12] = (         int  )0;
    sqlstm.sqharm[12] = (unsigned long )0;
    sqlstm.sqadto[12] = (unsigned short )0;
    sqlstm.sqtdso[12] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "No stations within %s found, prees any to exit", 
                o_subdistrict.arr);
        /* EXEC SQL CLOSE C02; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 13;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )114;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


        return ERROR;
    }

    fq_cnt = 0;

    for (i = 0; i < MAX_EXIST; )
    {
        /*************************************************************/
        /*  if this is a cancelled station (CANCEL_DATE is not NULL) */
        /*  skip it                                                  */
        /*************************************************************/
/*
        if ((o_east == 0) || (o_north == 0))
*/
        if (o_cancel_date.arr[0] != '-')
        {
            /* EXEC SQL
                 FETCH C02
                 INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
                       :o_base_no, :o_station_type, :o_east, :o_north,
                       :o_pw_sign, :o_pw_dbw, :o_cancel_date, :o_ant_gain,
                       :o_ant_height; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 13;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )129;
            sqlstm.selerr = (unsigned short)1;
            sqlstm.sqlpfmem = (unsigned int  )0;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlstm.sqfoff = (         int )0;
            sqlstm.sqfmod = (unsigned int )2;
            sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
            sqlstm.sqhstl[0] = (unsigned long )1;
            sqlstm.sqhsts[0] = (         int  )0;
            sqlstm.sqindv[0] = (         short *)0;
            sqlstm.sqinds[0] = (         int  )0;
            sqlstm.sqharm[0] = (unsigned long )0;
            sqlstm.sqadto[0] = (unsigned short )0;
            sqlstm.sqtdso[0] = (unsigned short )0;
            sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
            sqlstm.sqhstl[1] = (unsigned long )5;
            sqlstm.sqhsts[1] = (         int  )0;
            sqlstm.sqindv[1] = (         short *)0;
            sqlstm.sqinds[1] = (         int  )0;
            sqlstm.sqharm[1] = (unsigned long )0;
            sqlstm.sqadto[1] = (unsigned short )0;
            sqlstm.sqtdso[1] = (unsigned short )0;
            sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
            sqlstm.sqhstl[2] = (unsigned long )10;
            sqlstm.sqhsts[2] = (         int  )0;
            sqlstm.sqindv[2] = (         short *)0;
            sqlstm.sqinds[2] = (         int  )0;
            sqlstm.sqharm[2] = (unsigned long )0;
            sqlstm.sqadto[2] = (unsigned short )0;
            sqlstm.sqtdso[2] = (unsigned short )0;
            sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
            sqlstm.sqhstl[3] = (unsigned long )6;
            sqlstm.sqhsts[3] = (         int  )0;
            sqlstm.sqindv[3] = (         short *)0;
            sqlstm.sqinds[3] = (         int  )0;
            sqlstm.sqharm[3] = (unsigned long )0;
            sqlstm.sqadto[3] = (unsigned short )0;
            sqlstm.sqtdso[3] = (unsigned short )0;
            sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
            sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
            sqlstm.sqhsts[4] = (         int  )0;
            sqlstm.sqindv[4] = (         short *)0;
            sqlstm.sqinds[4] = (         int  )0;
            sqlstm.sqharm[4] = (unsigned long )0;
            sqlstm.sqadto[4] = (unsigned short )0;
            sqlstm.sqtdso[4] = (unsigned short )0;
            sqlstm.sqhstv[5] = (unsigned char  *)&o_station_type;
            sqlstm.sqhstl[5] = (unsigned long )1;
            sqlstm.sqhsts[5] = (         int  )0;
            sqlstm.sqindv[5] = (         short *)0;
            sqlstm.sqinds[5] = (         int  )0;
            sqlstm.sqharm[5] = (unsigned long )0;
            sqlstm.sqadto[5] = (unsigned short )0;
            sqlstm.sqtdso[5] = (unsigned short )0;
            sqlstm.sqhstv[6] = (unsigned char  *)&o_east;
            sqlstm.sqhstl[6] = (unsigned long )sizeof(int);
            sqlstm.sqhsts[6] = (         int  )0;
            sqlstm.sqindv[6] = (         short *)0;
            sqlstm.sqinds[6] = (         int  )0;
            sqlstm.sqharm[6] = (unsigned long )0;
            sqlstm.sqadto[6] = (unsigned short )0;
            sqlstm.sqtdso[6] = (unsigned short )0;
            sqlstm.sqhstv[7] = (unsigned char  *)&o_north;
            sqlstm.sqhstl[7] = (unsigned long )sizeof(int);
            sqlstm.sqhsts[7] = (         int  )0;
            sqlstm.sqindv[7] = (         short *)0;
            sqlstm.sqinds[7] = (         int  )0;
            sqlstm.sqharm[7] = (unsigned long )0;
            sqlstm.sqadto[7] = (unsigned short )0;
            sqlstm.sqtdso[7] = (unsigned short )0;
            sqlstm.sqhstv[8] = (unsigned char  *)&o_pw_sign;
            sqlstm.sqhstl[8] = (unsigned long )1;
            sqlstm.sqhsts[8] = (         int  )0;
            sqlstm.sqindv[8] = (         short *)0;
            sqlstm.sqinds[8] = (         int  )0;
            sqlstm.sqharm[8] = (unsigned long )0;
            sqlstm.sqadto[8] = (unsigned short )0;
            sqlstm.sqtdso[8] = (unsigned short )0;
            sqlstm.sqhstv[9] = (unsigned char  *)&o_pw_dbw;
            sqlstm.sqhstl[9] = (unsigned long )sizeof(float);
            sqlstm.sqhsts[9] = (         int  )0;
            sqlstm.sqindv[9] = (         short *)0;
            sqlstm.sqinds[9] = (         int  )0;
            sqlstm.sqharm[9] = (unsigned long )0;
            sqlstm.sqadto[9] = (unsigned short )0;
            sqlstm.sqtdso[9] = (unsigned short )0;
            sqlstm.sqhstv[10] = (unsigned char  *)&o_cancel_date;
            sqlstm.sqhstl[10] = (unsigned long )12;
            sqlstm.sqhsts[10] = (         int  )0;
            sqlstm.sqindv[10] = (         short *)0;
            sqlstm.sqinds[10] = (         int  )0;
            sqlstm.sqharm[10] = (unsigned long )0;
            sqlstm.sqadto[10] = (unsigned short )0;
            sqlstm.sqtdso[10] = (unsigned short )0;
            sqlstm.sqhstv[11] = (unsigned char  *)&o_ant_gain;
            sqlstm.sqhstl[11] = (unsigned long )sizeof(int);
            sqlstm.sqhsts[11] = (         int  )0;
            sqlstm.sqindv[11] = (         short *)0;
            sqlstm.sqinds[11] = (         int  )0;
            sqlstm.sqharm[11] = (unsigned long )0;
            sqlstm.sqadto[11] = (unsigned short )0;
            sqlstm.sqtdso[11] = (unsigned short )0;
            sqlstm.sqhstv[12] = (unsigned char  *)&o_ant_height;
            sqlstm.sqhstl[12] = (unsigned long )sizeof(int);
            sqlstm.sqhsts[12] = (         int  )0;
            sqlstm.sqindv[12] = (         short *)0;
            sqlstm.sqinds[12] = (         int  )0;
            sqlstm.sqharm[12] = (unsigned long )0;
            sqlstm.sqadto[12] = (unsigned short )0;
            sqlstm.sqtdso[12] = (unsigned short )0;
            sqlstm.sqphsv = sqlstm.sqhstv;
            sqlstm.sqphsl = sqlstm.sqhstl;
            sqlstm.sqphss = sqlstm.sqhsts;
            sqlstm.sqpind = sqlstm.sqindv;
            sqlstm.sqpins = sqlstm.sqinds;
            sqlstm.sqparm = sqlstm.sqharm;
            sqlstm.sqparc = sqlstm.sqharc;
            sqlstm.sqpadto = sqlstm.sqadto;
            sqlstm.sqptdso = sqlstm.sqtdso;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



            if (sqlca.sqlcode == NOT_FOUND)
            {
                /* EXEC SQL CLOSE C02; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 13;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )196;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                break;
            }

            continue;
        }

        if ((exist[i] = (EXIST *) malloc(sizeof(EXIST))) == (EXIST *) NULL)
        {
            sprintf(err_msg, 
                    "Cannot allocate space for 'exist', press any key to exit");
            return ERROR;
        }

        o_sys_type.arr[o_sys_type.len] = '\0';
        o_sys_no.arr[o_sys_no.len] = '\0';
        o_sys_suffix.arr[o_sys_suffix.len] = '\0';

        /* EXEC SQL DECLARE C03 CURSOR FOR
             SELECT NVL(TX_FREQ, 0.0), NVL(RX_FREQ, 0.0), UPPER(ACT_FLAG)
             FROM   BASE_CH
             WHERE  SYS_CATEGORY = :o_sys_category
             AND    SYS_TYPE = :o_sys_type
             AND    SYS_NO = :o_sys_no
             AND    SYS_SUFFIX = :o_sys_suffix
             AND    BASE_NO = :o_base_no
             ORDER BY TX_FREQ; */ 


        /* EXEC SQL OPEN C03; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 13;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.stmt = sq0003;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )211;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqcmod = (unsigned int )0;
        sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
        sqlstm.sqhstl[0] = (unsigned long )1;
        sqlstm.sqhsts[0] = (         int  )0;
        sqlstm.sqindv[0] = (         short *)0;
        sqlstm.sqinds[0] = (         int  )0;
        sqlstm.sqharm[0] = (unsigned long )0;
        sqlstm.sqadto[0] = (unsigned short )0;
        sqlstm.sqtdso[0] = (unsigned short )0;
        sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
        sqlstm.sqhstl[1] = (unsigned long )5;
        sqlstm.sqhsts[1] = (         int  )0;
        sqlstm.sqindv[1] = (         short *)0;
        sqlstm.sqinds[1] = (         int  )0;
        sqlstm.sqharm[1] = (unsigned long )0;
        sqlstm.sqadto[1] = (unsigned short )0;
        sqlstm.sqtdso[1] = (unsigned short )0;
        sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
        sqlstm.sqhstl[2] = (unsigned long )10;
        sqlstm.sqhsts[2] = (         int  )0;
        sqlstm.sqindv[2] = (         short *)0;
        sqlstm.sqinds[2] = (         int  )0;
        sqlstm.sqharm[2] = (unsigned long )0;
        sqlstm.sqadto[2] = (unsigned short )0;
        sqlstm.sqtdso[2] = (unsigned short )0;
        sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
        sqlstm.sqhstl[3] = (unsigned long )6;
        sqlstm.sqhsts[3] = (         int  )0;
        sqlstm.sqindv[3] = (         short *)0;
        sqlstm.sqinds[3] = (         int  )0;
        sqlstm.sqharm[3] = (unsigned long )0;
        sqlstm.sqadto[3] = (unsigned short )0;
        sqlstm.sqtdso[3] = (unsigned short )0;
        sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
        sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[4] = (         int  )0;
        sqlstm.sqindv[4] = (         short *)0;
        sqlstm.sqinds[4] = (         int  )0;
        sqlstm.sqharm[4] = (unsigned long )0;
        sqlstm.sqadto[4] = (unsigned short )0;
        sqlstm.sqtdso[4] = (unsigned short )0;
        sqlstm.sqphsv = sqlstm.sqhstv;
        sqlstm.sqphsl = sqlstm.sqhstl;
        sqlstm.sqphss = sqlstm.sqhsts;
        sqlstm.sqpind = sqlstm.sqindv;
        sqlstm.sqpins = sqlstm.sqinds;
        sqlstm.sqparm = sqlstm.sqharm;
        sqlstm.sqparc = sqlstm.sqharc;
        sqlstm.sqpadto = sqlstm.sqadto;
        sqlstm.sqptdso = sqlstm.sqtdso;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



        /* EXEC SQL
             FETCH C03
             INTO  :o_tx_freq, :o_rx_freq, o_act_flag; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 13;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )246;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqfoff = (         int )0;
        sqlstm.sqfmod = (unsigned int )2;
        sqlstm.sqhstv[0] = (unsigned char  *)&o_tx_freq;
        sqlstm.sqhstl[0] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[0] = (         int  )0;
        sqlstm.sqindv[0] = (         short *)0;
        sqlstm.sqinds[0] = (         int  )0;
        sqlstm.sqharm[0] = (unsigned long )0;
        sqlstm.sqadto[0] = (unsigned short )0;
        sqlstm.sqtdso[0] = (unsigned short )0;
        sqlstm.sqhstv[1] = (unsigned char  *)&o_rx_freq;
        sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
        sqlstm.sqhsts[1] = (         int  )0;
        sqlstm.sqindv[1] = (         short *)0;
        sqlstm.sqinds[1] = (         int  )0;
        sqlstm.sqharm[1] = (unsigned long )0;
        sqlstm.sqadto[1] = (unsigned short )0;
        sqlstm.sqtdso[1] = (unsigned short )0;
        sqlstm.sqhstv[2] = (unsigned char  *)&o_act_flag;
        sqlstm.sqhstl[2] = (unsigned long )1;
        sqlstm.sqhsts[2] = (         int  )0;
        sqlstm.sqindv[2] = (         short *)0;
        sqlstm.sqinds[2] = (         int  )0;
        sqlstm.sqharm[2] = (unsigned long )0;
        sqlstm.sqadto[2] = (unsigned short )0;
        sqlstm.sqtdso[2] = (unsigned short )0;
        sqlstm.sqphsv = sqlstm.sqhstv;
        sqlstm.sqphsl = sqlstm.sqhstl;
        sqlstm.sqphss = sqlstm.sqhsts;
        sqlstm.sqpind = sqlstm.sqindv;
        sqlstm.sqpins = sqlstm.sqinds;
        sqlstm.sqparm = sqlstm.sqharm;
        sqlstm.sqparc = sqlstm.sqharc;
        sqlstm.sqpadto = sqlstm.sqadto;
        sqlstm.sqptdso = sqlstm.sqtdso;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



        if (sqlca.sqlcode == NOT_FOUND)
        {
            sprintf(err_msg, "No BASE_CH record for %c-%s-%s-%s base no. %d",
                    o_sys_category, o_sys_type.arr, o_sys_no.arr,
                    o_sys_suffix.arr, o_base_no);
            sprintf(err_msg, "%s, press any to continue", err_msg);
            /* EXEC SQL CLOSE C03; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 13;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )273;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


            /* EXEC SQL CLOSE C02; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 13;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )288;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


            return ERROR;
        }

        for ( ; ; )
        {
            fq_list[fq_cnt] = (EXIST_FREQ *) malloc(sizeof(EXIST_FREQ));
            s_fq_list[fq_cnt] = (EXIST_FREQ *) malloc(sizeof(EXIST_FREQ));
            if ((fq_list[fq_cnt] == (EXIST_FREQ *) NULL)
            ||  (fq_list[fq_cnt] == (EXIST_FREQ *) NULL))
            {
                sprintf(err_msg, 
                "Cannot allocate space for fq_list/s_fq_list, press any key to exit");
                return ERROR;
            }
            fq_list[fq_cnt]->tx_freq = o_tx_freq;
            fq_list[fq_cnt]->rx_freq = o_rx_freq;
            fq_list[fq_cnt]->tx_channel = (int)(o_tx_freq/MIN_CHANNEL_SEP + .5);
            fq_list[fq_cnt]->rx_channel = (int)(o_rx_freq/MIN_CHANNEL_SEP + .5);
            fq_list[fq_cnt]->act_flag = 
                (o_act_flag == 'N')? o_act_flag : ' ';
            fq_list[fq_cnt]->stn_node = i;
            s_fq_list[fq_cnt]->tx_freq = o_tx_freq;
            s_fq_list[fq_cnt]->rx_freq = o_rx_freq;
            s_fq_list[fq_cnt]->tx_channel = 
                (int)(o_tx_freq / MIN_CHANNEL_SEP + .5);
            s_fq_list[fq_cnt]->rx_channel = 
                (int)(o_rx_freq / MIN_CHANNEL_SEP + .5);
            s_fq_list[fq_cnt]->stn_node = i;
            fq_cnt++;

            if (fq_cnt == (MAX_EXIST*2 + 500))
            {
                sprintf(err_msg, "No. of channels exceeds %d\n", (MAX_EXIST*2 + 500));
                /* EXEC SQL CLOSE C02; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 13;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )303;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                /* EXEC SQL CLOSE C03; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 13;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )318;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                return ERROR;
            }

            /* EXEC SQL
                 FETCH C03
                 INTO  :o_tx_freq, :o_rx_freq; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 13;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )333;
            sqlstm.selerr = (unsigned short)1;
            sqlstm.sqlpfmem = (unsigned int  )0;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlstm.sqfoff = (         int )0;
            sqlstm.sqfmod = (unsigned int )2;
            sqlstm.sqhstv[0] = (unsigned char  *)&o_tx_freq;
            sqlstm.sqhstl[0] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[0] = (         int  )0;
            sqlstm.sqindv[0] = (         short *)0;
            sqlstm.sqinds[0] = (         int  )0;
            sqlstm.sqharm[0] = (unsigned long )0;
            sqlstm.sqadto[0] = (unsigned short )0;
            sqlstm.sqtdso[0] = (unsigned short )0;
            sqlstm.sqhstv[1] = (unsigned char  *)&o_rx_freq;
            sqlstm.sqhstl[1] = (unsigned long )sizeof(double);
            sqlstm.sqhsts[1] = (         int  )0;
            sqlstm.sqindv[1] = (         short *)0;
            sqlstm.sqinds[1] = (         int  )0;
            sqlstm.sqharm[1] = (unsigned long )0;
            sqlstm.sqadto[1] = (unsigned short )0;
            sqlstm.sqtdso[1] = (unsigned short )0;
            sqlstm.sqphsv = sqlstm.sqhstv;
            sqlstm.sqphsl = sqlstm.sqhstl;
            sqlstm.sqphss = sqlstm.sqhsts;
            sqlstm.sqpind = sqlstm.sqindv;
            sqlstm.sqpins = sqlstm.sqinds;
            sqlstm.sqparm = sqlstm.sqharm;
            sqlstm.sqparc = sqlstm.sqharc;
            sqlstm.sqpadto = sqlstm.sqadto;
            sqlstm.sqptdso = sqlstm.sqtdso;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
            if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



            if (sqlca.sqlcode == NOT_FOUND)
            {
                /* EXEC SQL CLOSE C03; */ 

{
                struct sqlexd sqlstm;
                sqlstm.sqlvsn = 13;
                sqlstm.arrsiz = 13;
                sqlstm.sqladtp = &sqladt;
                sqlstm.sqltdsp = &sqltds;
                sqlstm.iters = (unsigned int  )1;
                sqlstm.offset = (unsigned int  )356;
                sqlstm.cud = sqlcud0;
                sqlstm.sqlest = (unsigned char  *)&sqlca;
                sqlstm.sqlety = (unsigned short)4352;
                sqlstm.occurs = (unsigned int  )0;
                sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
                if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


                break;
            }
        }

        exist[i]->sys_category = o_sys_category;
/*        strcpy(exist[i]->sys_type,  o_sys_type.arr);
        strcpy(exist[i]->sys_no,  o_sys_no.arr);
        strcpy(exist[i]->sys_suffix,  o_sys_suffix.arr); */
        strcpy(exist[i]->sys_type,  (char *)o_sys_type.arr);
        strcpy(exist[i]->sys_no,  (char *)o_sys_no.arr);
        strcpy(exist[i]->sys_suffix,  (char *)o_sys_suffix.arr);

        exist[i]->base_no = o_base_no;
        exist[i]->station_type = o_station_type;
        exist[i]->east_grid = o_east;
        exist[i]->north_grid = o_north;
        exist[i]->ant_gain = o_ant_gain;
        exist[i]->ant_height = o_ant_height;
/*
sprintf(s,"east north: %d %d", exist[i]->east_grid, exist[i]->north_grid);
mvaddstr(23,0,s);refresh();getch();
*/

        exist[i]->pw_dbw = (o_pw_sign == MINUS) ? (-1 * o_pw_dbw) : o_pw_dbw;
        i++;

        /* EXEC SQL
             FETCH C02
             INTO  :o_sys_category, :o_sys_type, :o_sys_no, :o_sys_suffix,
                   :o_base_no, :o_station_type, :o_east, :o_north,
                   :o_pw_sign, :o_pw_dbw, :o_cancel_date, :o_ant_gain,
                   :o_ant_height; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 13;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )371;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqfoff = (         int )0;
        sqlstm.sqfmod = (unsigned int )2;
        sqlstm.sqhstv[0] = (unsigned char  *)&o_sys_category;
        sqlstm.sqhstl[0] = (unsigned long )1;
        sqlstm.sqhsts[0] = (         int  )0;
        sqlstm.sqindv[0] = (         short *)0;
        sqlstm.sqinds[0] = (         int  )0;
        sqlstm.sqharm[0] = (unsigned long )0;
        sqlstm.sqadto[0] = (unsigned short )0;
        sqlstm.sqtdso[0] = (unsigned short )0;
        sqlstm.sqhstv[1] = (unsigned char  *)&o_sys_type;
        sqlstm.sqhstl[1] = (unsigned long )5;
        sqlstm.sqhsts[1] = (         int  )0;
        sqlstm.sqindv[1] = (         short *)0;
        sqlstm.sqinds[1] = (         int  )0;
        sqlstm.sqharm[1] = (unsigned long )0;
        sqlstm.sqadto[1] = (unsigned short )0;
        sqlstm.sqtdso[1] = (unsigned short )0;
        sqlstm.sqhstv[2] = (unsigned char  *)&o_sys_no;
        sqlstm.sqhstl[2] = (unsigned long )10;
        sqlstm.sqhsts[2] = (         int  )0;
        sqlstm.sqindv[2] = (         short *)0;
        sqlstm.sqinds[2] = (         int  )0;
        sqlstm.sqharm[2] = (unsigned long )0;
        sqlstm.sqadto[2] = (unsigned short )0;
        sqlstm.sqtdso[2] = (unsigned short )0;
        sqlstm.sqhstv[3] = (unsigned char  *)&o_sys_suffix;
        sqlstm.sqhstl[3] = (unsigned long )6;
        sqlstm.sqhsts[3] = (         int  )0;
        sqlstm.sqindv[3] = (         short *)0;
        sqlstm.sqinds[3] = (         int  )0;
        sqlstm.sqharm[3] = (unsigned long )0;
        sqlstm.sqadto[3] = (unsigned short )0;
        sqlstm.sqtdso[3] = (unsigned short )0;
        sqlstm.sqhstv[4] = (unsigned char  *)&o_base_no;
        sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[4] = (         int  )0;
        sqlstm.sqindv[4] = (         short *)0;
        sqlstm.sqinds[4] = (         int  )0;
        sqlstm.sqharm[4] = (unsigned long )0;
        sqlstm.sqadto[4] = (unsigned short )0;
        sqlstm.sqtdso[4] = (unsigned short )0;
        sqlstm.sqhstv[5] = (unsigned char  *)&o_station_type;
        sqlstm.sqhstl[5] = (unsigned long )1;
        sqlstm.sqhsts[5] = (         int  )0;
        sqlstm.sqindv[5] = (         short *)0;
        sqlstm.sqinds[5] = (         int  )0;
        sqlstm.sqharm[5] = (unsigned long )0;
        sqlstm.sqadto[5] = (unsigned short )0;
        sqlstm.sqtdso[5] = (unsigned short )0;
        sqlstm.sqhstv[6] = (unsigned char  *)&o_east;
        sqlstm.sqhstl[6] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[6] = (         int  )0;
        sqlstm.sqindv[6] = (         short *)0;
        sqlstm.sqinds[6] = (         int  )0;
        sqlstm.sqharm[6] = (unsigned long )0;
        sqlstm.sqadto[6] = (unsigned short )0;
        sqlstm.sqtdso[6] = (unsigned short )0;
        sqlstm.sqhstv[7] = (unsigned char  *)&o_north;
        sqlstm.sqhstl[7] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[7] = (         int  )0;
        sqlstm.sqindv[7] = (         short *)0;
        sqlstm.sqinds[7] = (         int  )0;
        sqlstm.sqharm[7] = (unsigned long )0;
        sqlstm.sqadto[7] = (unsigned short )0;
        sqlstm.sqtdso[7] = (unsigned short )0;
        sqlstm.sqhstv[8] = (unsigned char  *)&o_pw_sign;
        sqlstm.sqhstl[8] = (unsigned long )1;
        sqlstm.sqhsts[8] = (         int  )0;
        sqlstm.sqindv[8] = (         short *)0;
        sqlstm.sqinds[8] = (         int  )0;
        sqlstm.sqharm[8] = (unsigned long )0;
        sqlstm.sqadto[8] = (unsigned short )0;
        sqlstm.sqtdso[8] = (unsigned short )0;
        sqlstm.sqhstv[9] = (unsigned char  *)&o_pw_dbw;
        sqlstm.sqhstl[9] = (unsigned long )sizeof(float);
        sqlstm.sqhsts[9] = (         int  )0;
        sqlstm.sqindv[9] = (         short *)0;
        sqlstm.sqinds[9] = (         int  )0;
        sqlstm.sqharm[9] = (unsigned long )0;
        sqlstm.sqadto[9] = (unsigned short )0;
        sqlstm.sqtdso[9] = (unsigned short )0;
        sqlstm.sqhstv[10] = (unsigned char  *)&o_cancel_date;
        sqlstm.sqhstl[10] = (unsigned long )12;
        sqlstm.sqhsts[10] = (         int  )0;
        sqlstm.sqindv[10] = (         short *)0;
        sqlstm.sqinds[10] = (         int  )0;
        sqlstm.sqharm[10] = (unsigned long )0;
        sqlstm.sqadto[10] = (unsigned short )0;
        sqlstm.sqtdso[10] = (unsigned short )0;
        sqlstm.sqhstv[11] = (unsigned char  *)&o_ant_gain;
        sqlstm.sqhstl[11] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[11] = (         int  )0;
        sqlstm.sqindv[11] = (         short *)0;
        sqlstm.sqinds[11] = (         int  )0;
        sqlstm.sqharm[11] = (unsigned long )0;
        sqlstm.sqadto[11] = (unsigned short )0;
        sqlstm.sqtdso[11] = (unsigned short )0;
        sqlstm.sqhstv[12] = (unsigned char  *)&o_ant_height;
        sqlstm.sqhstl[12] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[12] = (         int  )0;
        sqlstm.sqindv[12] = (         short *)0;
        sqlstm.sqinds[12] = (         int  )0;
        sqlstm.sqharm[12] = (unsigned long )0;
        sqlstm.sqadto[12] = (unsigned short )0;
        sqlstm.sqtdso[12] = (unsigned short )0;
        sqlstm.sqphsv = sqlstm.sqhstv;
        sqlstm.sqphsl = sqlstm.sqhstl;
        sqlstm.sqphss = sqlstm.sqhsts;
        sqlstm.sqpind = sqlstm.sqindv;
        sqlstm.sqpins = sqlstm.sqinds;
        sqlstm.sqparm = sqlstm.sqharm;
        sqlstm.sqparc = sqlstm.sqharc;
        sqlstm.sqpadto = sqlstm.sqadto;
        sqlstm.sqptdso = sqlstm.sqtdso;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



        if (sqlca.sqlcode == NOT_FOUND)
            break;
    }

    /* EXEC SQL CLOSE C02; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 13;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )438;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (i == MAX_EXIST)
    {
        sprintf(err_msg, "No. of stations exceeds %d\n", MAX_EXIST);
        return ERROR;
    }

    cull_stn_cnt = i;

/**********************************************************************/
/*  sort all channels in ascending order using shell sort             */
/**********************************************************************/

    for (gap = fq_cnt / 2; gap > 0; gap /= 2)
        for (i = gap; i < fq_cnt; i++)
            for (j = i - gap; 
                (j >= 0) && (s_fq_list[j]->rx_freq > s_fq_list[j+gap]->rx_freq);
                 j -= gap)
            {
                tmp_list         = s_fq_list[j];
                s_fq_list[j]     = s_fq_list[j+gap];
                s_fq_list[j+gap] = tmp_list;
            }

    return OK;

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    disp_err(err_msg);
    refresh(); beep();
    sleep(1); clear(); refresh();
    endwin();
    exit(1);
}


/***************************************/
/*    print intermod summary report    */
/***************************************/

print_intermod_summary(err_msg)
char   *err_msg;
{
    char    sfname[120];
    char    cmdline[150];
    FILE    *sfp;

    sprintf(sfname, "%s/cosite/summary/%s.%.5lf.%s", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if ((sfp = fopen(sfname, "w")) == (FILE *) NULL)
    {
        sprintf(err_msg, "Fail to open summary file, press any to exit");
        return ERROR;
    }

    fprintf(sfp, "RUN DATE: %s%14s", sys_date, "");
    fprintf(sfp,
           "*************************************************************"); 
    fprintf(sfp, "%22sPAGE   : 1\n", "");
    fprintf(sfp, "RUN TIME: %s%14s", sys_time, "");
    fprintf(sfp,
           "*                                                           *");
    fprintf(sfp, "%22sPROGRAM: esemcs0f\n", "");
    fprintf(sfp, "USER ID : %-19s%3s", emc_uid, "");
    fprintf(sfp,
      "*             CO-SITE ANALYSIS - INTERMODULATION            *\n");
    fprintf(sfp, "%32s", "");
    fprintf(sfp, 
      "*                       SUMMARY   LOG                       *\n");
    fprintf(sfp, "%32s*%59s*\n", "", "");
    fprintf(sfp, "%32s*%21sSUB-DISTRICT : %-3s%20s*\n", 
            "", "", sub_district, "");
    fprintf(sfp, "%32s*%59s*\n", "", "");
    fprintf(sfp, 
            "%32s*  RX FREQ (MHz) : %10.4lf   TX FREQ (MHz) : %10.4lf  *\n",
            "", prop_rx_freq, prop_tx_freq);
    fprintf(sfp, "%32s*%59s*\n", "", "");
    fprintf(sfp, "%32s", "");
    fprintf(sfp, 
            "*************************************************************\n");
    fprintf(sfp, "\n\n\n");
    fprintf(sfp, "** 2-signal Intermodulation Report");
    fprintf(sfp, "(Proposed Station as Victim)\n");
    fprintf(sfp, "   Number of Combinations : %d\n\n", intmod2_vict_tot);
    fprintf(sfp, "** 2-signal Intermodulation Report");
    fprintf(sfp, "(Proposed Station as Tx1)\n");
    fprintf(sfp, "   Number of Combinations : %d\n\n", intmod2_tx1_tot);
    fprintf(sfp, "** 2-signal Intermodulation Report");
    fprintf(sfp, "(Proposed Station as Tx2)\n");
    fprintf(sfp, "   Number of Combinations : %d\n\n\n", intmod2_tx2_tot);
    fprintf(sfp, "** 3-signal Intermodulation Report");
    fprintf(sfp, "(Proposed Station as Victim)\n");
    fprintf(sfp, "   Number of Combinations : %d\n\n", intmod3_vict_tot);
    fprintf(sfp, "** 3-signal Intermodulation Report");
    fprintf(sfp, "(Proposed Station as Tx1)\n");
    fprintf(sfp, "   Number of Combinations : %d\n\n", intmod3_tx1_tot);
    fprintf(sfp, "** 3-signal Intermodulation Report");
    fprintf(sfp, "(Proposed Station as Tx3)\n");
    fprintf(sfp, "   Number of Combinations : %d\f", intmod3_tx3_tot);
    fclose(sfp);

/*
    sprintf(cmdline, "cat %s >> xxx", sfname);
*/
    sprintf(cmdline, "lp -dprinter1 %s > /dev/null",sfname);
    system(cmdline);
    return OK;
}
    

/***************************************/
/* reprint sub-district station report */
/***************************************/

reprint_substn_rpt()
{
    char   cs_fname[120];
    char   cmdline[150];
    FILE   *ifp;

    sprintf(cs_fname, "%s/cosite/substn/%s.%s", 
            emc_dir, emc_uid, sub_district);
    if ((ifp = fopen(cs_fname, "r")) == (FILE *)NULL)
        return ERROR;
    fclose(ifp);
    centre_msg(23, "Printing ...", A_BLINK, A_REVERSE);
/*
    sprintf(cmdline, "cat %s >> xxx", cs_fname);
*/
    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", cs_fname);
    system(cmdline);
    return OK;
}


/************************************/
/* reprint 2-signal intermod report */
/************************************/

reprint_intermod2_rpt()
{
    char   im_fname[120];
    int    found = FALSE;

    sprintf(im_fname, "%s/cosite/intermod2/%s.%.5lf.%s.v", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if (reprint_report(im_fname) == OK)
        found = TRUE;
    sprintf(im_fname, "%s/cosite/intermod2/%s.%.5lf.%s.t1", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if (reprint_report(im_fname) == OK)
        found = TRUE;
    sprintf(im_fname, "%s/cosite/intermod2/%s.%.5lf.%s.t2", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if (reprint_report(im_fname) == OK)
        found = TRUE;

    return ((found == TRUE) ? OK : ERROR);
}


/************************************/
/* reprint 3-signal intermod report */
/************************************/

reprint_intermod3_rpt()
{
    char   im_fname[120];
    int    found = FALSE;

    sprintf(im_fname, "%s/cosite/intermod3/%s.%.5lf.%s.v", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if (reprint_report(im_fname) == OK)
        found = TRUE;
    sprintf(im_fname, "%s/cosite/intermod3/%s.%.5lf.%s.t1", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if (reprint_report(im_fname) == OK)
        found = TRUE;
    sprintf(im_fname, "%s/cosite/intermod3/%s.%.5lf.%s.t3", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if (reprint_report(im_fname) == OK)
        found = TRUE;

    return ((found == TRUE) ? OK : ERROR);
}


/*****************************************/
/* actual reprint of the intermod report */
/*****************************************/

reprint_report(fname)
char   *fname;
{
    char   cmdline[150];
    FILE   *ifp;

    if ((ifp = fopen(fname, "r")) == (FILE *)NULL)
        return NOT_FOUND;
    fclose(ifp);
    centre_msg(23, "Printing ...", A_BLINK, A_REVERSE);
/*
    sprintf(cmdline, "cat %s >> xxx", fname);
*/
    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", fname);
    system(cmdline);
    return OK;
}
    

/***************************************/
/* reprint intermod summary log        */
/***************************************/

reprint_intermod_summary()
{
    char   sfname[120];
    char   cmdline[150];
    FILE   *ifp;

    sprintf(sfname, "%s/cosite/summary/%s.%.5lf.%s", 
            emc_dir, emc_uid, prop_tx_freq, sub_district);
    if ((ifp = fopen(sfname, "r")) == (FILE *)NULL)
        return ERROR;
    fclose(ifp);
    centre_msg(23, "Printing ...", A_BLINK, A_REVERSE);
/*
    sprintf(cmdline, "cat %s >> xxx", sfname);
*/
    sprintf(cmdline, "lp -dprinter1 %s > /dev/null", sfname);
    system(cmdline);
    return OK;
}
