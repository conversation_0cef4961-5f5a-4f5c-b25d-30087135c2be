/* @(#)dvr_cc_def.h	6.1	(ULTRIX)	11/19/91	*/
#define _dvr$cdef_

/*
**++
**  COPYRIGHT (c) 1988, 1990 BY
**  DIGITAL EQUIPMENT CORPORATION, MAYNARD, MASSACHUSETTS.
**  ALL RIGHTS RESERVED.
**
**  THIS SOFTWARE IS FURNISHED UNDER A LICENSE AND MAY BE USED AND COPIED
**  ONLY  IN  ACCORDANCE  OF  THE  TERMS  OF  SUCH  LICENSE  AND WITH THE
**  INCLUSION OF THE ABOVE COPYRIGHT NOTICE. THIS SOFTWARE OR  ANY  OTHER
**  COPIES THEREOF MAY NOT BE PROVIDED OR OTHERWISE MADE AVAILABLE TO ANY
**  OTHER PERSON.  NO TITLE TO AND  OWNERSHIP OF THE  SOFTWARE IS  HEREBY
**  TRANSFERRED.
**
**  THE INFORMATION IN THIS SOFTWARE IS  SUBJECT TO CHANGE WITHOUT NOTICE
**  AND  SHOULD  NOT  BE  CONSTRUED  AS A COMMITMENT BY DIGITAL EQUIPMENT
**  CORPORATION.
**
**  <PERSON><PERSON><PERSON><PERSON> ASSUMES NO RESPONSIBILITY FOR THE USE  OR  RELIABILITY OF ITS
**  SOFTWARE ON EQUIPMENT WHICH IS NOT SUPPLIED BY DIGITAL.
**--
**/


/*
**++
**  MODULE NAME:
**	dvr$cc_def.h
**
**  ABSTRACT:
**	This file includes definitions of constants for use by applications
**	calling the the public entry points of the CDA character cell viewer.
**
**--
**/

/**
 ** Character cell viewer constants and structure defs
 **/
/*
 * Select options flags, all are boolean
 */
#define DVR$M_SoftDirectives   (1L<<0)	/* obey soft directives		    */
#define DVR$M_Auto_Wrap	       (1L<<1)	/* do word-wrap formatting of text  */
#define DVR$M_Outfile	       (1L<<2)	/* output to file		    */
#define DVR$M_Paging	       (1L<<3)	/* pause at end of page		    */
#define DVR$M_Text	       (1L<<4)	/* output text			    */
#define DVR$M_Images	       (1L<<5)	/* text placeholder for images	    */
#define DVR$M_Graphics	       (1L<<6)	/* text placeholder for graphics    */
#define DVR$M_ReportErrors     (1L<<7)	/* write errors to stderr	    */
#define DVR$M_Layout	       (1L<<8)	/* Do layout			    */
#define DVR$M_SpecificLayout   (1L<<9)	/* Do specific layout		    */
#define DVR$M_Text_Backend     (1L<<10)	/* Act like a text backend	    */
#define DVR$M_DefaultOptions					    \
      DVR$M_SoftDirectives | DVR$M_Auto_Wrap | DVR$M_Outfile \
    | DVR$M_Text | DVR$M_Images | DVR$M_Graphics	       \
    | DVR$M_Layout | DVR$M_SpecificLayout

/* Item codes for CDA$CONVERT, CDA$OPEN_CONVERTER, domain$READ_format, and  */
/* domain$WRITE_format.  The DVR$ prefix has been substituted for the CDA$  */
/* prefix.								    */
#define DVR$_PROCESSING_OPTION 1        /* Processing option                */
#define DVR$_INPUT_FORMAT 2             /* Input document format-name       */
#define DVR$_INPUT_FRONT_END_PROCEDURE 3 /* Input document front-end procedure */
#define DVR$_INPUT_FILE 4               /* Input file specification         */
#define DVR$_INPUT_DEFAULT 5            /* Input default file specification */
#define DVR$_INPUT_PROCEDURE 6          /* Input get data procedure         */
#define DVR$_INPUT_POSITION_PROCEDURE 7 /* Input get position procedure     */
#define DVR$_INPUT_PROCEDURE_PARM 8     /* Input procedure parameter        */
#define DVR$_INPUT_ROOT_AGGREGATE 9     /* Input root aggregate             */
#define DVR$_OUTPUT_FORMAT 10           /* Output document format-name      */
#define DVR$_OUTPUT_BACK_END_PROCEDURE 11 /* Output document back-end procedure */
#define DVR$_OUTPUT_FILE 12             /* Output file specification        */
#define DVR$_OUTPUT_DEFAULT 13          /* Output default file specification */
#define DVR$_OUTPUT_PROCEDURE 14        /* Output procedure                 */
#define DVR$_OUTPUT_PROCEDURE_PARM 15   /* Output procedure parameter       */
#define DVR$_OUTPUT_PROCEDURE_BUFFER 16 /* Output procedure initial buffer  */
#define DVR$_OUTPUT_ROOT_AGGREGATE 17   /* Output root aggregate            */
#define DVR$_OPTIONS_FILE 18            /* Options file specification       */

/*
**  This following item codes area unique to the CC viewer.  They are intended
**  for use in the private item list argument to the DvrCCInitialize routine.
*/
#define DVR$_FRONT_END_HANDLE 	256	/* front end input procedure handle */
#define DVR$_PAGE_HEIGHT	257	/* formatted page height in chars   */
#define DVR$_PAGE_WIDTH		258	/* formatted page width in chars    */

