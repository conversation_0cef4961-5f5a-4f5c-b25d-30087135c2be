package com.emc.dao.impl;

import com.emc.dao.TerrainDao;
import com.emc.dao.mapper.TerrainRowMapper;
import com.emc.model.Terrain;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * JDBC implementation of TerrainDao.
 * This matches the original ProC TERRAIN table queries.
 */
@Repository
@Slf4j
public class TerrainDaoImpl implements TerrainDao {
    
    private final JdbcTemplate jdbcTemplate;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final TerrainRowMapper rowMapper;
    
    // SQL queries matching original ProC queries
    private static final String SELECT_ALL = 
        "SELECT EAST, NORTH, H1, H2, H3, H4 FROM TERRAIN";
    
    private static final String SELECT_BY_COORDINATES = SELECT_ALL +
        " WHERE NORTH = ? AND EAST = ?";
    
    private static final String SELECT_IN_BOUNDING_BOX = SELECT_ALL +
        " WHERE EAST BETWEEN ? AND ? AND NORTH BETWEEN ? AND ? " +
        "ORDER BY NORTH, EAST";
    
    @Autowired
    public TerrainDaoImpl(JdbcTemplate jdbcTemplate, NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.rowMapper = new TerrainRowMapper();
    }
    
    @Override
    public Optional<Terrain> findByGridCoordinates(int east, int north) {
        log.debug("Finding terrain by grid coordinates: E={}, N={}", east, north);
        try {
            Terrain terrain = jdbcTemplate.queryForObject(SELECT_BY_COORDINATES, rowMapper, north, east);
            return Optional.ofNullable(terrain);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    @Override
    public List<Terrain> findInBoundingBox(int eastMin, int eastMax, int northMin, int northMax) {
        log.debug("Finding terrain in bounding box: E={}-{}, N={}-{}", 
                 eastMin, eastMax, northMin, northMax);
        return jdbcTemplate.query(SELECT_IN_BOUNDING_BOX, rowMapper,
                                 eastMin, eastMax, northMin, northMax);
    }
    
    // BaseDao implementation methods
    @Override
    public Terrain save(Terrain entity) {
        throw new UnsupportedOperationException("Terrain save not implemented");
    }
    
    @Override
    public Terrain update(Terrain entity) {
        throw new UnsupportedOperationException("Terrain update not implemented");
    }
    
    @Override
    public Optional<Terrain> findById(String id) {
        throw new UnsupportedOperationException("Terrain findById not implemented - use findByGridCoordinates instead");
    }
    
    @Override
    public List<Terrain> findAll() {
        log.debug("Finding all terrain points");
        return jdbcTemplate.query(SELECT_ALL + " ORDER BY NORTH, EAST", rowMapper);
    }
    
    @Override
    public boolean deleteById(String id) {
        throw new UnsupportedOperationException("Terrain delete not implemented");
    }
    
    @Override
    public boolean existsById(String id) {
        throw new UnsupportedOperationException("Terrain existsById not implemented");
    }
    
    @Override
    public long count() {
        Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM TERRAIN", Integer.class);
        return count != null ? count : 0;
    }
}
