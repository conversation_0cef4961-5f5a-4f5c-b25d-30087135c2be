package com.emc.service;

import com.emc.model.Exist;
import com.emc.model.Propose;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for the copyExistToProp method in EmcAnalysisService.
 */
@ExtendWith(MockitoExtension.class)
public class EmcAnalysisServiceCopyTest {

    @Mock
    private ReferenceService referenceService;

    @Mock
    private TerrainService terrainService;

    @Mock
    private DesensitizationService desensitizationService;

    @Mock
    private IntermodulationService intermodulationService;

    @Mock
    private CochannelService cochannelService;

    @InjectMocks
    private EmcAnalysisService emcAnalysisService;

    private List<Exist> exist;
    private Propose prop;
    private String emcUid;

    @BeforeEach
    void setUp() {
        // Initialize the exist list with a test object
        exist = new ArrayList<>();
        Exist existObj = new Exist();
        existObj.setEmcUid("TEST_EMC_UID");
        existObj.setSysCategory('X');
        existObj.setSysType("XX");
        existObj.setSysNo("0001");
        existObj.setSysSuffix("000");
        existObj.setEastGrid(12345);
        existObj.setNorthGrid(67890);
        existObj.setSubDistrict("ABC");
        existObj.setStationType('X');
        existObj.setDesenAttDb(12.34);
        existObj.setIntmodAttDb(56.78);
        existObj.setAntenna("YZ");
        existObj.setAntHeight(45);
        existObj.setPwDbw(12.34);
        existObj.setAzMaxRad(123);
        existObj.setAzMaxRadR(2.1467);
        existObj.setFeedLoss(123);
        existObj.setSfxFilter("FILTER123");
        existObj.setHeightAsl(100.0);
        existObj.setDistType('U');
        existObj.setNoiseCode(1);
        existObj.setDistIndex(2);
        exist.add(existObj);
        
        // Initialize the prop object
        prop = new Propose();
        
        // Initialize the emcUid
        emcUid = "";
        
        // Set the fields in the service using reflection
        ReflectionTestUtils.setField(emcAnalysisService, "exist", exist);
        ReflectionTestUtils.setField(emcAnalysisService, "prop", prop);
        ReflectionTestUtils.setField(emcAnalysisService, "emcUid", emcUid);
    }

    @Test
    void testCopyExistToProp() throws Exception {
        // Execute
        // Call the private method using reflection
        java.lang.reflect.Method method = EmcAnalysisService.class.getDeclaredMethod("copyExistToProp", int.class);
        method.setAccessible(true);
        method.invoke(emcAnalysisService, 0);
        
        // Get the updated values
        prop = (Propose) ReflectionTestUtils.getField(emcAnalysisService, "prop");
        emcUid = (String) ReflectionTestUtils.getField(emcAnalysisService, "emcUid");
        
        // Verify
        assertEquals("TEST_EMC_UID", emcUid);
        assertEquals('X', prop.getSysCategory());
        assertEquals("XX", prop.getSysType());
        assertEquals("0001", prop.getSysNo());
        assertEquals("000", prop.getSysSuffix());
        assertEquals(12345, prop.getEastGrid());
        assertEquals(67890, prop.getNorthGrid());
        assertEquals("ABC", prop.getSubDistrict());
        assertEquals('X', prop.getStationType());
        assertEquals(12.34, prop.getDesenAttDb());
        assertEquals(56.78, prop.getIntmodAttDb());
        assertEquals("YZ", prop.getAntenna());
        assertEquals(45, prop.getAntHeight());
        assertEquals(12.34, prop.getPwDbw());
        assertEquals(123, prop.getAzMaxRad());
        assertEquals(2.1467, prop.getAzMaxRadR());
        assertEquals(123, prop.getFeedLoss());
        assertEquals("FILTER123", prop.getSfxFilter());
        assertEquals(100.0, prop.getHeightAsl());
        assertEquals('U', prop.getDistType());
        assertEquals(1, prop.getNoiseCode());
        assertEquals(2, prop.getDistIndex());
    }
}
