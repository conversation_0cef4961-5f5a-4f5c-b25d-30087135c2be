package com.emc.service.impl;

import com.emc.constants.EmcConstants;
import com.emc.model.CullingFrequency;
import com.emc.model.Exist;
import com.emc.model.ExistFreq;
import com.emc.model.Propose;
import com.emc.service.IntermodulationService;
import com.emc.service.ReferenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * Implementation of the IntermodulationService interface.
 * This is a translation of the intermod_2 and intermod_3 functions from the original C++ code.
 */
@Service
@Slf4j
public class IntermodulationServiceImpl implements IntermodulationService {

    private static final double FREQ_EPSILON = 0.0001;
    private static final int INTERMOD_LINES = 28;

    @Autowired
    private ReferenceService referenceService;

    @Value("${emc.summary.directory}")
    private String summaryDir;

    // These would be injected in a real application
    private Propose prop;
    private List<Exist> exist;
    private List<ExistFreq> fqList;
    private double propTxFreq;
    private double propRxFreq;
    private int propTxChannel;
    private int propRxChannel;
    private int fqCnt;
    private int cullStnCnt;
    private int intmod2VictTot;
    private int intmod2Tx1Tot;
    private int intmod2Tx2Tot;
    private int intmod3VictTot;
    private int intmod3Tx1Tot;
    private int intmod3Tx3Tot;
    private PrintWriter afp; // Audit file pointer

    @Override
    public void intermod2() {
        log.info("Performing 2-signal intermodulation analysis");

        try {
            // Create the 2-signal intermodulation analysis file
            String im2Fname = createIntermod2File();

            // Open the file for writing
            try (PrintWriter ifp = new PrintWriter(new FileWriter(im2Fname))) {
                // Write the header
                writeIntermod2Header(ifp);

                // Perform 2-signal intermodulation analysis
                performIntermod2Analysis(ifp);

                // Append the intermodulation file to the audit file
                appendToAuditFile(im2Fname);

                log.info("2-signal intermodulation analysis completed successfully");
            }
        } catch (Exception e) {
            log.error("Error performing 2-signal intermodulation analysis", e);
        }
    }

    @Override
    public void intermod3() {
        log.info("Performing 3-signal intermodulation analysis");

        try {
            // Create the 3-signal intermodulation analysis file
            String im3Fname = createIntermod3File();

            // Open the file for writing
            try (PrintWriter ifp = new PrintWriter(new FileWriter(im3Fname))) {
                // Write the header
                writeIntermod3Header(ifp);

                // Perform 3-signal intermodulation analysis
                performIntermod3Analysis(ifp);

                // Append the intermodulation file to the audit file
                appendToAuditFile(im3Fname);

                log.info("3-signal intermodulation analysis completed successfully");
            }
        } catch (Exception e) {
            log.error("Error performing 3-signal intermodulation analysis", e);
        }
    }

    /**
     * Creates the 2-signal intermodulation analysis file.
     *
     * @return The filename of the created file
     */
    private String createIntermod2File() {
        String filename = String.format("%s/intermod2/%.5f.%s",
            summaryDir, propTxFreq, getCurrentTimeString());

        // Create directory if it doesn't exist
        Path dir = Paths.get(summaryDir, "intermod2");
        try {
            Files.createDirectories(dir);
        } catch (IOException e) {
            log.error("Error creating intermod2 directory", e);
        }

        return filename;
    }

    /**
     * Creates the 3-signal intermodulation analysis file.
     *
     * @return The filename of the created file
     */
    private String createIntermod3File() {
        String filename = String.format("%s/intermod3/%.5f.%s",
            summaryDir, propTxFreq, getCurrentTimeString());

        // Create directory if it doesn't exist
        Path dir = Paths.get(summaryDir, "intermod3");
        try {
            Files.createDirectories(dir);
        } catch (IOException e) {
            log.error("Error creating intermod3 directory", e);
        }

        return filename;
    }

    /**
     * Writes the header for 2-signal intermodulation analysis file.
     *
     * @param ifp The PrintWriter for the intermodulation file
     */
    private void writeIntermod2Header(PrintWriter ifp) {
        ifp.println("                                2-SIGNAL INTERMODULATION ANALYSIS");
        ifp.println();
        ifp.println("   VICTIM STATION                                   TX1 STATION                                   TX2 STATION");
        ifp.println("   SYSTEM ID        FREQ    CHANNEL  GRID    POWER  SYSTEM ID        FREQ    CHANNEL  GRID    POWER  SYSTEM ID        FREQ    CHANNEL  GRID    POWER");
        ifp.println("   --------------   -----   -------  -----   -----  --------------   -----   -------  -----   -----  --------------   -----   -------  -----   -----");
    }

    /**
     * Writes the header for 3-signal intermodulation analysis file.
     *
     * @param ifp The PrintWriter for the intermodulation file
     */
    private void writeIntermod3Header(PrintWriter ifp) {
        ifp.println("                                3-SIGNAL INTERMODULATION ANALYSIS");
        ifp.println();
        ifp.println("   VICTIM STATION                                   TX1 STATION                                   TX2 STATION                                   TX3 STATION");
        ifp.println("   SYSTEM ID        FREQ    CHANNEL  GRID    POWER  SYSTEM ID        FREQ    CHANNEL  GRID    POWER  SYSTEM ID        FREQ    CHANNEL  GRID    POWER  SYSTEM ID        FREQ    CHANNEL  GRID    POWER");
        ifp.println("   --------------   -----   -------  -----   -----  --------------   -----   -------  -----   -----  --------------   -----   -------  -----   -----  --------------   -----   -------  -----   -----");
    }

    /**
     * Performs 2-signal intermodulation analysis.
     * This is a translation of the intermod_2 function from the original C++ code.
     *
     * @param ifp The PrintWriter for the intermodulation file
     */
    private void performIntermod2Analysis(PrintWriter ifp) {
        log.info("Performing 2-signal intermodulation analysis");

        // Reset counters
        intmod2VictTot = 0;
        intmod2Tx1Tot = 0;
        intmod2Tx2Tot = 0;

        // Get culling frequency from reference data
        CullingFrequency cullingFreq = referenceService.getCullingFrequency();
        // Use a default culling channel value since it's not in the CullingFrequency model
        int cullChannel = 50; // Default value based on typical EMC analysis

        // Perform analysis for proposed station as victim
        performIntermod2VictimAnalysis(ifp, cullChannel);

        // Perform analysis for existing stations as victims
        performIntermod2ExistingVictimAnalysis(ifp, cullChannel);

        log.info("2-signal intermodulation analysis completed. Victims: {}, TX1: {}, TX2: {}",
                intmod2VictTot, intmod2Tx1Tot, intmod2Tx2Tot);
    }

    /**
     * Performs 3-signal intermodulation analysis.
     * This is a translation of the intermod_3 function from the original C++ code.
     *
     * @param ifp The PrintWriter for the intermodulation file
     */
    private void performIntermod3Analysis(PrintWriter ifp) {
        log.info("Performing 3-signal intermodulation analysis");

        // Reset counters
        intmod3VictTot = 0;
        intmod3Tx1Tot = 0;
        intmod3Tx3Tot = 0;

        // Get culling frequency from reference data
        CullingFrequency cullingFreq = referenceService.getCullingFrequency();
        // Use a default culling channel value since it's not in the CullingFrequency model
        int cullChannel = 50; // Default value based on typical EMC analysis

        // Perform analysis for proposed station as victim
        performIntermod3VictimAnalysis(ifp, cullChannel);

        // Perform analysis for existing stations as victims
        performIntermod3ExistingVictimAnalysis(ifp, cullChannel);

        log.info("3-signal intermodulation analysis completed. Victims: {}, TX1: {}, TX3: {}",
                intmod3VictTot, intmod3Tx1Tot, intmod3Tx3Tot);
    }

    /**
     * Performs 2-signal intermodulation analysis with proposed station as victim.
     *
     * @param ifp The PrintWriter for the intermodulation file
     * @param cullChannel The culling channel threshold
     */
    private void performIntermod2VictimAnalysis(PrintWriter ifp, int cullChannel) {
        // This would contain the actual intermodulation calculation logic
        // For now, we'll implement a simplified version

        if (fqList == null || fqList.isEmpty()) {
            return;
        }

        // Iterate through frequency combinations for 2-signal intermodulation
        for (int i = 0; i < fqCnt; i++) {
            if (fqList.get(i).getStnNode() == prop.getStnNode()) {
                continue;
            }

            for (int j = i + 1; j < fqCnt; j++) {
                if (fqList.get(j).getStnNode() == prop.getStnNode()) {
                    continue;
                }

                // Check channel difference for culling
                int channelDiff1 = Math.abs(propRxChannel - fqList.get(i).getTxChannel());
                int channelDiff2 = Math.abs(propRxChannel - fqList.get(j).getTxChannel());

                if (channelDiff1 > cullChannel || channelDiff2 > cullChannel) {
                    continue;
                }

                // Calculate intermodulation frequency
                double intermodFreq = calculateIntermod2Frequency(
                    fqList.get(i).getTxFreq(), fqList.get(j).getTxFreq());

                // Check if intermodulation frequency matches victim frequency
                if (Math.abs(intermodFreq - propRxFreq) < FREQ_EPSILON) {
                    // Write intermodulation result
                    writeIntermod2Result(ifp, i, j, true);
                    intmod2VictTot++;
                }
            }
        }
    }

    /**
     * Performs 2-signal intermodulation analysis with existing stations as victims.
     *
     * @param ifp The PrintWriter for the intermodulation file
     * @param cullChannel The culling channel threshold
     */
    private void performIntermod2ExistingVictimAnalysis(PrintWriter ifp, int cullChannel) {
        // Similar logic to victim analysis but for existing stations
        // This is a simplified implementation

        if (fqList == null || fqList.isEmpty()) {
            return;
        }

        for (int victim = 0; victim < fqCnt; victim++) {
            if (fqList.get(victim).getStnNode() == prop.getStnNode()) {
                continue;
            }

            for (int i = 0; i < fqCnt; i++) {
                if (i == victim || fqList.get(i).getStnNode() == fqList.get(victim).getStnNode()) {
                    continue;
                }

                for (int j = i + 1; j < fqCnt; j++) {
                    if (j == victim || fqList.get(j).getStnNode() == fqList.get(victim).getStnNode()) {
                        continue;
                    }

                    // Check channel difference for culling
                    int channelDiff1 = Math.abs(fqList.get(victim).getRxChannel() - fqList.get(i).getTxChannel());
                    int channelDiff2 = Math.abs(fqList.get(victim).getRxChannel() - fqList.get(j).getTxChannel());

                    if (channelDiff1 > cullChannel || channelDiff2 > cullChannel) {
                        continue;
                    }

                    // Calculate intermodulation frequency
                    double intermodFreq = calculateIntermod2Frequency(
                        fqList.get(i).getTxFreq(), fqList.get(j).getTxFreq());

                    // Check if intermodulation frequency matches victim frequency
                    if (Math.abs(intermodFreq - fqList.get(victim).getRxFreq()) < FREQ_EPSILON) {
                        // Write intermodulation result
                        writeIntermod2Result(ifp, i, j, false);
                        intmod2VictTot++;
                    }
                }
            }
        }
    }

    /**
     * Calculates the 2-signal intermodulation frequency.
     * Formula: 2*f1 - f2 or 2*f2 - f1
     *
     * @param freq1 First frequency
     * @param freq2 Second frequency
     * @return The intermodulation frequency
     */
    private double calculateIntermod2Frequency(double freq1, double freq2) {
        // Calculate both possible intermodulation products
        double intermod1 = 2 * freq1 - freq2;
        double intermod2 = 2 * freq2 - freq1;

        // Return the positive frequency that's closer to the victim frequency
        if (intermod1 > 0 && intermod2 > 0) {
            return Math.abs(intermod1 - propRxFreq) < Math.abs(intermod2 - propRxFreq) ? intermod1 : intermod2;
        } else if (intermod1 > 0) {
            return intermod1;
        } else if (intermod2 > 0) {
            return intermod2;
        }

        return 0.0; // No valid intermodulation frequency
    }

    /**
     * Calculates the 3-signal intermodulation frequency.
     * Formula: f1 + f2 - f3 (and other combinations)
     *
     * @param freq1 First frequency
     * @param freq2 Second frequency
     * @param freq3 Third frequency
     * @return The intermodulation frequency
     */
    private double calculateIntermod3Frequency(double freq1, double freq2, double freq3) {
        // Calculate possible 3rd order intermodulation products
        double intermod1 = freq1 + freq2 - freq3;
        double intermod2 = freq1 + freq3 - freq2;
        double intermod3 = freq2 + freq3 - freq1;

        // Return the positive frequency that's closest to the victim frequency
        double bestFreq = 0.0;
        double minDiff = Double.MAX_VALUE;

        if (intermod1 > 0) {
            double diff = Math.abs(intermod1 - propRxFreq);
            if (diff < minDiff) {
                minDiff = diff;
                bestFreq = intermod1;
            }
        }

        if (intermod2 > 0) {
            double diff = Math.abs(intermod2 - propRxFreq);
            if (diff < minDiff) {
                minDiff = diff;
                bestFreq = intermod2;
            }
        }

        if (intermod3 > 0) {
            double diff = Math.abs(intermod3 - propRxFreq);
            if (diff < minDiff) {
                minDiff = diff;
                bestFreq = intermod3;
            }
        }

        return bestFreq;
    }

    /**
     * Writes a 2-signal intermodulation result to the file.
     *
     * @param ifp The PrintWriter for the intermodulation file
     * @param tx1Index Index of first transmitter
     * @param tx2Index Index of second transmitter
     * @param isProposedVictim Whether the proposed station is the victim
     */
    private void writeIntermod2Result(PrintWriter ifp, int tx1Index, int tx2Index, boolean isProposedVictim) {
        if (isProposedVictim) {
            // Proposed station is victim
            ifp.printf("   PROPOSED         %8.3f %7d  %05d   %5.1f  %-14s %8.3f %7d  %05d   %5.1f  %-14s %8.3f %7d  %05d   %5.1f%n",
                propRxFreq, propRxChannel, 0, 0.0,
                getSystemId(fqList.get(tx1Index).getStnNode()),
                fqList.get(tx1Index).getTxFreq(), fqList.get(tx1Index).getTxChannel(), 0, 0.0,
                getSystemId(fqList.get(tx2Index).getStnNode()),
                fqList.get(tx2Index).getTxFreq(), fqList.get(tx2Index).getTxChannel(), 0, 0.0);
        } else {
            // Existing station is victim - simplified for now
            ifp.printf("   %-14s   %8.3f %7d  %05d   %5.1f  %-14s %8.3f %7d  %05d   %5.1f  %-14s %8.3f %7d  %05d   %5.1f%n",
                "EXISTING", 0.0, 0, 0, 0.0,
                getSystemId(fqList.get(tx1Index).getStnNode()),
                fqList.get(tx1Index).getTxFreq(), fqList.get(tx1Index).getTxChannel(), 0, 0.0,
                getSystemId(fqList.get(tx2Index).getStnNode()),
                fqList.get(tx2Index).getTxFreq(), fqList.get(tx2Index).getTxChannel(), 0, 0.0);
        }

        intmod2Tx1Tot++;
        intmod2Tx2Tot++;
    }

    /**
     * Gets a system ID string for a station node.
     *
     * @param stnNode The station node index
     * @return The system ID string
     */
    private String getSystemId(int stnNode) {
        if (exist != null && stnNode < exist.size()) {
            Exist station = exist.get(stnNode);
            return String.format("%c%s%s-%s",
                station.getSysCategory(),
                station.getSysType(),
                station.getSysNo(),
                station.getSysSuffix());
        }
        return "UNKNOWN";
    }

    /**
     * Performs 3-signal intermodulation analysis with proposed station as victim.
     *
     * @param ifp The PrintWriter for the intermodulation file
     * @param cullChannel The culling channel threshold
     */
    private void performIntermod3VictimAnalysis(PrintWriter ifp, int cullChannel) {
        // This would contain the actual 3-signal intermodulation calculation logic
        // For now, we'll implement a simplified version

        if (fqList == null || fqList.size() < 3) {
            return;
        }

        // Iterate through frequency combinations for 3-signal intermodulation
        for (int i = 0; i < fqCnt; i++) {
            if (fqList.get(i).getStnNode() == prop.getStnNode()) {
                continue;
            }

            for (int j = i + 1; j < fqCnt; j++) {
                if (fqList.get(j).getStnNode() == prop.getStnNode()) {
                    continue;
                }

                for (int k = j + 1; k < fqCnt; k++) {
                    if (fqList.get(k).getStnNode() == prop.getStnNode()) {
                        continue;
                    }

                    // Check channel difference for culling
                    int channelDiff1 = Math.abs(propRxChannel - fqList.get(i).getTxChannel());
                    int channelDiff2 = Math.abs(propRxChannel - fqList.get(j).getTxChannel());
                    int channelDiff3 = Math.abs(propRxChannel - fqList.get(k).getTxChannel());

                    if (channelDiff1 > cullChannel || channelDiff2 > cullChannel || channelDiff3 > cullChannel) {
                        continue;
                    }

                    // Calculate intermodulation frequency
                    double intermodFreq = calculateIntermod3Frequency(
                        fqList.get(i).getTxFreq(), fqList.get(j).getTxFreq(), fqList.get(k).getTxFreq());

                    // Check if intermodulation frequency matches victim frequency
                    if (Math.abs(intermodFreq - propRxFreq) < FREQ_EPSILON) {
                        // Write intermodulation result
                        writeIntermod3Result(ifp, i, j, k, true);
                        intmod3VictTot++;
                    }
                }
            }
        }
    }

    /**
     * Performs 3-signal intermodulation analysis with existing stations as victims.
     *
     * @param ifp The PrintWriter for the intermodulation file
     * @param cullChannel The culling channel threshold
     */
    private void performIntermod3ExistingVictimAnalysis(PrintWriter ifp, int cullChannel) {
        // Similar logic to victim analysis but for existing stations
        // This is a simplified implementation

        if (fqList == null || fqList.size() < 3) {
            return;
        }

        for (int victim = 0; victim < fqCnt; victim++) {
            if (fqList.get(victim).getStnNode() == prop.getStnNode()) {
                continue;
            }

            for (int i = 0; i < fqCnt; i++) {
                if (i == victim || fqList.get(i).getStnNode() == fqList.get(victim).getStnNode()) {
                    continue;
                }

                for (int j = i + 1; j < fqCnt; j++) {
                    if (j == victim || fqList.get(j).getStnNode() == fqList.get(victim).getStnNode()) {
                        continue;
                    }

                    for (int k = j + 1; k < fqCnt; k++) {
                        if (k == victim || fqList.get(k).getStnNode() == fqList.get(victim).getStnNode()) {
                            continue;
                        }

                        // Check channel difference for culling
                        int channelDiff1 = Math.abs(fqList.get(victim).getRxChannel() - fqList.get(i).getTxChannel());
                        int channelDiff2 = Math.abs(fqList.get(victim).getRxChannel() - fqList.get(j).getTxChannel());
                        int channelDiff3 = Math.abs(fqList.get(victim).getRxChannel() - fqList.get(k).getTxChannel());

                        if (channelDiff1 > cullChannel || channelDiff2 > cullChannel || channelDiff3 > cullChannel) {
                            continue;
                        }

                        // Calculate intermodulation frequency
                        double intermodFreq = calculateIntermod3Frequency(
                            fqList.get(i).getTxFreq(), fqList.get(j).getTxFreq(), fqList.get(k).getTxFreq());

                        // Check if intermodulation frequency matches victim frequency
                        if (Math.abs(intermodFreq - fqList.get(victim).getRxFreq()) < FREQ_EPSILON) {
                            // Write intermodulation result
                            writeIntermod3Result(ifp, i, j, k, false);
                            intmod3VictTot++;
                        }
                    }
                }
            }
        }
    }

    /**
     * Writes a 3-signal intermodulation result to the file.
     *
     * @param ifp The PrintWriter for the intermodulation file
     * @param tx1Index Index of first transmitter
     * @param tx2Index Index of second transmitter
     * @param tx3Index Index of third transmitter
     * @param isProposedVictim Whether the proposed station is the victim
     */
    private void writeIntermod3Result(PrintWriter ifp, int tx1Index, int tx2Index, int tx3Index, boolean isProposedVictim) {
        if (isProposedVictim) {
            // Proposed station is victim
            ifp.printf("   PROPOSED         %8.3f %7d  %05d   %5.1f  %-14s %8.3f %7d  %05d   %5.1f  %-14s %8.3f %7d  %05d   %5.1f  %-14s %8.3f %7d  %05d   %5.1f%n",
                propRxFreq, propRxChannel, 0, 0.0,
                getSystemId(fqList.get(tx1Index).getStnNode()),
                fqList.get(tx1Index).getTxFreq(), fqList.get(tx1Index).getTxChannel(), 0, 0.0,
                getSystemId(fqList.get(tx2Index).getStnNode()),
                fqList.get(tx2Index).getTxFreq(), fqList.get(tx2Index).getTxChannel(), 0, 0.0,
                getSystemId(fqList.get(tx3Index).getStnNode()),
                fqList.get(tx3Index).getTxFreq(), fqList.get(tx3Index).getTxChannel(), 0, 0.0);
        } else {
            // Existing station is victim - simplified for now
            ifp.printf("   %-14s   %8.3f %7d  %05d   %5.1f  %-14s %8.3f %7d  %05d   %5.1f  %-14s %8.3f %7d  %05d   %5.1f  %-14s %8.3f %7d  %05d   %5.1f%n",
                "EXISTING", 0.0, 0, 0, 0.0,
                getSystemId(fqList.get(tx1Index).getStnNode()),
                fqList.get(tx1Index).getTxFreq(), fqList.get(tx1Index).getTxChannel(), 0, 0.0,
                getSystemId(fqList.get(tx2Index).getStnNode()),
                fqList.get(tx2Index).getTxFreq(), fqList.get(tx2Index).getTxChannel(), 0, 0.0,
                getSystemId(fqList.get(tx3Index).getStnNode()),
                fqList.get(tx3Index).getTxFreq(), fqList.get(tx3Index).getTxChannel(), 0, 0.0);
        }

        intmod3Tx1Tot++;
        intmod3Tx3Tot++;
    }

    /**
     * Gets the current time string for file naming.
     *
     * @return The current time string in HHMMSS format
     */
    private String getCurrentTimeString() {
        return java.time.LocalTime.now().format(
            java.time.format.DateTimeFormatter.ofPattern("HHmmss"));
    }

    /**
     * Appends a file to the audit file.
     *
     * @param filename The filename to append
     */
    private void appendToAuditFile(String filename) {
        if (afp != null) {
            try {
                java.nio.file.Files.lines(Paths.get(filename))
                    .forEach(afp::println);
            } catch (IOException e) {
                log.error("Error appending file {} to audit file", filename, e);
            }
        }
    }

    // Setter methods for data injection
    @Override
    public void setProp(Propose prop) {
        this.prop = prop;
    }

    @Override
    public void setExist(List<Exist> exist) {
        this.exist = exist;
    }

    @Override
    public void setFqList(List<ExistFreq> fqList) {
        this.fqList = fqList;
    }

    @Override
    public void setPropTxFreq(double propTxFreq) {
        this.propTxFreq = propTxFreq;
    }

    @Override
    public void setPropRxFreq(double propRxFreq) {
        this.propRxFreq = propRxFreq;
    }

    @Override
    public void setAfp(PrintWriter afp) {
        this.afp = afp;
    }

    @Override
    public void setFqCnt(int fqCnt) {
        this.fqCnt = fqCnt;
    }

    // Getter methods for results
    @Override
    public int getIntmod2VictTot() {
        return intmod2VictTot;
    }

    @Override
    public int getIntmod2Tx1Tot() {
        return intmod2Tx1Tot;
    }

    @Override
    public int getIntmod2Tx2Tot() {
        return intmod2Tx2Tot;
    }

    @Override
    public int getIntmod3VictTot() {
        return intmod3VictTot;
    }

    @Override
    public int getIntmod3Tx1Tot() {
        return intmod3Tx1Tot;
    }

    @Override
    public int getIntmod3Tx3Tot() {
        return intmod3Tx3Tot;
    }
}
