/**********************************************************************/
/*                                                                    */
/*    Module Name   :  get_diffract_loss (esemdl0x.c)                 */  
/*    Date Written  :  April, 1993                                    */
/*    Author        :  <PERSON><PERSON> <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  base_to_base (esemin0x.pc)                     */  
/*                                                                    */
/*    Parameters    :  tx_grid (grid of Tx station)                   */
/*                     tx_ant_height (antenna ht of Tx station)       */
/*                     rx_grid (grid of Rx station)                   */
/*                     rx_ant_height (antenna ht of Rx station)       */
/*                                                                    */
/*    Called Modules:  get_terrain_profile (esemtn0x.pc)              */
/*                                                                    */
/*    Purpose       :  Calulate the diffraction loss from Tx station  */
/*                     to Rx station.                                 */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <math.h>
#include "../include/macros.h"
#include "../include/define.h"
#include "../include/emcext.h"
#include "../include/trainext.h"
#include "../include/propext.h"
#include "../include/existext.h"
#include "../include/victext.h"
#include "../include/intrext.h"
#include "../include/presuext.h"

//#include "../include/presul.h"


float get_diffract_loss(float*, float, float*, float);
int get_terrain_profile(float *, float *);

float get_diffract_loss(tx_grid, tx_ant_height, rx_grid, rx_ant_height)
float tx_grid[]; 
float tx_ant_height;
float rx_grid[];
float rx_ant_height;
{
    double  v;
    float   elev_angle;
    float   tx_tan, rx_tan;
    float   max_tx_tan, max_rx_tan;
    float   diff_loss;
    int     i, j;
    static  float   wave_len[] = { 3.75, 2.0, 0.75, 0.375 };

	
	

/*
#ifdef DEBUG
    printf("get_diffract_loss\n");
#endif

printf("tx_grid: %d %d, prev_rx_grid: %d %d, rx_grid: %d %d, prev_tx_grid %d %d\n",
    (int)tx_grid[0],(int)tx_grid[1],(int)prev_rx_grid[0],(int)prev_rx_grid[1],
    (int)rx_grid[0],(int)rx_grid[1],(int)prev_tx_grid[0],(int)prev_tx_grid[1]);
*/

/**********************************************************************/
/*    if prev Tx station and Rx station exchange their roles,         */
/*    then the diffraction loss makes no change. Therefore, just      */
/*    return prev diffraction loss is enough                          */
/**********************************************************************/

    if (((int)tx_grid[0] == (int)prev_rx_grid[0]) 
    &&  ((int)tx_grid[1] == (int)prev_rx_grid[1])
    &&  ((int)rx_grid[0] == (int)prev_tx_grid[0]) 
    &&  ((int)rx_grid[1] == (int)prev_tx_grid[1]))
	return(prev_diff_loss);

    get_terrain_profile(tx_grid, rx_grid);

    if (n_points > 1)
    {

        elev_angle = (rx_ant_height - tx_ant_height) / delta_dist;
/*
printf("n_pts elev_angle rx_ant_ht tx_ant_ht delta_dist: %d %f %f %f %f\n", 
       n_points,elev_angle,rx_ant_height,tx_ant_height, delta_dist);
*/

        max_tx_tan = (height[0] - tx_ant_height) / dist[0];
        max_rx_tan = 
            (height[n_points-2]-rx_ant_height) / (delta_dist-dist[n_points-2]);
/*
printf("0 max_tx_tan height dist: %f %f %f\n", max_tx_tan,height[0],dist[0]);
{
float a, b, c;
a= height[n_points-2]-rx_ant_height;
b= delta_dist-dist[n_points-2];
c = a/b;
        max_rx_tan = a/b;
printf("%d a b c max_rx_tan height dist: %f %f %f %f %f %f\n", 
n_points-2,a,b,c,max_rx_tan,height[n_points-2],dist[n_points-2]);
}
*/

        for (i = 1, j = n_points-3; i < n_points-1; i++, j--)
        {
			
            tx_tan = (height[i] - tx_ant_height) / dist[i];
            rx_tan = (height[j] - rx_ant_height) / (delta_dist - dist[j]);
            max_tx_tan = max(max_tx_tan, tx_tan);
            max_rx_tan = max(max_rx_tan, rx_tan); 
/*
printf("%d max_tx_tan height dist: %f %f %f\n", i,max_tx_tan,height[i],dist[i]);
printf("%d max_rx_tan height dist: %f %f %f\n", j,max_rx_tan,height[j],dist[j]);
*/
        }

        tx_tan = max_tx_tan - elev_angle;
        rx_tan = max_rx_tan + elev_angle;

/*
printf("max_tx_tan max_rx_tan tx_tan rx_tan: %f %f %f %f\n", 
       max_tx_tan,max_rx_tan,tx_tan,rx_tan);
*/
        if ((tx_tan >= 0.0) || (rx_tan >= 0.0))
        {
			
            v = 2.0 * delta_dist * tx_tan * rx_tan / wave_len[prop->band];
/*
printf("v: %f\n", v);
*/
            if (v < 0.0)
            {
                fprintf(afp, "Error: -ve v in diffraction loss calculation\n");
                fprintf(afp, "proposed grid: %d(N) %d(E)\n", prop->north_grid, 
                        prop->east_grid);
                fprintf(afp, "culling station system id.: %c-%s-%s-%s\n",
                        exist[e_idx]->sys_category, exist[e_idx]->sys_type,
                        exist[e_idx]->sys_no, exist[e_idx]->sys_suffix);
                fprintf(afp, "grid: %d(N) %d(E)\n", exist[e_idx]->north_grid, 
                        exist[e_idx]->east_grid);
                exit(1);
            }

            v = sqrt(v);

/*
            if (v > 1.0)
                diff_loss = 13.0 + 10.0 * log10(v) + CLEARANCE_LOSS;
            if ((v > 0.0) && (v <= 1.0))
                diff_loss = 6.0 + 7.0 * v + CLEARANCE_LOSS;
            if (v == 0.0)
                diff_loss = 6.0 + CLEARANCE_LOSS;
*/
            /******************************/
            /* refer to CCIR Report 715-3 */
            /******************************/
            if (v > 1.0)
                diff_loss = 13.0 + 20.0 * log10(v);
            if ((v > 0.0) && (v <= 1.0))
                diff_loss = 6.0 + 7.0 * v;
            if (v == 0.0)
                diff_loss = 6.0;
        }
        else
            diff_loss = 0.0;
    }
    else
        diff_loss = 0.0; 
        

    prev_diff_loss = diff_loss;
    prev_tx_grid[0] = tx_grid[0];
    prev_tx_grid[1] = tx_grid[1];
    prev_rx_grid[0] = rx_grid[0];
    prev_rx_grid[1] = rx_grid[1];

    return(diff_loss);
}
