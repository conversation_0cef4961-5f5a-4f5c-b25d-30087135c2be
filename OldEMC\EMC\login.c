
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,0,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[9];
};
static const struct sqlcxp sqlfpn =
{
    8,
    "login.pc"
};


static unsigned int sqlctx = 19259;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[4];
   unsigned long  sqhstl[4];
            int   sqhsts[4];
            short *sqindv[4];
            int   sqinds[4];
   unsigned long  sqharm[4];
   unsigned long  *sqharc[4];
   unsigned short  sqadto[4];
   unsigned short  sqtdso[4];
} sqlstm = {13,4};

/* SQLLIB Prototypes */
extern void sqlcxt (void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlcx2t(void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlbuft(void **, char *);
extern void sqlgs2t(void **, char *);
extern void sqlorat(void **, unsigned int *, void *);

/* Forms Interface */
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern void sqliem(unsigned char *, signed int *);

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4242,1,0,0,
5,0,0,0,0,0,27,88,0,0,4,4,0,1,0,1,9,0,0,1,9,0,0,1,10,0,0,1,10,0,0,
36,0,0,2,0,0,30,140,0,0,0,0,0,1,0,
51,0,0,3,72,0,4,200,0,0,3,1,0,1,0,2,1,0,0,2,9,0,0,1,9,0,0,
};


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  user_login (login.pc)                          */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  esemcn0f.c, esembe0f.pc, esemcs0f.pc,          */  
/*                     esemns0f.pc, esemsc0f.pc, esemvc0f.pc,         */  
/*                     esemct0x.pc                                    */  
/*                                                                    */
/*    Parameters    :  user id.                                       */
/*                  :  user password                                  */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  login ORACLE database using user id. and       */
/*                     password from parameter list.                  */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>

#ifndef TRUE
#define	TRUE     1
#endif

#ifndef FALSE
#define	FALSE    0
#endif

#ifndef FOUND
#define FOUND    0
#endif

#ifndef NOT_FOUND
#define  NOT_FOUND      1403
#endif

#ifndef LOGIN_OK
#define LOGIN_OK    0
#endif

#define  TRAIL          0
#define  LEADING        1

char msg[180];

/* EXEC SQL BEGIN DECLARE SECTION; */ 

    char    o_dummy;
    char    o_user_type;
    /* VARCHAR o_sys_type[3]; */ 
struct { unsigned short len; unsigned char arr[3]; } o_sys_type;

    /* VARCHAR o_uid[20]; */ 
struct { unsigned short len; unsigned char arr[20]; } o_uid;

    /* VARCHAR o_pwd[20]; */ 
struct { unsigned short len; unsigned char arr[20]; } o_pwd;

    /* VARCHAR o_level_2_uid[21]; */ 
struct { unsigned short len; unsigned char arr[21]; } o_level_2_uid;

    /* VARCHAR o_level_2_pwd[21]; */ 
struct { unsigned short len; unsigned char arr[21]; } o_level_2_pwd;

/* EXEC SQL END DECLARE SECTION; */ 

    
/* EXEC SQL INCLUDE SQLCA;
 */ 
/*
 * $Header: sqlca.h 24-apr-2003.12:50:58 mkandarp Exp $ sqlca.h 
 */

/* Copyright (c) 1985, 2003, Oracle Corporation.  All rights reserved.  */
 
/*
NAME
  SQLCA : SQL Communications Area.
FUNCTION
  Contains no code. Oracle fills in the SQLCA with status info
  during the execution of a SQL stmt.
NOTES
  **************************************************************
  ***                                                        ***
  *** This file is SOSD.  Porters must change the data types ***
  *** appropriately on their platform.  See notes/pcport.doc ***
  *** for more information.                                  ***
  ***                                                        ***
  **************************************************************

  If the symbol SQLCA_STORAGE_CLASS is defined, then the SQLCA
  will be defined to have this storage class. For example:
 
    #define SQLCA_STORAGE_CLASS extern
 
  will define the SQLCA as an extern.
 
  If the symbol SQLCA_INIT is defined, then the SQLCA will be
  statically initialized. Although this is not necessary in order
  to use the SQLCA, it is a good pgming practice not to have
  unitialized variables. However, some C compilers/OS's don't
  allow automatic variables to be init'd in this manner. Therefore,
  if you are INCLUDE'ing the SQLCA in a place where it would be
  an automatic AND your C compiler/OS doesn't allow this style
  of initialization, then SQLCA_INIT should be left undefined --
  all others can define SQLCA_INIT if they wish.

  If the symbol SQLCA_NONE is defined, then the SQLCA variable will
  not be defined at all.  The symbol SQLCA_NONE should not be defined
  in source modules that have embedded SQL.  However, source modules
  that have no embedded SQL, but need to manipulate a sqlca struct
  passed in as a parameter, can set the SQLCA_NONE symbol to avoid
  creation of an extraneous sqlca variable.
 
MODIFIED
    lvbcheng   07/31/98 -  long to int
    jbasu      12/12/94 -  Bug 217878: note this is an SOSD file
    losborne   08/11/92 -  No sqlca var if SQLCA_NONE macro set 
  Clare      12/06/84 - Ch SQLCA to not be an extern.
  Clare      10/21/85 - Add initialization.
  Bradbury   01/05/86 - Only initialize when SQLCA_INIT set
  Clare      06/12/86 - Add SQLCA_STORAGE_CLASS option.
*/
 
#ifndef SQLCA
#define SQLCA 1
 
struct   sqlca
         {
         /* ub1 */ char    sqlcaid[8];
         /* b4  */ int     sqlabc;
         /* b4  */ int     sqlcode;
         struct
           {
           /* ub2 */ unsigned short sqlerrml;
           /* ub1 */ char           sqlerrmc[70];
           } sqlerrm;
         /* ub1 */ char    sqlerrp[8];
         /* b4  */ int     sqlerrd[6];
         /* ub1 */ char    sqlwarn[8];
         /* ub1 */ char    sqlext[8];
         };

#ifndef SQLCA_NONE 
#ifdef   SQLCA_STORAGE_CLASS
SQLCA_STORAGE_CLASS struct sqlca sqlca
#else
         struct sqlca sqlca
#endif
 
#ifdef  SQLCA_INIT
         = {
         {'S', 'Q', 'L', 'C', 'A', ' ', ' ', ' '},
         sizeof(struct sqlca),
         0,
         { 0, {0}},
         {'N', 'O', 'T', ' ', 'S', 'E', 'T', ' '},
         {0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0}
         }
#endif
         ;
#endif
 
#endif
 
/* end SQLCA */





int strip_blank   (char *,char *);	/*20170614 Cyrus [Add] */





int user_login(p_uid, p_pwd, err_msg)				/*20170614 Cyrus [Add][int] */
char    *p_uid, *p_pwd;
char    *err_msg;
{

/*    strcpy(o_uid.arr, p_uid);
    strcpy(o_pwd.arr, p_pwd); */
    strcpy((char *)o_uid.arr, p_uid);
    strcpy((char *)o_pwd.arr, p_pwd);
    o_uid.len = strlen(p_uid);
    o_pwd.len = strlen(p_pwd);

    /* EXEC SQL CONNECT :o_uid IDENTIFIED BY :o_pwd; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 4;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )10;
    sqlstm.offset = (unsigned int  )5;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_uid;
    sqlstm.sqhstl[0] = (unsigned long )22;
    sqlstm.sqhsts[0] = (         int  )22;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_pwd;
    sqlstm.sqhstl[1] = (unsigned long )22;
    sqlstm.sqhsts[1] = (         int  )22;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlstm.sqlcmax = (unsigned int )100;
    sqlstm.sqlcmin = (unsigned int )2;
    sqlstm.sqlcincr = (unsigned int )1;
    sqlstm.sqlctimeout = (unsigned int )0;
    sqlstm.sqlcnowait = (unsigned int )0;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}




/*
#ifdef DEBUG
    printf("user_login\n");
#endif
*/

    if (sqlca.sqlcode != 0)
        strcpy(err_msg, sqlca.sqlerrm.sqlerrmc);

    return(sqlca.sqlcode);
}


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  user_logout (login.pc)                         */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  esemcn0f.c, esembe0f.pc, esemcs0f.pc,          */  
/*                     esemns0f.pc, esemsc0f.pc, esemvc0f.pc,         */  
/*                     esemct0x.pc                                    */  
/*                                                                    */
/*    Parameters    :  user id.                                       */
/*                  :  user password                                  */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  logout from ORACLE database.                   */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

int user_logout()						/*20170614 Cyrus [Add][int ] */			
{

    /* EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn; */ 



/*
#ifdef DEBUG
    printf("user_logout\n");
#endif
*/

    /* EXEC SQL COMMIT WORK RELEASE; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 4;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )36;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



/* changed by Chen Yung */
/* original: */
/*    return; */
    return(0);


sqlerr_rtn:
    printf("%s\n", sqlca.sqlerrm.sqlerrmc);
    exit(1);

}


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  emc_login (login.pc)                           */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  esemcn0f.c                                     */  
/*                                                                    */
/*    Parameters    :  user id.                                       */
/*                  :  user password                                  */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  This is an EMC procedure to determine whether  */
/*                     the login user name is an authorised EMC user. */
/*                     if yes, determine the class to which the user  */
/*                     belongs.                                       */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/


int emc_login(p_uid, p_pwd, user_type, err_msg)
char    *p_uid, *p_pwd;
char    *user_type;
char    *err_msg;

{
    
    /* EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn; */ 

    /* EXEC SQL WHENEVER NOT FOUND CONTINUE; */ 


/*    strcpy(o_level_2_uid.arr, p_uid); */
    strcpy((char *)o_level_2_uid.arr, p_uid);
    o_level_2_uid.len = strlen(p_uid);

/*
system("echo \"login 1\" >> /tmp/debug");
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_level_2_uid.arr );
system(msg);
*/
    /* EXEC SQL
         SELECT USER_TYPE, PASSWORD
         INTO   :o_user_type, :o_level_2_pwd
         FROM   EMC_AUTH
         WHERE  USER_ID = :o_level_2_uid; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 4;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select USER_TYPE ,PASSWORD into :b0,:b1  from EMC_AUTH wh\
ere USER_ID=:b2";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )51;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_user_type;
    sqlstm.sqhstl[0] = (unsigned long )1;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_level_2_pwd;
    sqlstm.sqhstl[1] = (unsigned long )23;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&o_level_2_uid;
    sqlstm.sqhstl[2] = (unsigned long )23;
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    if (sqlca.sqlcode == NOT_FOUND)
    {
	
	


        sprintf(err_msg, "User not authorised to access EMC"  );
        return (NOT_FOUND);
    }

    o_level_2_pwd.arr[o_level_2_pwd.len] = '\0';
/*    if (strcmp(p_pwd, o_level_2_pwd.arr)) */
    if (strcmp(p_pwd, (char *)o_level_2_pwd.arr))
    {
        sprintf(err_msg, "Incorrect user password");
        return (NOT_FOUND);
    }

    *user_type = o_user_type;

    return(LOGIN_OK);

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    return(sqlca.sqlcode);

/*
    strcpy(o_level_2_pwd.arr, p_pwd);
    o_level_2_pwd.len = strlen(p_pwd);

system("echo \"login 2\" >> /tmp/debug");
sprintf(msg, "echo \"%s %s\" >> /tmp/debug",
o_level_2_uid.arr, o_level_2_pwd.arr );
system(msg);

    EXEC SQL
         SELECT 'X'
         INTO   :o_dummy
         FROM   USER_PROFILE
         WHERE  USER_ID = :o_level_2_uid
         AND    PASSWD = :o_level_2_pwd;
        
    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "User not authorised to access EMC");
        return (sqlca.sqlcode);
    }

    strcpy(o_sys_type.arr, "EM");
    o_sys_type.len = strlen(o_sys_type.arr);

system("echo \"login 3\" >> /tmp/debug");
sprintf(msg, "echo \"%s %s\" >> /tmp/debug",
o_level_2_uid.arr, o_sys_type.arr
system(msg);

    EXEC SQL
         SELECT MAINT
         INTO   :o_maint
         FROM   USER_AUTH
         WHERE  USER_ID = :o_level_2_uid
         AND    SYS_TYPE = :o_sys_type;

    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "User not authorised to access EMC");
        return (sqlca.sqlcode);
    }

    if (o_maint == 'Y')
        *maint_flag = TRUE;
    else
        *maint_flag = FALSE;
*/
}
