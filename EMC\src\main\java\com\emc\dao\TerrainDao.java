package com.emc.dao;

import com.emc.model.Terrain;

import java.util.Optional;

/**
 * DAO interface for Terrain operations.
 * This matches the original ProC TERRAIN table queries.
 */
public interface TerrainDao extends BaseDao<Terrain, String> {
    
    /**
     * Finds terrain data by grid coordinates.
     * This matches the original ProC terrain height lookup.
     *
     * @param east East grid coordinate
     * @param north North grid coordinate
     * @return Optional containing terrain data if found
     */
    Optional<Terrain> findByGridCoordinates(int east, int north);
    
    /**
     * Finds terrain data within a bounding box.
     *
     * @param eastMin Minimum east coordinate
     * @param eastMax Maximum east coordinate
     * @param northMin Minimum north coordinate
     * @param northMax Maximum north coordinate
     * @return List of terrain points within the bounding box
     */
    java.util.List<Terrain> findInBoundingBox(int eastMin, int eastMax, int northMin, int northMax);
}
