#include "../include/refdef.h"

int     band_cnt;               /* no. of bands */
int     noise_cnt;              /* no. of noise categories */
int     off_chan_cnt;           /* no. of separations in 0.0125MHz increment */
int     subdist_cnt;            /* no. of sub-districts */

OFF_CHANNEL off_channel[MAX_SEP];
SUBDIST     subdist[MAX_SUBDIST];
float       min_signal[MAX_NOISE][MAX_BAND];  /* currently, the first 3 rows */
                                              /* store min. signal for high, */
                                              /* medium, and low noise areas */
                                              /* respectively; the 4th row   */
                                              /* stores the min. signal for  */
                                              /* mobiles.                    */
float       bw3db[] = {.01, .01, .01, .01};   /* 3dB bandwidth in MHz for    */
                                              /* LOWVHF, HIGHVHF, UHF and    */
                                              /* UHF800 respectively         */
float       loc_var[] = {6.0, 11.0, 11.0, 11.0};

double  desensit_cull_freq;
double  intermod2_cull_freq;
double  intermod3_cull_freq;
