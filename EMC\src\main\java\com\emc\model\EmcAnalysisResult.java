package com.emc.model;

import lombok.Data;
import java.util.List;

/**
 * Represents the result of an EMC analysis.
 * This is used as output for the REST API.
 */
@Data
public class EmcAnalysisResult {
    private String emcUid;
    private String printFile;
    private String auditFile;
    private String summaryFile;
    private String status;
    private String message;
    private List<String> warnings;
    private List<String> errors;
    
    // Summary statistics
    private int cochanTot;
    private int desenTot;
    private int intmod2VictTot;
    private int intmod2Tx1Tot;
    private int intmod2Tx2Tot;
    private int intmod3VictTot;
    private int intmod3Tx1Tot;
    private int intmod3Tx3Tot;
}
