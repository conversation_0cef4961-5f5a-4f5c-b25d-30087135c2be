package com.emc.service;

/**
 * Service for terrain-related operations.
 */
public interface TerrainService {
    
    /**
     * Gets the local terrain height at the specified grid coordinates.
     * Equivalent to the get_local_height function in the original C++ code.
     * 
     * @param gridEast The east grid coordinate
     * @param gridNorth The north grid coordinate
     * @return The local terrain height
     */
    float getLocalHeight(float gridEast, float gridNorth);
}
