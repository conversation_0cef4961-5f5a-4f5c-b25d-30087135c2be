/**********************************************************************/
/*                                                                    */
/*    Module Name   :  user_login (login.pc)                          */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  <PERSON><PERSON> <PERSON>. Mak                                      */
/*                                                                    */
/*    Callers       :  esemcn0f.c, esembe0f.pc, esemcs0f.pc,          */  
/*                     esemns0f.pc, esemsc0f.pc, esemvc0f.pc,         */  
/*                     esemct0x.pc                                    */  
/*                                                                    */
/*    Parameters    :  user id.                                       */
/*                  :  user password                                  */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  login ORACLE database using user id. and       */
/*                     password from parameter list.                  */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>

#ifndef TRUE
#define	TRUE     1
#endif

#ifndef FALSE
#define	FALSE    0
#endif

#ifndef FOUND
#define FOUND    0
#endif

#ifndef NOT_FOUND
#define  NOT_FOUND      1403
#endif

#ifndef LOGIN_OK
#define LOGIN_OK    0
#endif

#define  TRAIL          0
#define  LEADING        1

char msg[180];

EXEC SQL BEGIN DECLARE SECTION;
    char    o_dummy;
    char    o_user_type;
    VARCHAR o_sys_type[3];
    VARCHAR o_uid[20];
    VARCHAR o_pwd[20];
    VARCHAR o_level_2_uid[21];
    VARCHAR o_level_2_pwd[21];
EXEC SQL END DECLARE SECTION;
    
EXEC SQL INCLUDE SQLCA;




int strip_blank   (char *,char *);	/*20170614 Cyrus [Add] */





int user_login(p_uid, p_pwd, err_msg)				/*20170614 Cyrus [Add][int] */
char    *p_uid, *p_pwd;
char    *err_msg;
{

/*    strcpy(o_uid.arr, p_uid);
    strcpy(o_pwd.arr, p_pwd); */
    strcpy((char *)o_uid.arr, p_uid);
    strcpy((char *)o_pwd.arr, p_pwd);
    o_uid.len = strlen(p_uid);
    o_pwd.len = strlen(p_pwd);

    EXEC SQL CONNECT :o_uid IDENTIFIED BY :o_pwd;


/*
#ifdef DEBUG
    printf("user_login\n");
#endif
*/

    if (sqlca.sqlcode != 0)
        strcpy(err_msg, sqlca.sqlerrm.sqlerrmc);

    return(sqlca.sqlcode);
}


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  user_logout (login.pc)                         */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  esemcn0f.c, esembe0f.pc, esemcs0f.pc,          */  
/*                     esemns0f.pc, esemsc0f.pc, esemvc0f.pc,         */  
/*                     esemct0x.pc                                    */  
/*                                                                    */
/*    Parameters    :  user id.                                       */
/*                  :  user password                                  */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  logout from ORACLE database.                   */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

int user_logout()						/*20170614 Cyrus [Add][int ] */			
{

    EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn;


/*
#ifdef DEBUG
    printf("user_logout\n");
#endif
*/

    EXEC SQL COMMIT WORK RELEASE;

/* changed by Chen Yung */
/* original: */
/*    return; */
    return(0);


sqlerr_rtn:
    printf("%s\n", sqlca.sqlerrm.sqlerrmc);
    exit(1);

}


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  emc_login (login.pc)                           */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  esemcn0f.c                                     */  
/*                                                                    */
/*    Parameters    :  user id.                                       */
/*                  :  user password                                  */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  This is an EMC procedure to determine whether  */
/*                     the login user name is an authorised EMC user. */
/*                     if yes, determine the class to which the user  */
/*                     belongs.                                       */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/


int emc_login(p_uid, p_pwd, user_type, err_msg)
char    *p_uid, *p_pwd;
char    *user_type;
char    *err_msg;

{
    
    EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn;
    EXEC SQL WHENEVER NOT FOUND CONTINUE;

/*    strcpy(o_level_2_uid.arr, p_uid); */
    strcpy((char *)o_level_2_uid.arr, p_uid);
    o_level_2_uid.len = strlen(p_uid);

/*
system("echo \"login 1\" >> /tmp/debug");
sprintf(msg, "echo \"%s\" >> /tmp/debug", o_level_2_uid.arr );
system(msg);
*/
    EXEC SQL
         SELECT USER_TYPE, PASSWORD
         INTO   :o_user_type, :o_level_2_pwd
         FROM   EMC_AUTH
         WHERE  USER_ID = :o_level_2_uid;

    if (sqlca.sqlcode == NOT_FOUND)
    {
	
	


        sprintf(err_msg, "User not authorised to access EMC"  );
        return (NOT_FOUND);
    }

    o_level_2_pwd.arr[o_level_2_pwd.len] = '\0';
/*    if (strcmp(p_pwd, o_level_2_pwd.arr)) */
    if (strcmp(p_pwd, (char *)o_level_2_pwd.arr))
    {
        sprintf(err_msg, "Incorrect user password");
        return (NOT_FOUND);
    }

    *user_type = o_user_type;

    return(LOGIN_OK);

sqlerr_rtn:
    sprintf(err_msg, "%s", sqlca.sqlerrm.sqlerrmc);
    strip_blank(err_msg, TRAIL);
    return(sqlca.sqlcode);

/*
    strcpy(o_level_2_pwd.arr, p_pwd);
    o_level_2_pwd.len = strlen(p_pwd);

system("echo \"login 2\" >> /tmp/debug");
sprintf(msg, "echo \"%s %s\" >> /tmp/debug",
o_level_2_uid.arr, o_level_2_pwd.arr );
system(msg);

    EXEC SQL
         SELECT 'X'
         INTO   :o_dummy
         FROM   USER_PROFILE
         WHERE  USER_ID = :o_level_2_uid
         AND    PASSWD = :o_level_2_pwd;
        
    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "User not authorised to access EMC");
        return (sqlca.sqlcode);
    }

    strcpy(o_sys_type.arr, "EM");
    o_sys_type.len = strlen(o_sys_type.arr);

system("echo \"login 3\" >> /tmp/debug");
sprintf(msg, "echo \"%s %s\" >> /tmp/debug",
o_level_2_uid.arr, o_sys_type.arr
system(msg);

    EXEC SQL
         SELECT MAINT
         INTO   :o_maint
         FROM   USER_AUTH
         WHERE  USER_ID = :o_level_2_uid
         AND    SYS_TYPE = :o_sys_type;

    if (sqlca.sqlcode == NOT_FOUND)
    {
        sprintf(err_msg, "User not authorised to access EMC");
        return (sqlca.sqlcode);
    }

    if (o_maint == 'Y')
        *maint_flag = TRUE;
    else
        *maint_flag = FALSE;
*/
}
