-------------------------------------------------------------------------------
Test set: com.emc.service.impl.ReferenceServiceImplTest
-------------------------------------------------------------------------------
Tests run: 1, Failures: 1, Errors: 0, Skipped: 0, Time elapsed: 0.037 s <<< FAILURE! - in com.emc.service.impl.ReferenceServiceImplTest
testLoadReference  Time elapsed: 0.036 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <0> but was: <1>
	at com.emc.service.impl.ReferenceServiceImplTest.testLoadReference(ReferenceServiceImplTest.java:67)

