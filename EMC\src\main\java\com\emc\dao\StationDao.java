package com.emc.dao;

import com.emc.model.Station;

import java.util.List;
import java.util.Optional;

/**
 * DAO interface for Station operations.
 * This matches the original ProC STATION table queries.
 */
public interface StationDao extends BaseDao<Station, String> {
    
    /**
     * Finds stations within a geographic bounding box.
     * This matches the original ProC query for station selection.
     *
     * @param southGrid Southern boundary
     * @param northGrid Northern boundary
     * @param westGrid Western boundary
     * @param eastGrid Eastern boundary
     * @return List of stations within the bounding box
     */
    List<Station> findStationsInBoundingBox(int southGrid, int northGrid, int westGrid, int eastGrid);
    
    /**
     * Finds a station by system identification.
     *
     * @param sysCategory System category
     * @param sysType System type
     * @param sysNo System number
     * @param sysSuffix System suffix
     * @param baseNo Base number
     * @return Optional containing the station if found
     */
    Optional<Station> findBySystemId(char sysCategory, String sysType, String sysNo, String sysSuffix, int baseNo);
    
    /**
     * Finds stations by system type.
     *
     * @param sysType System type
     * @return List of stations with the specified system type
     */
    List<Station> findBySysType(String sysType);
    
    /**
     * Finds stations by sub-district.
     *
     * @param subDistrict Sub-district code
     * @return List of stations in the specified sub-district
     */
    List<Station> findBySubDistrict(String subDistrict);
}
