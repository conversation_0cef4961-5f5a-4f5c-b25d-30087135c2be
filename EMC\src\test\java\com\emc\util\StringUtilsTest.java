package com.emc.util;

import com.emc.constants.EmcConstants;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class StringUtilsTest {

    @Test
    void testStripBlank_TrailingBlanks() {
        // Setup
        StringBuilder str = new StringBuilder("Hello World   ");
        
        // Execute
        int count = StringUtils.stripBlank(str, EmcConstants.TRAIL);
        
        // Verify
        assertEquals(3, count);
        assertEquals("Hello World", str.toString());
    }

    @Test
    void testStripBlank_LeadingBlanks() {
        // Setup
        StringBuilder str = new StringBuilder("   Hello World");
        
        // Execute
        int count = StringUtils.stripBlank(str, EmcConstants.LEADING);
        
        // Verify
        assertEquals(3, count);
        assertEquals("Hello World", str.toString());
    }

    @Test
    void testStripBlank_NoTrailingBlanks() {
        // Setup
        StringBuilder str = new StringBuilder("Hello World");
        
        // Execute
        int count = StringUtils.stripBlank(str, EmcConstants.TRAIL);
        
        // Verify
        assertEquals(0, count);
        assertEquals("Hello World", str.toString());
    }

    @Test
    void testStripBlank_NoLeadingBlanks() {
        // Setup
        StringBuilder str = new StringBuilder("Hello World");
        
        // Execute
        int count = StringUtils.stripBlank(str, EmcConstants.LEADING);
        
        // Verify
        assertEquals(0, count);
        assertEquals("Hello World", str.toString());
    }

    @Test
    void testStripBlank_EmptyString() {
        // Setup
        StringBuilder str = new StringBuilder("");
        
        // Execute
        int count = StringUtils.stripBlank(str, EmcConstants.TRAIL);
        
        // Verify
        assertEquals(0, count);
        assertEquals("", str.toString());
    }

    @Test
    void testIsNumber_ValidNumber() {
        // Execute and Verify
        assertTrue(StringUtils.isNumber("123"));
        assertTrue(StringUtils.isNumber("123.45"));
        assertTrue(StringUtils.isNumber("-123"));
        assertTrue(StringUtils.isNumber("-123.45"));
        assertTrue(StringUtils.isNumber("0"));
        assertTrue(StringUtils.isNumber("0.0"));
    }

    @Test
    void testIsNumber_InvalidNumber() {
        // Execute and Verify
        assertFalse(StringUtils.isNumber("abc"));
        assertFalse(StringUtils.isNumber("123abc"));
        assertFalse(StringUtils.isNumber(""));
        assertFalse(StringUtils.isNumber(null));
    }
}
