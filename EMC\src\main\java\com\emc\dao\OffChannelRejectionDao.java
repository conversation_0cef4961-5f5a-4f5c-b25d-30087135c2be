package com.emc.dao;

import com.emc.model.OffChannelRejection;
import java.util.List;
import java.util.Optional;

/**
 * DAO interface for OffChannelRejection entity operations.
 */
public interface OffChannelRejectionDao extends BaseDao<OffChannelRejection, Long> {
    
    /**
     * Find off-channel rejection by channel separation.
     * 
     * @param channelSeparation The channel separation value
     * @return Optional containing the off-channel rejection if found
     */
    Optional<OffChannelRejection> findByChannelSeparation(float channelSeparation);
    
    /**
     * Find off-channel rejections with channel separation greater than or equal to the specified value.
     * 
     * @param channelSeparation The minimum channel separation value
     * @return List of off-channel rejections
     */
    List<OffChannelRejection> findByChannelSeparationGreaterThanEqual(float channelSeparation);
    
    /**
     * Find off-channel rejections with channel separation less than or equal to the specified value.
     * 
     * @param channelSeparation The maximum channel separation value
     * @return List of off-channel rejections
     */
    List<OffChannelRejection> findByChannelSeparationLessThanEqual(float channelSeparation);
    
    /**
     * Find all off-channel rejections ordered by channel separation.
     * 
     * @return List of all off-channel rejections ordered by channel separation
     */
    List<OffChannelRejection> findAllOrderByChannelSeparation();
}
