package com.emc.dao.mapper;

import com.emc.model.SubDistrict;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for SubDistrict entity.
 */
public class SubDistrictRowMapper implements RowMapper<SubDistrict> {
    
    @Override
    public SubDistrict mapRow(ResultSet rs, int rowNum) throws SQLException {
        SubDistrict subDistrict = new SubDistrict();
        subDistrict.setSubDistrictCode(rs.getString("SUBDISTRICT"));
        subDistrict.setNoiseCode(rs.getInt("NOISE_CODE"));
        subDistrict.setDistType(rs.getString("DISTRICT_TYPE"));
        subDistrict.setLowVhfReg(rs.getInt("LOW_VHF_CULL_REG"));
        subDistrict.setLowVhfHd(rs.getInt("LOW_VHF_CULL_HD"));
        subDistrict.setVhfReg(rs.getInt("VHF_CULL_REG"));
        subDistrict.setVhfHd(rs.getInt("VHF_CULL_HD"));
        subDistrict.setUhfReg(rs.getInt("UHF_CULL_REG"));
        subDistrict.setUhfHd(rs.getInt("UHF_CULL_HD"));
        subDistrict.setUhf800Reg(rs.getInt("UHF_800_CULL_REG"));
        subDistrict.setUhf800Hd(rs.getInt("UHF_800_CULL_HD"));
        return subDistrict;
    }
}
