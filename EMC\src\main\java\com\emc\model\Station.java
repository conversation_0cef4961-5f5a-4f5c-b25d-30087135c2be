package com.emc.model;

import lombok.Data;

/**
 * Represents a station record from the STATION table.
 * This matches the original ProC STATION table structure.
 */
@Data
public class Station {
    private char sysCategory;
    private String sysType;
    private String sysNo;
    private String sysSuffix;
    private int baseNo;
    private int east;
    private int north;
    private String licType;
    private String licNo;
    private char stationType;
    private String antType;
    private int halt; // antenna height
    private int azMaxRad;
    private char pwSign;
    private float pwDbw;
    private String subDistrict;
    private String cancelDate;
}
