
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,0,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[12];
};
static const struct sqlcxp sqlfpn =
{
    11,
    "esemtn0x.pc"
};


static unsigned int sqlctx = 150427;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[8];
   unsigned long  sqhstl[8];
            int   sqhsts[8];
            short *sqindv[8];
            int   sqinds[8];
   unsigned long  sqharm[8];
   unsigned long  *sqharc[8];
   unsigned short  sqadto[8];
   unsigned short  sqtdso[8];
} sqlstm = {13,8};

/* SQLLIB Prototypes */
extern void sqlcxt (void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlcx2t(void **, unsigned int *,
                    struct sqlexd *, const struct sqlcxp *);
extern void sqlbuft(void **, char *);
extern void sqlgs2t(void **, char *);
extern void sqlorat(void **, unsigned int *, void *);

/* Forms Interface */
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern void sqliem(unsigned char *, signed int *);

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4242,1,0,0,
5,0,0,1,72,0,4,206,0,0,3,2,0,1,0,2,3,0,0,1,3,0,0,1,3,0,0,
32,0,0,2,108,0,4,247,0,0,8,2,0,1,0,2,3,0,0,2,3,0,0,2,3,0,0,2,3,0,0,2,3,0,0,2,3,
0,0,1,3,0,0,1,3,0,0,
79,0,0,3,108,0,4,303,0,0,8,2,0,1,0,2,3,0,0,2,3,0,0,2,3,0,0,2,3,0,0,2,3,0,0,2,3,
0,0,1,3,0,0,1,3,0,0,
};


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  get_terrain_profile (esemtn0x.pc)              */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  get_diffract_loss (esemdl0x.c)                 */  
/*                                                                    */
/*    Parameters    :  grid of transmitting station                   */
/*                     grid of receiving station                      */
/*                                                                    */
/*    Called Modules:  cal_dist (utility.c)                           */
/*                     get_local_height (esemtn0x.pc)                 */
/*                                                                    */
/*    Purpose       :  Derive the intermediate points info. from      */
/*                     transmitting station to receiving station.     */
/*                     Info. is stored in the following arrays:       */
/*                         grid,                                      */
/*                         dist (distance from Tx to Rx station),     */
/*                         height                                     */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include "../include/emcext.h"
#include "../include/macros.h"
#include "../include/define.h"
#include "../include/east.h"
#include "../include/north.h"
#include "../include/trainext.h"
#include "../include/presuext.h"

#define  BASE_EAST    110.
#define  BASE_NORTH   465.
#define  STEP         15.24




int get_terrain_profile (float[],float[]);

float    cal_dist();
float    get_local_height();

extern char msg[];

int get_terrain_profile(grid_a, grid_b)
float  grid_a[], grid_b[];
{
    float  grid[2];
    float  grid_1[2], grid_2[2];
    float  east_dist, north_dist;
    float  east_inc, north_inc;
    int    i;


/*
#ifdef DEBUG
    printf("get_terrain_profile\n");
#endif
*/

    grid_1[0] = grid_a[0] / 10;
    grid_1[1] = grid_a[1] / 10;
    grid_2[0] = grid_b[0] / 10;
    grid_2[1] = grid_b[1] / 10;

    east_dist  = grid_2[0] - grid_1[0];
    north_dist = grid_2[1] - grid_1[1];

    n_points = (int)max(abs(east_dist)/STEP, abs(north_dist)/STEP);
    n_points = max(n_points, 1);

    east_inc  = east_dist / n_points;
    north_inc = north_dist / n_points;

    grid[0] = grid_1[0];
    grid[1] = grid_1[1];

/*
printf("n_points east_in north_inc: %d %f %f\n", n_points, east_inc, north_inc);
*/

    for (i = 0; i < n_points - 1; i++)
    {

        grid[0]   = grid[0] + east_inc;
        grid[1]   = grid[1] + north_inc;
        dist[i]   = cal_dist(grid, grid_1) * 1000;
        height[i] = get_local_height(grid[0], grid[1]);

/*
system("echo \"esemtn0x height\" >> /tmp/debug");
sprintf(msg, "echo \"%f %f\" >> /tmp/debug", height[i], dist[i]);
system(msg);
*/

/*
printf("height dist: %f %f\n", height[i], dist[i]);
*/
    }

	
return(0);


}


/**********************************************************************/
/*                                                                    */
/*    Module Name   :  get_local_height (esemtn0x.pc)                 */
/*    Date Written  :  April, 1993                                    */
/*    Author        :  C. H. Mak                                      */
/*                                                                    */
/*    Callers       :  main (esemba0x.c)                              */  
/*                     select_station (esemss0x.pc)                   */  
/*                     get_terrain_profile (esemtn0x.pc)              */  
/*                                                                    */
/*    Parameters    :  grid_east                                      */
/*                     grid_north                                     */
/*                                                                    */
/*    Called Modules:                                                 */
/*                                                                    */
/*    Purpose       :  Determine the terrain local height of the      */
/*                     given point by either getting the height       */
/*                     from FINES or by interpolation from points     */
/*                     obtained from TERRAIN.                         */
/*                     Note that all calculations still assume        */
/*                     4-digit grid point (ie., east-2300 is treated  */
/*                     as east-230). Only when referring to TERRAIN   */
/*                     and FINES that grid is treated as 5 digits     */
/*                                                                    */
/*--------------------------------------------------------------------*/
/*                    Modification   History                          */
/*--------------------------------------------------------------------*/
/*                                                                    */
/*   C. H. Mak      Apr-93      Initial version                       */
/**********************************************************************/

float  get_local_height(grid_east, grid_north)
float  grid_east, grid_north;
{
    float  loc_height;
    float  east_ref, north_ref;
    float  hp1, hp2;
    float  x, y;
    int    i;
    int    fine_terrain_required = 0;

    /* EXEC SQL BEGIN DECLARE SECTION; */ 


    int    o_grid_east;
    int    o_grid_north;
    int    o_loc_height;
    int    o_grid_east_r;
    int    o_grid_north_r;
    int    o_h1; 
    int    o_h2;
    int    o_h3;
    int    o_h4;

    /* EXEC SQL END DECLARE SECTION; */ 


/*     EXEC SQL INCLUDE SQLCA;
 */ 
/*
 * $Header: sqlca.h 24-apr-2003.12:50:58 mkandarp Exp $ sqlca.h 
 */

/* Copyright (c) 1985, 2003, Oracle Corporation.  All rights reserved.  */
 
/*
NAME
  SQLCA : SQL Communications Area.
FUNCTION
  Contains no code. Oracle fills in the SQLCA with status info
  during the execution of a SQL stmt.
NOTES
  **************************************************************
  ***                                                        ***
  *** This file is SOSD.  Porters must change the data types ***
  *** appropriately on their platform.  See notes/pcport.doc ***
  *** for more information.                                  ***
  ***                                                        ***
  **************************************************************

  If the symbol SQLCA_STORAGE_CLASS is defined, then the SQLCA
  will be defined to have this storage class. For example:
 
    #define SQLCA_STORAGE_CLASS extern
 
  will define the SQLCA as an extern.
 
  If the symbol SQLCA_INIT is defined, then the SQLCA will be
  statically initialized. Although this is not necessary in order
  to use the SQLCA, it is a good pgming practice not to have
  unitialized variables. However, some C compilers/OS's don't
  allow automatic variables to be init'd in this manner. Therefore,
  if you are INCLUDE'ing the SQLCA in a place where it would be
  an automatic AND your C compiler/OS doesn't allow this style
  of initialization, then SQLCA_INIT should be left undefined --
  all others can define SQLCA_INIT if they wish.

  If the symbol SQLCA_NONE is defined, then the SQLCA variable will
  not be defined at all.  The symbol SQLCA_NONE should not be defined
  in source modules that have embedded SQL.  However, source modules
  that have no embedded SQL, but need to manipulate a sqlca struct
  passed in as a parameter, can set the SQLCA_NONE symbol to avoid
  creation of an extraneous sqlca variable.
 
MODIFIED
    lvbcheng   07/31/98 -  long to int
    jbasu      12/12/94 -  Bug 217878: note this is an SOSD file
    losborne   08/11/92 -  No sqlca var if SQLCA_NONE macro set 
  Clare      12/06/84 - Ch SQLCA to not be an extern.
  Clare      10/21/85 - Add initialization.
  Bradbury   01/05/86 - Only initialize when SQLCA_INIT set
  Clare      06/12/86 - Add SQLCA_STORAGE_CLASS option.
*/
 
#ifndef SQLCA
#define SQLCA 1
 
struct   sqlca
         {
         /* ub1 */ char    sqlcaid[8];
         /* b4  */ int     sqlabc;
         /* b4  */ int     sqlcode;
         struct
           {
           /* ub2 */ unsigned short sqlerrml;
           /* ub1 */ char           sqlerrmc[70];
           } sqlerrm;
         /* ub1 */ char    sqlerrp[8];
         /* b4  */ int     sqlerrd[6];
         /* ub1 */ char    sqlwarn[8];
         /* ub1 */ char    sqlext[8];
         };

#ifndef SQLCA_NONE 
#ifdef   SQLCA_STORAGE_CLASS
SQLCA_STORAGE_CLASS struct sqlca sqlca
#else
         struct sqlca sqlca
#endif
 
#ifdef  SQLCA_INIT
         = {
         {'S', 'Q', 'L', 'C', 'A', ' ', ' ', ' '},
         sizeof(struct sqlca),
         0,
         { 0, {0}},
         {'N', 'O', 'T', ' ', 'S', 'E', 'T', ' '},
         {0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0},
         {0, 0, 0, 0, 0, 0, 0, 0}
         }
#endif
         ;
#endif
 
#endif
 
/* end SQLCA */



    /* EXEC SQL WHENEVER SQLERROR GO TO sqlerr_rtn; */ 

    /* EXEC SQL WHENEVER NOT FOUND CONTINUE; */ 


#ifdef DEBUG
    printf("get_local_height: %f %f\n", grid_east, grid_north);
#endif

    /**********************************************************************/
    /*  check whether the given point is within the fine terrain area     */
    /*  if yes, use FINES to obtain local terrain height                  */
    /**********************************************************************/

    for (i = 0; i < fine_terrain_cnt; i++)
        if ((grid_east > (float)fine_terrain_east[i] - 7)
        &&  (grid_east < (float)fine_terrain_east[i] + 7)
        &&  (grid_north > (float)fine_terrain_north[i] - 7)
        &&  (grid_north < (float)fine_terrain_north[i] + 7))
        {
            fine_terrain_required = 1;
            break;
        }

    if (fine_terrain_required)
    {
       o_grid_east  = (int)(grid_east - 1);
       o_grid_north = (int)(grid_north - 1); 
       o_grid_east  = o_grid_east * 10;
       o_grid_north = o_grid_north * 10;

/*
system("echo \"esemtn0x 1\" >> /tmp/debug");
sprintf(msg, "echo \"%d %d\" >> /tmp/debug", o_grid_north, o_grid_east);
system(msg);
*/
       /* EXEC SQL
            SELECT DISTINCT H1 
            into   :o_loc_height
            FROM   FINES
            WHERE  (NORTH >= :o_grid_north)
            AND    (EAST >= :o_grid_east); */ 

{
       struct sqlexd sqlstm;
       sqlstm.sqlvsn = 13;
       sqlstm.arrsiz = 3;
       sqlstm.sqladtp = &sqladt;
       sqlstm.sqltdsp = &sqltds;
       sqlstm.stmt = "select distinct H1 into :b0  from FINES where (NORTH>=\
:b1 and EAST>=:b2)";
       sqlstm.iters = (unsigned int  )1;
       sqlstm.offset = (unsigned int  )5;
       sqlstm.selerr = (unsigned short)1;
       sqlstm.sqlpfmem = (unsigned int  )0;
       sqlstm.cud = sqlcud0;
       sqlstm.sqlest = (unsigned char  *)&sqlca;
       sqlstm.sqlety = (unsigned short)4352;
       sqlstm.occurs = (unsigned int  )0;
       sqlstm.sqhstv[0] = (unsigned char  *)&o_loc_height;
       sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
       sqlstm.sqhsts[0] = (         int  )0;
       sqlstm.sqindv[0] = (         short *)0;
       sqlstm.sqinds[0] = (         int  )0;
       sqlstm.sqharm[0] = (unsigned long )0;
       sqlstm.sqadto[0] = (unsigned short )0;
       sqlstm.sqtdso[0] = (unsigned short )0;
       sqlstm.sqhstv[1] = (unsigned char  *)&o_grid_north;
       sqlstm.sqhstl[1] = (unsigned long )sizeof(int);
       sqlstm.sqhsts[1] = (         int  )0;
       sqlstm.sqindv[1] = (         short *)0;
       sqlstm.sqinds[1] = (         int  )0;
       sqlstm.sqharm[1] = (unsigned long )0;
       sqlstm.sqadto[1] = (unsigned short )0;
       sqlstm.sqtdso[1] = (unsigned short )0;
       sqlstm.sqhstv[2] = (unsigned char  *)&o_grid_east;
       sqlstm.sqhstl[2] = (unsigned long )sizeof(int);
       sqlstm.sqhsts[2] = (         int  )0;
       sqlstm.sqindv[2] = (         short *)0;
       sqlstm.sqinds[2] = (         int  )0;
       sqlstm.sqharm[2] = (unsigned long )0;
       sqlstm.sqadto[2] = (unsigned short )0;
       sqlstm.sqtdso[2] = (unsigned short )0;
       sqlstm.sqphsv = sqlstm.sqhstv;
       sqlstm.sqphsl = sqlstm.sqhstl;
       sqlstm.sqphss = sqlstm.sqhsts;
       sqlstm.sqpind = sqlstm.sqindv;
       sqlstm.sqpins = sqlstm.sqinds;
       sqlstm.sqparm = sqlstm.sqharm;
       sqlstm.sqparc = sqlstm.sqharc;
       sqlstm.sqpadto = sqlstm.sqadto;
       sqlstm.sqptdso = sqlstm.sqtdso;
       sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
       if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}


/*
            WHERE  (NORTH*100000 + EAST) >= 
                   (:o_grid_north*100000 + :o_grid_east);
*/

       if (sqlca.sqlcode == NOT_FOUND)
       {
           printf("No FINES record matched for east/north grid: %d/%d\n",
                  o_grid_east, o_grid_north);
           exit(1);
       }

/*
       printf("fine_terrain: %f\n", o_loc_height);
*/
       return((float)o_loc_height);
    }


    /**********************************************************************/
    /*  else, use interpolation and obtain local terrain height from      */
    /*  TERRAIN                                                           */
    /**********************************************************************/
    east_ref  = BASE_EAST + STEP * (1.+(int)((grid_east-BASE_EAST)/STEP));
    north_ref = BASE_NORTH + STEP * (1.+(int)((grid_north-BASE_NORTH)/STEP));

    o_grid_east  = (int)(east_ref + 0.5) * 10;
    o_grid_north = (int)(north_ref + 0.5) * 10;

/*
system("echo \"esemtn0x 2\" >> /tmp/debug");
sprintf(msg, "echo \"%d %d\" >> /tmp/debug", o_grid_north, o_grid_east);
system(msg);
*/

    /* EXEC SQL
         SELECT EAST, NORTH, H1, H2, H3, H4
         INTO   :o_grid_east_r, :o_grid_north_r,
                :o_h1, :o_h2, :o_h3, :o_h4
         FROM   TERRAIN
         WHERE  NORTH = :o_grid_north
         AND    EAST  = :o_grid_east; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 8;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "select EAST ,NORTH ,H1 ,H2 ,H3 ,H4 into :b0,:b1,:b2,:b3,:\
b4,:b5  from TERRAIN where (NORTH=:b6 and EAST=:b7)";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )32;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&o_grid_east_r;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&o_grid_north_r;
    sqlstm.sqhstl[1] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&o_h1;
    sqlstm.sqhstl[2] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)&o_h2;
    sqlstm.sqhstl[3] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqhstv[4] = (unsigned char  *)&o_h3;
    sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[4] = (         int  )0;
    sqlstm.sqindv[4] = (         short *)0;
    sqlstm.sqinds[4] = (         int  )0;
    sqlstm.sqharm[4] = (unsigned long )0;
    sqlstm.sqadto[4] = (unsigned short )0;
    sqlstm.sqtdso[4] = (unsigned short )0;
    sqlstm.sqhstv[5] = (unsigned char  *)&o_h4;
    sqlstm.sqhstl[5] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[5] = (         int  )0;
    sqlstm.sqindv[5] = (         short *)0;
    sqlstm.sqinds[5] = (         int  )0;
    sqlstm.sqharm[5] = (unsigned long )0;
    sqlstm.sqadto[5] = (unsigned short )0;
    sqlstm.sqtdso[5] = (unsigned short )0;
    sqlstm.sqhstv[6] = (unsigned char  *)&o_grid_north;
    sqlstm.sqhstl[6] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[6] = (         int  )0;
    sqlstm.sqindv[6] = (         short *)0;
    sqlstm.sqinds[6] = (         int  )0;
    sqlstm.sqharm[6] = (unsigned long )0;
    sqlstm.sqadto[6] = (unsigned short )0;
    sqlstm.sqtdso[6] = (unsigned short )0;
    sqlstm.sqhstv[7] = (unsigned char  *)&o_grid_east;
    sqlstm.sqhstl[7] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[7] = (         int  )0;
    sqlstm.sqindv[7] = (         short *)0;
    sqlstm.sqinds[7] = (         int  )0;
    sqlstm.sqharm[7] = (unsigned long )0;
    sqlstm.sqadto[7] = (unsigned short )0;
    sqlstm.sqtdso[7] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
    if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



    /**********************************************************************/
    /*  if no TERRAIN record matched, use ieast[] and inorth[] and search */
    /*  TERRAIN again                                                     */
    /**********************************************************************/
    if (sqlca.sqlcode == NOT_FOUND)
    {
        o_grid_east  = (int)grid_east;
        o_grid_north = (int)grid_north;
/*
        for (i = 0; i < 390; i++)
            if (ieast[i] >= o_grid_east) break;
        if (i >= 390)
*/
        for (i = 0; ieast[i] != DUMMY; i++)
            if (ieast[i] >= o_grid_east)
                break;
        if (ieast[i] == DUMMY)
        {
            fprintf(afp, "No TERRAIN in (ieast) for east/north grid: %d/%d\n",
                   o_grid_east, o_grid_north);
            fflush(afp);
            exit(1);
        }
        o_grid_east = ieast[i] * 10;

/*
        for (i = 0; i < 290; i++)
            if (inorth[i] >= o_grid_north) break;
        if (i >= 290)
*/
        for (i = 0; inorth[i] != DUMMY; i++)
            if (inorth[i] >= o_grid_north) 
                break;
        if (inorth[i] == DUMMY)
        {
            fprintf(afp, "No TERRAIN in (inorth) for east/north grid: %d/%d\n",
                   o_grid_east, o_grid_north);
            fflush(afp);
            exit(1);
        }
        o_grid_north = inorth[i] * 10;

/*
system("echo \"esemtn0x 3\" >> /tmp/debug");
sprintf(msg, "echo \"%d %d\" >> /tmp/debug", o_grid_north, o_grid_east);
system(msg);
*/

        /* EXEC SQL
             SELECT EAST, NORTH, H1, H2, H3, H4
             INTO   :o_grid_east_r, :o_grid_north_r,
                    :o_h1, :o_h2, :o_h3, :o_h4
             FROM   TERRAIN
             WHERE  NORTH = :o_grid_north
             AND    EAST  = :o_grid_east; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 8;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.stmt = "select EAST ,NORTH ,H1 ,H2 ,H3 ,H4 into :b0,:b1,:b2,:\
b3,:b4,:b5  from TERRAIN where (NORTH=:b6 and EAST=:b7)";
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )79;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqhstv[0] = (unsigned char  *)&o_grid_east_r;
        sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[0] = (         int  )0;
        sqlstm.sqindv[0] = (         short *)0;
        sqlstm.sqinds[0] = (         int  )0;
        sqlstm.sqharm[0] = (unsigned long )0;
        sqlstm.sqadto[0] = (unsigned short )0;
        sqlstm.sqtdso[0] = (unsigned short )0;
        sqlstm.sqhstv[1] = (unsigned char  *)&o_grid_north_r;
        sqlstm.sqhstl[1] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[1] = (         int  )0;
        sqlstm.sqindv[1] = (         short *)0;
        sqlstm.sqinds[1] = (         int  )0;
        sqlstm.sqharm[1] = (unsigned long )0;
        sqlstm.sqadto[1] = (unsigned short )0;
        sqlstm.sqtdso[1] = (unsigned short )0;
        sqlstm.sqhstv[2] = (unsigned char  *)&o_h1;
        sqlstm.sqhstl[2] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[2] = (         int  )0;
        sqlstm.sqindv[2] = (         short *)0;
        sqlstm.sqinds[2] = (         int  )0;
        sqlstm.sqharm[2] = (unsigned long )0;
        sqlstm.sqadto[2] = (unsigned short )0;
        sqlstm.sqtdso[2] = (unsigned short )0;
        sqlstm.sqhstv[3] = (unsigned char  *)&o_h2;
        sqlstm.sqhstl[3] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[3] = (         int  )0;
        sqlstm.sqindv[3] = (         short *)0;
        sqlstm.sqinds[3] = (         int  )0;
        sqlstm.sqharm[3] = (unsigned long )0;
        sqlstm.sqadto[3] = (unsigned short )0;
        sqlstm.sqtdso[3] = (unsigned short )0;
        sqlstm.sqhstv[4] = (unsigned char  *)&o_h3;
        sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[4] = (         int  )0;
        sqlstm.sqindv[4] = (         short *)0;
        sqlstm.sqinds[4] = (         int  )0;
        sqlstm.sqharm[4] = (unsigned long )0;
        sqlstm.sqadto[4] = (unsigned short )0;
        sqlstm.sqtdso[4] = (unsigned short )0;
        sqlstm.sqhstv[5] = (unsigned char  *)&o_h4;
        sqlstm.sqhstl[5] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[5] = (         int  )0;
        sqlstm.sqindv[5] = (         short *)0;
        sqlstm.sqinds[5] = (         int  )0;
        sqlstm.sqharm[5] = (unsigned long )0;
        sqlstm.sqadto[5] = (unsigned short )0;
        sqlstm.sqtdso[5] = (unsigned short )0;
        sqlstm.sqhstv[6] = (unsigned char  *)&o_grid_north;
        sqlstm.sqhstl[6] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[6] = (         int  )0;
        sqlstm.sqindv[6] = (         short *)0;
        sqlstm.sqinds[6] = (         int  )0;
        sqlstm.sqharm[6] = (unsigned long )0;
        sqlstm.sqadto[6] = (unsigned short )0;
        sqlstm.sqtdso[6] = (unsigned short )0;
        sqlstm.sqhstv[7] = (unsigned char  *)&o_grid_east;
        sqlstm.sqhstl[7] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[7] = (         int  )0;
        sqlstm.sqindv[7] = (         short *)0;
        sqlstm.sqinds[7] = (         int  )0;
        sqlstm.sqharm[7] = (unsigned long )0;
        sqlstm.sqadto[7] = (unsigned short )0;
        sqlstm.sqtdso[7] = (unsigned short )0;
        sqlstm.sqphsv = sqlstm.sqhstv;
        sqlstm.sqphsl = sqlstm.sqhstl;
        sqlstm.sqphss = sqlstm.sqhsts;
        sqlstm.sqpind = sqlstm.sqindv;
        sqlstm.sqpins = sqlstm.sqinds;
        sqlstm.sqparm = sqlstm.sqharm;
        sqlstm.sqparc = sqlstm.sqharc;
        sqlstm.sqpadto = sqlstm.sqadto;
        sqlstm.sqptdso = sqlstm.sqtdso;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
        if (sqlca.sqlcode < 0) goto sqlerr_rtn;
}



       if (sqlca.sqlcode == NOT_FOUND)
       {
           fprintf(afp, "No TERRAIN record for east/north grid: %d/%d\n",
                  o_grid_east, o_grid_north);
           fflush(afp);
           exit(1);
       }
    }

    o_grid_east_r  = o_grid_east_r / 10;
    o_grid_north_r = o_grid_north_r / 10;

    y = (o_grid_north_r - grid_north) / STEP;
    x = (o_grid_east_r - grid_east) / STEP;

    hp1 = o_h1 + (o_h2 - o_h1) * y;
    hp2 = o_h4 + (o_h3 - o_h4) * y;

/*
    printf("east_ref north_ref o_grid_east o_grid_north: %f %f %d %d\n", 
           east_ref,north_ref,o_grid_east_r,o_grid_north_r);
    printf("o_h1 o_h2 o_h3 o_h4 y x : %d %d %d %d %f %f\n",
           o_h1,o_h2,o_h3,o_h4,y,x);
    printf("hp1 hp2 (hp1 + (hp2 - hp1) * x): %f %f %f\n",
    hp1, hp2, (hp1 + (hp2 - hp1) * x));
*/

    return(hp1 + (hp2 - hp1) * x);


sqlerr_rtn:
    printf("%s\n", sqlca.sqlerrm.sqlerrmc);
    exit(1);

}
